# Tavily API Key Management System 🔑

## Overview

This system provides automated testing and management of Tavily API keys to ensure reliable search functionality. It automatically identifies quota exceeded and invalid keys, and can remove them from the rotation system.

## 🚀 Quick Start

### Test All API Keys
```bash
npm run test:tavily
```

### Update Keys (Interactive)
```bash
npm run update:tavily
```

### Update Keys (Automatic)
```bash
npm run update:tavily:auto
```

## 📋 Available Scripts

| Script | Command | Description |
|--------|---------|-------------|
| **Test Only** | `npm run test:tavily` | Tests all API keys and generates a report |
| **Interactive Update** | `npm run update:tavily` | Tests keys and prompts before removing bad ones |
| **Automatic Update** | `npm run update:tavily:auto` | Tests and automatically removes bad keys |

## 🔍 Test Script Features

### Key Testing (`test-tavily-api-keys.mjs`)

**What it does:**
- Tests all available Tavily API keys with minimal quota usage
- Categorizes keys by status: <PERSON><PERSON>, <PERSON>uo<PERSON> Exceeded, Invalid, Network Errors
- Generates comprehensive reports
- Saves detailed results to JSON files

**Key Categories:**
- ✅ **Valid Keys**: Working properly, available for use
- ❌ **Quota Exceeded**: Hit their usage limit, need removal
- 🚫 **Invalid Keys**: Unauthorized/forbidden, need removal  
- 🌐 **Network Errors**: Connection issues, may be temporary

### Sample Output
```
🚀 Starting Tavily API Key Testing
📊 Testing 10 API keys...

🔍 Testing Key 1 (...gSay)
  ✅ Key 1 (...gSay): SUCCESS - 1 results

🔍 Testing Key 2 (...9Vq)
  ❌ Key 2 (...9Vq): QUOTA EXCEEDED - Usage limit exceeded

============================================================
📊 TAVILY API KEY TEST REPORT
============================================================

📈 SUMMARY:
  ✅ Valid Keys:           7
  ❌ Quota Exceeded:       2
  🚫 Invalid Keys:         1
  🌐 Network Errors:       0
  📊 Total Tested:         10

🎯 RECOMMENDATIONS:
  1. Remove 3 non-working keys from the rotation system
  2. ✅ Service can continue with 7 valid keys
```

## 🔧 Update Script Features

### Automatic Key Removal (`update-tavily-keys.mjs`)

**What it does:**
1. **Tests** all API keys automatically
2. **Identifies** quota exceeded and invalid keys
3. **Creates backups** of original files
4. **Updates** `src/lib/search.ts` to remove bad keys
5. **Validates** the updated file structure

**Safety Features:**
- ✅ **Automatic backups** before any changes
- ✅ **File validation** after updates  
- ✅ **Interactive confirmation** (unless `--auto` flag used)
- ✅ **Detailed logging** of all operations

### Sample Update Output
```
🚀 Starting Tavily API Key Update Process

📊 Step 1: Testing all API keys...
[... test results ...]

🔧 Step 3: Ready to remove 2 non-working keys:
  1. ...9Vq (QUOTA EXCEEDED)
  2. ...3a5 (INVALID KEY)

🔄 Step 4: Updating search.ts...
📁 Backup created: backups/search.ts.backup.2025-01-27T10-30-00-000Z
🔧 Removing 2 keys from search.ts...
  1. Removing key ending in ...9Vq
  2. Removing key ending in ...3a5
✅ Successfully updated search.ts

✅ Step 5: Validating updated file...
✅ Validation passed: 8 keys remain in fallbackKeys array

🎉 Update completed successfully!
📊 Remaining valid keys: 8
```

## 📊 Generated Files

### Test Results JSON
Every test run creates a detailed JSON report:
- **Filename**: `tavily-test-results-[timestamp].json`
- **Contains**: Full test results, recommendations, key categorization
- **Use**: For detailed analysis, debugging, audit trails

### Backup Files
Every update creates a backup:
- **Location**: `backups/search.ts.backup.[timestamp]`
- **Contains**: Original file before modifications
- **Use**: Quick restoration if needed

## 🔑 API Key Structure

The system manages keys in `src/lib/search.ts`:

```typescript
const fallbackKeys = [
  '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',  // Active
  '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',  // Active  
  '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',  // To be removed
  // ... more keys
];
```

## 📈 Key Rotation Logic

1. **Primary**: Environment variable `TAVILY_API_KEY`
2. **Fallback**: Hardcoded keys in `fallbackKeys` array
3. **Rotation**: Automatic switching on errors
4. **Health Tracking**: Error counting and quota monitoring

## ⚠️ Important Considerations

### Before Running Updates
1. **Test First**: Always run `npm run test:tavily` to see what will be changed
2. **Review Results**: Check which keys will be removed
3. **Backup Awareness**: Updates create automatic backups
4. **Service Impact**: Ensure enough valid keys remain (recommended: 3+)

### Key Quota Limits
- **Tavily Free Tier**: Typically 1000 searches per month
- **Dev Keys**: Usually have lower limits
- **Production Keys**: Higher limits but still finite

### Network Issues
- **Temporary Failures**: May be connectivity issues, not key problems
- **Re-test**: Run tests multiple times if network errors occur
- **Manual Review**: Check network error keys manually before removal

## 🛠️ Manual Operations

### View Current Keys
```bash
grep -A 20 "fallbackKeys" src/lib/search.ts
```

### Restore from Backup
```bash
cp backups/search.ts.backup.[timestamp] src/lib/search.ts
```

### Add New Keys
Edit `src/lib/search.ts` and add to the `fallbackKeys` array:
```typescript
const fallbackKeys = [
  // ... existing keys
  'tvly-dev-YOUR-NEW-KEY-HERE',
];
```

## 🔄 Regular Maintenance

### Recommended Schedule
- **Weekly**: Run `npm run test:tavily` to monitor key health
- **Monthly**: Run `npm run update:tavily:auto` to clean up bad keys
- **As Needed**: After adding new keys or noticing search failures

### Monitoring Key Health
1. **Watch Logs**: Monitor search failure patterns
2. **Track Usage**: Keep eye on quota consumption  
3. **Test Regularly**: Don't wait for failures to discover issues

## 🚨 Troubleshooting

### All Keys Failing
```bash
# Check network connectivity
curl -X POST https://api.tavily.com/search

# Test with minimal request
npm run test:tavily
```

### Script Errors
```bash
# Check Node.js version (requires Node 14+)
node --version

# Verify file permissions
ls -la scripts/test-tavily-api-keys.mjs
```

### File Update Issues
```bash
# Restore from backup
cp backups/search.ts.backup.[latest] src/lib/search.ts

# Check file syntax
node -c src/lib/search.ts
```

## 📞 Support

If you encounter issues:
1. **Check Logs**: Review console output for specific errors
2. **Test Results**: Examine the generated JSON files
3. **Backup Files**: Use backups to restore working state
4. **Manual Review**: Check `src/lib/search.ts` for syntax issues

---

## 🎯 Best Practices

1. **Test Before Update**: Always review what will be changed
2. **Keep Backups**: Don't delete backup files immediately  
3. **Monitor Key Count**: Maintain at least 3 valid keys
4. **Regular Maintenance**: Schedule periodic key health checks
5. **Add New Keys**: Proactively add keys before current ones expire 