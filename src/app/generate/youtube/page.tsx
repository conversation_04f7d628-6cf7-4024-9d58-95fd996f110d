'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Video, Zap, ArrowLeft, Copy, Download, Sparkles, Clock, Globe, Eye, PlayCircle, Search, Brain } from 'lucide-react'
import Link from 'next/link'
import ReactMarkdown from 'react-markdown'
import YouTubeScriptDisplay from '@/components/YouTubeScriptDisplay'

export default function YouTubeGeneratorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [formData, setFormData] = useState({
    title: '',
    brief: '',
    duration: '5-10 minutes',
    style: 'educational',
    targetAudience: 'general audience',
    useAdvancedResearch: true,
    videoUrls: ''
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')
  const [progressId, setProgressId] = useState<string | null>(null)
  const [progress, setProgress] = useState(0)
  const [progressMessage, setProgressMessage] = useState('')
  const [metadata, setMetadata] = useState<any>(null)
  const [wordCount, setWordCount] = useState<number>(0)
  const [currentPhase, setCurrentPhase] = useState(0)
  const [researchData, setResearchData] = useState<any>(null)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Update current phase based on progress
  useEffect(() => {
    if (progress <= 20) setCurrentPhase(0)
    else if (progress <= 60) setCurrentPhase(1)
    else setCurrentPhase(2)
  }, [progress])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-red-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsGenerating(true)
    setProgress(0)
    setProgressMessage('Starting script generation...')
    setMetadata(null)
    setResearchData(null)
    setCurrentPhase(0)
    
    try {
      const response = await fetch('/api/generate/youtube', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      const data = await response.json()
      if (data.success) {
        setGeneratedContent(data.content)
        setProgressId(data.progressId)
        setMetadata(data.metadata)
        setResearchData(data.research)
        setWordCount(data.wordCount || data.content.split(/\s+/).filter((word: string) => word.length > 0).length)
        
        // Simulate progress updates
        const phases = [
          { message: 'Searching web and scraping research content...', progress: 15 },
          { message: 'Finding YouTube videos and extracting captions...', progress: 35 },
          { message: 'Generating script with comprehensive analysis...', progress: 60 },
          { message: 'Finalizing research-backed script...', progress: 90 },
          { message: 'Script generation complete!', progress: 100 }
        ]
        
        for (let i = 0; i < phases.length; i++) {
          setTimeout(() => {
            setProgress(phases[i].progress)
            setProgressMessage(phases[i].message)
          }, i * 1500)
        }
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      alert('Failed to generate YouTube script')
    } finally {
      setTimeout(() => setIsGenerating(false), 8000)
    }
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedContent)
  }

  const downloadAsText = () => {
    const blob = new Blob([generatedContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `youtube-script-${formData.title.replace(/\s+/g, '-').toLowerCase()}.txt`
    a.click()
  }

  const workflowPhases = [
    {
      id: 0,
      title: 'Web Research',
      icon: <Globe className="w-6 h-6" />,
      description: 'Search & scrape content from 10 sources',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 1, 
      title: 'YouTube Analysis',
      icon: <Video className="w-6 h-6" />,
      description: 'Find top 5 videos & extract captions',
      color: 'from-red-500 to-orange-500'
    },
    {
      id: 2,
      title: 'Script Generation', 
      icon: <Brain className="w-6 h-6" />,
      description: 'AI follows your instructions exactly',
      color: 'from-purple-500 to-pink-500'
    }
  ]

  return (
    <div className="min-h-screen bg-[#0f0f0f]">
      {/* Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-red-950/30 via-[#0f0f0f] to-red-900/20" />
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/3 left-1/4 w-[600px] h-[400px] bg-gradient-to-r from-red-500/20 to-transparent rounded-full blur-[120px]"
        />
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="border-b border-[#272727] bg-[#0f0f0f]/95 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 rounded-full hover:bg-[#272727] transition-all duration-200"
                  >
                    <ArrowLeft className="w-6 h-6 text-white" />
                  </motion.button>
                </Link>
                <div className="flex items-center space-x-4">
                  <motion.div 
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    className="relative"
                  >
                    <div className="w-10 h-10 bg-[#ff0000] rounded-lg flex items-center justify-center shadow-lg">
                      <Video className="w-6 h-6 text-white" />
                    </div>
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-[#0f0f0f] animate-pulse"></div>
                  </motion.div>
                  <div>
                    <h1 className="text-2xl font-bold text-white flex items-center gap-2">
                      Smart Script Generator
                      <span className="text-xs bg-[#ff0000] text-white px-2 py-1 rounded-full font-medium">V2.0</span>
                    </h1>
                    <p className="text-[#aaa] text-sm">Research-Powered YouTube Scripts</p>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <motion.div 
                  whileHover={{ scale: 1.05 }}
                  className="hidden md:flex items-center space-x-2 bg-[#272727] rounded-full px-4 py-2"
                >
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-white text-sm font-medium">Gemini 2.5 Flash</span>
                </motion.div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto px-6 py-8">
          <div className="space-y-8">
            
            {/* Generation Form */}
            <div className="space-y-6">
              
              {/* Workflow Overview */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-[#181818] border border-[#303030] rounded-2xl p-6"
              >
                <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                  <Sparkles className="w-5 h-5 text-yellow-400 mr-2" />
                  Streamlined Workflow
                </h3>
                <div className="grid grid-cols-3 gap-4">
                  {workflowPhases.map((phase, index) => (
                    <motion.div
                      key={phase.id}
                      className={`relative p-4 rounded-xl border-2 transition-all duration-300 ${
                        currentPhase >= phase.id && isGenerating
                          ? 'border-white bg-gradient-to-br ' + phase.color + ' shadow-lg'
                          : 'border-[#404040] bg-[#272727]'
                      }`}
                      whileHover={{ scale: 1.02 }}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className={`p-2 rounded-lg ${
                          currentPhase >= phase.id && isGenerating
                            ? 'bg-white/20'
                            : 'bg-[#404040]'
                        }`}>
                          {phase.icon}
                        </div>
                        {currentPhase >= phase.id && isGenerating && (
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                            className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
                          />
                        )}
                      </div>
                      <h4 className="font-semibold text-white text-sm">{phase.title}</h4>
                      <p className="text-xs text-[#aaa] mt-1">{phase.description}</p>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Input Form */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-[#181818] border border-[#303030] rounded-2xl p-6"
              >
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-white">Create Your Script</h2>
                  <div className="flex items-center space-x-2 bg-[#272727] rounded-full px-3 py-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-white text-xs font-medium">Ready</span>
                  </div>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-5">
                  {/* Title */}
                  <div>
                    <label className="block text-sm font-medium text-[#aaa] mb-2">
                      🎬 Video Title *
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      placeholder="How to create viral YouTube videos in 2024..."
                      required
                      className="w-full px-4 py-3 bg-[#272727] border border-[#404040] rounded-xl text-white placeholder-[#666] focus:border-[#ff0000] transition-all duration-300"
                    />
                  </div>

                                     {/* Brief */}
                   <div>
                     <label className="block text-sm font-medium text-[#aaa] mb-2 flex items-center">
                       📝 Script Instructions *
                       <span className="ml-2 text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full">
                         AI Directive
                       </span>
                     </label>
                     <textarea
                       value={formData.brief}
                       onChange={(e) => setFormData({...formData, brief: e.target.value})}
                       placeholder="Tell me exactly how you want this script created:&#10;&#10;• What tone and style should I use?&#10;• What specific points must be covered?&#10;• How should I structure the content?&#10;• What's your target audience?&#10;• Any specific requirements or constraints?&#10;&#10;Example: 'Create an energetic, beginner-friendly tutorial that explains X in simple terms, includes 3 actionable tips, and has a strong call-to-action at the end.'"
                       required
                       rows={6}
                       className="w-full px-4 py-3 bg-[#272727] border border-[#404040] rounded-xl text-white placeholder-[#666] focus:border-[#ff0000] transition-all duration-300 resize-none"
                     />
                     <div className="mt-2 flex items-center justify-between text-xs text-[#aaa]">
                       <span>💡 The AI will follow these instructions precisely when creating your script</span>
                       <span>{formData.brief.length}/1000</span>
                     </div>
                   </div>

                   {/* Video URLs Input */}
                   <div>
                     <label className="block text-sm font-medium text-[#aaa] mb-2 flex items-center">
                       📺 YouTube Video Links (Optional)
                       <span className="ml-2 text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">
                         Style Learning
                       </span>
                     </label>
                     <textarea
                       value={formData.videoUrls}
                       onChange={(e) => setFormData({...formData, videoUrls: e.target.value})}
                       placeholder="Paste YouTube video URLs here (one per line):&#10;https://www.youtube.com/watch?v=example1&#10;https://www.youtube.com/watch?v=example2&#10;https://youtu.be/example3"
                       rows={4}
                       className="w-full px-4 py-3 bg-[#272727] border border-[#404040] rounded-xl text-white placeholder-[#666] focus:border-[#ff0000] transition-all duration-300 resize-none font-mono text-sm"
                     />
                     <div className="mt-2 flex items-center justify-between text-xs text-[#aaa]">
                       <span>💡 AI will analyze these videos to learn writing style and structure</span>
                       <span>{formData.videoUrls.split('\n').filter(url => url.trim().includes('youtube')).length} videos</span>
                     </div>
                   </div>

                  {/* Configuration Row */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-[#aaa] mb-2">⏱️ Duration</label>
                      <select
                        value={formData.duration}
                        onChange={(e) => setFormData({...formData, duration: e.target.value})}
                        className="w-full px-3 py-2 bg-[#272727] border border-[#404040] rounded-lg text-white text-sm focus:border-[#ff0000] transition-all"
                      >
                        <option value="3-5 minutes">3-5 min</option>
                        <option value="5-10 minutes">5-10 min</option>
                        <option value="10-15 minutes">10-15 min</option>
                        <option value="15-30 minutes">15-30 min</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-[#aaa] mb-2">🎨 Style</label>
                      <select
                        value={formData.style}
                        onChange={(e) => setFormData({...formData, style: e.target.value})}
                        className="w-full px-3 py-2 bg-[#272727] border border-[#404040] rounded-lg text-white text-sm focus:border-[#ff0000] transition-all"
                      >
                        <option value="educational">Educational</option>
                        <option value="entertainment">Entertainment</option>
                        <option value="tutorial">Tutorial</option>
                        <option value="review">Review</option>
                        <option value="vlog">Vlog</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-[#aaa] mb-2">👥 Audience</label>
                      <input
                        type="text"
                        value={formData.targetAudience}
                        onChange={(e) => setFormData({...formData, targetAudience: e.target.value})}
                        placeholder="General audience"
                        className="w-full px-3 py-2 bg-[#272727] border border-[#404040] rounded-lg text-white placeholder-[#666] text-sm focus:border-[#ff0000] transition-all"
                      />
                    </div>
                  </div>

                  {/* Generate Button */}
                  <motion.button
                    type="submit"
                    disabled={isGenerating || !formData.title || !formData.brief}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full py-4 bg-gradient-to-r from-[#ff0000] to-[#cc0000] text-white font-bold rounded-xl hover:from-[#ff1a1a] hover:to-[#e60000] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center justify-center space-x-3"
                  >
                    {isGenerating ? (
                      <>
                        <motion.div 
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full"
                        />
                        <span>Generating Script...</span>
                      </>
                    ) : (
                      <>
                        <Zap className="w-5 h-5" />
                        <span>Generate YouTube Script</span>
                      </>
                    )}
                  </motion.button>

                  {/* Progress */}
                  {isGenerating && (
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span className="text-[#aaa]">{progressMessage}</span>
                        <span className="text-[#ff0000] font-medium">{progress}%</span>
                      </div>
                      <div className="w-full bg-[#404040] rounded-full h-2">
                        <motion.div 
                          className="bg-gradient-to-r from-[#ff0000] to-[#ff6600] h-2 rounded-full transition-all duration-500"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </form>
              </motion.div>
            </div>

            {/* Research Data - Full Width */}
            {researchData && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-[#181818] border border-[#303030] rounded-2xl p-6"
              >
                <h3 className="text-2xl font-bold text-white mb-6 flex items-center">
                  <Search className="w-6 h-6 text-green-400 mr-3" />
                  Research Sources Used
                </h3>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Web Sources */}
                  {researchData.webSources && (
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-4">🌐 Web Research ({researchData.webSources.length} sources)</h4>
                      <div className="space-y-3">
                        {researchData.webSources.slice(0, 5).map((source: any, index: number) => (
                          <div key={index} className="p-4 bg-[#272727] rounded-xl border border-[#404040] hover:border-[#555] transition-colors">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-white font-medium text-sm">
                                {source.title.substring(0, 50)}...
                              </span>
                              <span className="text-xs text-green-400 bg-green-400/20 px-2 py-1 rounded-full">
                                {source.source}
                              </span>
                            </div>
                            <a 
                              href={source.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                            >
                              View Source →
                            </a>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* YouTube Videos */}
                  {researchData.youtubeVideos && (
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-4">📺 YouTube Analysis ({researchData.youtubeVideos.length} videos)</h4>
                      <div className="space-y-3">
                        {researchData.youtubeVideos.map((video: any, index: number) => (
                          <div key={index} className="p-4 bg-[#272727] rounded-xl border border-[#404040] hover:border-[#555] transition-colors">
                            <div className="mb-3">
                              <h5 className="text-white font-medium text-sm mb-1">
                                {video.title}
                              </h5>
                              <p className="text-xs text-[#aaa]">{video.channel}</p>
                              <div className="flex items-center space-x-4 mt-2">
                                <span className="text-xs text-red-400 bg-red-400/20 px-2 py-1 rounded">
                                  {video.views} views
                                </span>
                                <span className="text-xs text-green-400 bg-green-400/20 px-2 py-1 rounded">
                                  {video.captionLength} chars
                                </span>
                              </div>
                            </div>
                            <a 
                              href={video.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center space-x-2 text-xs text-[#ff0000] hover:text-[#ff3333] transition-colors"
                            >
                              <PlayCircle className="w-4 h-4" />
                              <span>Watch Video</span>
                            </a>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}

            {/* Enhanced Script Display - Full Width Below Form */}
            {generatedContent && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="w-full"
              >
                <div className="mb-6 flex items-center justify-between">
                  <div>
                    <h2 className="text-3xl font-bold text-white mb-2">🎬 Your Viral Script is Ready!</h2>
                    <p className="text-[#aaa]">Research-powered, AI-generated YouTube script with stunning visual display</p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right text-sm text-[#aaa]">
                      <div>{wordCount.toLocaleString()} words</div>
                      <div>{Math.ceil(wordCount / 150)} min read time</div>
                    </div>
                    <Link 
                      href={`/youtube-script-view?title=${encodeURIComponent(formData.title)}&script=${encodeURIComponent(generatedContent)}`}
                      className="px-6 py-3 bg-[#ff0000] hover:bg-[#cc0000] text-white font-medium rounded-xl transition-all flex items-center space-x-2 shadow-lg"
                    >
                      <Eye className="w-5 h-5" />
                      <span>Full Screen View</span>
                    </Link>
                  </div>
                </div>
                
                {/* Full-Width Enhanced Script Display */}
                <div className="w-full rounded-2xl overflow-hidden border border-[#303030]/50 shadow-2xl">
                  <YouTubeScriptDisplay 
                    title={formData.title}
                    content={generatedContent}
                    wordCount={wordCount}
                  />
                </div>
                
                <div className="mt-6 flex items-center justify-center space-x-4">
                  <button
                    onClick={copyToClipboard}
                    className="flex items-center space-x-2 px-6 py-3 bg-[#272727] hover:bg-[#404040] text-white rounded-xl transition-all shadow-lg"
                  >
                    <Copy className="w-5 h-5" />
                    <span>Copy Script</span>
                  </button>
                  <button
                    onClick={downloadAsText}
                    className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-[#ff0000] to-[#cc0000] hover:from-[#ff1a1a] hover:to-[#e60000] text-white rounded-xl transition-all shadow-lg"
                  >
                    <Download className="w-5 h-5" />
                    <span>Download Script</span>
                  </button>
                </div>
              </motion.div>
            )}

          </div>
        </div>
      </div>
    </div>
  )
} 