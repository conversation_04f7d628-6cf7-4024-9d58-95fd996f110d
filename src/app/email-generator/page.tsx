'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Mail, Zap, ArrowLeft, Copy, Download, Sparkles, Plus, X, Send, Edit } from 'lucide-react'
import Link from 'next/link'

export default function EmailGeneratorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [formData, setFormData] = useState({
    purpose: '',
    audience: '',
    tone: 'professional',
    keyPoints: ['']
  })
  const [emailData, setEmailData] = useState({
    to: '',
    subject: '',
    content: ''
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')
  const [generatedSubject, setGeneratedSubject] = useState('')

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-emerald-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsGenerating(true)
    
    try {
      const response = await fetch('/api/generate/email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          keyPoints: formData.keyPoints.filter(point => point.trim() !== '')
        })
      })
      
      const data = await response.json()
      if (data.success) {
        setGeneratedContent(data.content)
        setGeneratedSubject(data.subject || '')
        setEmailData({
          to: emailData.to,
          subject: data.subject || '',
          content: data.content
        })
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      alert('Failed to generate email')
    } finally {
      setIsGenerating(false)
    }
  }

  const addKeyPoint = () => {
    setFormData({...formData, keyPoints: [...formData.keyPoints, '']})
  }

  const removeKeyPoint = (index: number) => {
    const newKeyPoints = formData.keyPoints.filter((_, i) => i !== index)
    setFormData({...formData, keyPoints: newKeyPoints.length > 0 ? newKeyPoints : ['']})
  }

  const updateKeyPoint = (index: number, value: string) => {
    const newKeyPoints = [...formData.keyPoints]
    newKeyPoints[index] = value
    setFormData({...formData, keyPoints: newKeyPoints})
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedContent)
  }

  const downloadAsText = () => {
    const content = generatedSubject ? `Subject: ${generatedSubject}\n\n${generatedContent}` : generatedContent
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `email-${formData.purpose.replace(/\s+/g, '-').toLowerCase()}.txt`
    a.click()
  }

  const sendViaGmail = () => {
    if (!emailData.to.trim()) {
      alert('Please enter a recipient email address')
      return
    }
    
    const subject = encodeURIComponent(emailData.subject || generatedSubject)
    const body = encodeURIComponent(emailData.content || generatedContent)
    const to = encodeURIComponent(emailData.to)
    
    const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=${to}&su=${subject}&body=${body}`
    window.open(gmailUrl, '_blank')
  }

  const sendViaOutlook = () => {
    if (!emailData.to.trim()) {
      alert('Please enter a recipient email address')
      return
    }
    
    const subject = encodeURIComponent(emailData.subject || generatedSubject)
    const body = encodeURIComponent(emailData.content || generatedContent)
    const to = encodeURIComponent(emailData.to)
    
    const outlookUrl = `https://outlook.live.com/mail/0/deeplink/compose?to=${to}&subject=${subject}&body=${body}`
    window.open(outlookUrl, '_blank')
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-900/20 via-black to-teal-900/20" />
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-emerald-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-teal-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="border-b border-white/10 bg-black/60 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </motion.button>
                </Link>
                <div className="flex items-center space-x-3">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-emerald-600 to-teal-600">
                    <Mail className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">Email Composer</h1>
                    <p className="text-gray-400">Professional Email Generation</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Form Panel */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                  <Sparkles className="w-5 h-5 mr-2 text-emerald-400" />
                  Email Configuration
                </h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Purpose */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Email Purpose *
                    </label>
                    <input
                      type="text"
                      value={formData.purpose}
                      onChange={(e) => setFormData({...formData, purpose: e.target.value})}
                      placeholder="e.g., Product launch announcement, Follow-up meeting..."
                      required
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                    />
                  </div>

                  {/* Audience */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Target Audience *
                    </label>
                    <input
                      type="text"
                      value={formData.audience}
                      onChange={(e) => setFormData({...formData, audience: e.target.value})}
                      placeholder="e.g., Customers, Team members, Potential clients..."
                      required
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                    />
                  </div>

                  {/* Tone */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Email Tone
                    </label>
                    <select
                      value={formData.tone}
                      onChange={(e) => setFormData({...formData, tone: e.target.value})}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                    >
                      <option value="professional">Professional</option>
                      <option value="friendly">Friendly</option>
                      <option value="formal">Formal</option>
                      <option value="casual">Casual</option>
                      <option value="persuasive">Persuasive</option>
                      <option value="urgent">Urgent</option>
                      <option value="grateful">Grateful</option>
                    </select>
                  </div>

                  {/* Key Points */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Key Points to Include
                    </label>
                    <div className="space-y-3">
                      {formData.keyPoints.map((point, index) => (
                        <div key={index} className="flex space-x-2">
                          <input
                            type="text"
                            value={point}
                            onChange={(e) => updateKeyPoint(index, e.target.value)}
                            placeholder={`Key point ${index + 1}...`}
                            className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                          />
                          {formData.keyPoints.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeKeyPoint(index)}
                              className="p-3 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-xl transition-all"
                            >
                              <X className="w-5 h-5" />
                            </button>
                          )}
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={addKeyPoint}
                        className="w-full py-3 border-2 border-dashed border-white/20 rounded-xl text-gray-400 hover:text-white hover:border-emerald-500/50 transition-all flex items-center justify-center space-x-2"
                      >
                        <Plus className="w-5 h-5" />
                        <span>Add Key Point</span>
                      </button>
                    </div>
                  </div>

                  {/* Generate Button */}
                  <motion.button
                    type="submit"
                    disabled={isGenerating || !formData.purpose || !formData.audience}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="w-full py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2"
                  >
                    {isGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Generating Email...</span>
                      </>
                    ) : (
                      <>
                        <Zap className="w-5 h-5" />
                        <span>Generate Email</span>
                      </>
                    )}
                  </motion.button>
                </form>
              </div>
            </motion.div>

            {/* Output Panel */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="space-y-6"
            >
              {/* Recipient & Send Section */}
              {generatedContent && (
                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <Send className="w-5 h-5 mr-2 text-emerald-400" />
                    Send Email
                  </h3>
                  
                  <div className="space-y-4">
                    {/* Recipient Input */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        To *
                      </label>
                      <input
                        type="email"
                        value={emailData.to}
                        onChange={(e) => setEmailData({...emailData, to: e.target.value})}
                        placeholder="<EMAIL>"
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                      />
                    </div>

                    {/* Subject Input */}
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Subject
                      </label>
                      <input
                        type="text"
                        value={emailData.subject || generatedSubject}
                        onChange={(e) => setEmailData({...emailData, subject: e.target.value})}
                        placeholder="Email subject"
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                      />
                    </div>

                    {/* Send Buttons */}
                    <div className="flex space-x-3">
                      <motion.button
                        onClick={sendViaGmail}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="flex-1 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white font-semibold rounded-xl hover:from-red-700 hover:to-red-800 transition-all flex items-center justify-center space-x-2"
                      >
                        <Mail className="w-5 h-5" />
                        <span>Send via Gmail</span>
                      </motion.button>
                      
                      <motion.button
                        onClick={sendViaOutlook}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className="flex-1 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all flex items-center justify-center space-x-2"
                      >
                        <Mail className="w-5 h-5" />
                        <span>Send via Outlook</span>
                      </motion.button>
                    </div>
                  </div>
                </div>
              )}

              {/* Generated Email Preview */}
              <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-white">Generated Email</h2>
                  {generatedContent && (
                    <div className="flex space-x-2">
                      <button
                        onClick={copyToClipboard}
                        className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors"
                        title="Copy to clipboard"
                      >
                        <Copy className="w-4 h-4 text-white" />
                      </button>
                      <button
                        onClick={downloadAsText}
                        className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors"
                        title="Download as text"
                      >
                        <Download className="w-4 h-4 text-white" />
                      </button>
                    </div>
                  )}
                </div>
                
                {generatedContent ? (
                  <div className="space-y-4">
                    {/* Subject Preview */}
                    {generatedSubject && (
                      <div className="bg-black/40 rounded-xl p-4 border border-white/10">
                        <div className="flex items-center space-x-2 mb-2">
                          <Edit className="w-4 h-4 text-emerald-400" />
                          <span className="text-emerald-400 font-medium">Subject:</span>
                        </div>
                        <div className="text-white font-medium">{generatedSubject}</div>
                      </div>
                    )}
                    
                    {/* Email Content */}
                    <div className="bg-black/40 rounded-xl p-4 border border-white/10 max-h-[500px] overflow-y-auto">
                      <div className="flex items-center space-x-2 mb-3">
                        <Mail className="w-4 h-4 text-emerald-400" />
                        <span className="text-emerald-400 font-medium">Email Content:</span>
                      </div>
                      <div className="text-gray-300 whitespace-pre-wrap leading-relaxed">
                        {generatedContent}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12 text-gray-400">
                    <Mail className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p>Your generated email will appear here...</p>
                    <p className="text-sm mt-2">Fill out the form and click "Generate Email"</p>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
} 