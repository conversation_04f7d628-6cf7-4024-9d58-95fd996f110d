'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { FileText, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { safeDecodeURIComponent } from '@/lib/utils'
import YouTubeScriptDisplay from '@/components/YouTubeScriptDisplay'

export default function YouTubeScriptViewPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [script, setScript] = useState('')
  const [title, setTitle] = useState('YouTube Script')
  const [loading, setLoading] = useState(true)

  // Handle authentication redirect
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Handle content loading from URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const scriptFromUrl = urlParams.get('script')
    const titleFromUrl = urlParams.get('title')

    if (scriptFromUrl) {
      const decodedScript = safeDecodeURIComponent(scriptFromUrl)
      const decodedTitle = safeDecodeURIComponent(titleFromUrl || '', 'YouTube Script')
      
      setScript(decodedScript)
      setTitle(decodedTitle)
      setLoading(false)
      return
    }

    // Fallback: check localStorage for script content
    const savedScript = localStorage.getItem('youtubeScript')
    const savedTitle = localStorage.getItem('youtubeScriptTitle')

    if (savedScript) {
      setScript(savedScript)
      setTitle(savedTitle || 'YouTube Script')
    } else {
      // No script found, redirect to content library
      setTimeout(() => {
        router.push('/content')
      }, 3000)
    }

    setLoading(false)
  }, [router])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-red-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-[#aaa]">Loading YouTube script...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="relative z-10 text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full mx-auto mb-4"
          />
          <h2 className="text-xl font-semibold text-white mb-2">Loading YouTube Script</h2>
          <p className="text-[#aaa]">Preparing your viral content...</p>
        </div>
      </div>
    )
  }

  if (!script) {
    return (
      <div className="min-h-screen bg-[#0f0f0f] flex items-center justify-center">
        <div className="relative z-10 text-center">
          <div className="p-3 bg-gradient-to-r from-red-600 to-red-700 rounded-2xl mb-6 w-fit mx-auto">
            <FileText className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">No YouTube Script Found</h2>
          <p className="text-[#aaa] mb-6">No YouTube script found in your session.</p>
          <Link
            href="/content"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:scale-105 transition-transform"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Back to Content Library
          </Link>
        </div>
      </div>
    )
  }

  const wordCount = script.split(/\s+/).filter(word => word.length > 0).length

  return <YouTubeScriptDisplay title={title} content={script} wordCount={wordCount} />
} 