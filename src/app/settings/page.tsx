'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useSession, signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useTheme } from '@/components/ThemeProvider'
import { 
  User,
  Settings as SettingsIcon,
  Bell,
  Shield,
  Palette,
  Save,
  Home,
  CheckCircle,
  AlertCircle,
  Camera,
  Zap,
  Crown,
  TrendingUp,
  Clock,
  BarChart3,
  Target,
  LogOut
} from 'lucide-react'
import Link from 'next/link'

interface UserProfile {
  id: string
  name: string | null
  email: string | null
  image: string | null
  firstName: string | null
  lastName: string | null
  bio: string | null
  settings?: {
    defaultLanguage: string
    timezone: string
    defaultWordCount: number
    defaultTone: string
    includeResearchByDefault: boolean
    autoSaveEnabled: boolean
    emailNotifications: boolean
    pushNotifications: boolean
    weeklyReports: boolean
    marketingEmails: boolean
    theme: string
    accentColor: string
    animationsEnabled: boolean
    compactMode: boolean
    profileVisibility: string
    dataSharing: boolean
    analyticsTracking: boolean
  }
  subscription?: {
    plan: string
    status: string
  }
  stats?: {
    totalContent: number
    totalUsage: number
  }
}

export default function SettingsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { 
    theme, 
    setTheme, 
    compactMode, 
    setCompactMode, 
    accentColor, 
    setAccentColor, 
    animationsEnabled, 
    setAnimationsEnabled 
  } = useTheme()
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isSaving, setIsSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('profile')

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  useEffect(() => {
    if (session?.user) {
      fetchUserProfile()
    }
  }, [session])

  const fetchUserProfile = async () => {
    try {
      const response = await fetch('/api/user/profile')
      if (response.ok) {
        const data = await response.json()
        setUserProfile(data)
      }
    } catch (error) {
      console.error('Error fetching user profile:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSave = async () => {
    if (!userProfile) return
    
    setIsSaving(true)
    setSaveStatus('idle')

    try {
      // Sync theme provider values with userProfile settings
      const updatedSettings = {
        ...userProfile.settings,
        theme,
        accentColor,
        animationsEnabled,
        compactMode
      }

      // Save profile changes
      const profileResponse = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: userProfile.firstName,
          lastName: userProfile.lastName,
          bio: userProfile.bio,
        }),
      })

      // Save settings changes
      const settingsResponse = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSettings),
      })

      if (profileResponse.ok && settingsResponse.ok) {
        setSaveStatus('success')
        setTimeout(() => setSaveStatus('idle'), 3000)
        // Refresh profile data
        await fetchUserProfile()
      } else {
        setSaveStatus('error')
      }
    } catch (error) {
      console.error('Error saving settings:', error)
      setSaveStatus('error')
    } finally {
      setIsSaving(false)
    }
  }

  const updateProfile = (key: keyof UserProfile, value: any) => {
    setUserProfile(prev => prev ? ({ ...prev, [key]: value }) : null)
  }

  const updateSetting = (key: string, value: any) => {
    setUserProfile(prev => prev ? ({
      ...prev,
      settings: {
        ...prev.settings!,
        [key]: value
      }
    }) : null)
  }

  const getUserInitials = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName[0]}${userProfile.lastName[0]}`
    } else if (userProfile?.name) {
      const names = userProfile.name.split(' ')
      return names.length > 1 ? `${names[0][0]}${names[names.length - 1][0]}` : names[0][0]
    } else if (userProfile?.email) {
      return userProfile.email[0].toUpperCase()
    }
    return 'U'
  }

  const getDisplayName = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName} ${userProfile.lastName}`
    } else if (userProfile?.name) {
      return userProfile.name
    } else if (userProfile?.email) {
      return userProfile.email.split('@')[0]
    }
    return 'User'
  }

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/login' })
  }

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User, color: 'from-blue-500 to-cyan-500' },
    { id: 'preferences', label: 'Preferences', icon: SettingsIcon, color: 'from-emerald-500 to-teal-500' },
    { id: 'notifications', label: 'Notifications', icon: Bell, color: 'from-yellow-500 to-orange-500' },
    { id: 'appearance', label: 'Appearance', icon: Palette, color: 'from-purple-500 to-pink-500' },
    { id: 'privacy', label: 'Privacy', icon: Shield, color: 'from-red-500 to-pink-500' },
  ]

  // Loading state
  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 dark:from-gray-900 dark:via-black dark:to-gray-900 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading your settings...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 dark:from-gray-900 dark:via-black dark:to-gray-900 flex items-center justify-center">
        <div className="text-center space-y-4">
          <p className="text-gray-600 dark:text-gray-400">Unable to load profile</p>
          <Link href="/login">
            <button className="px-4 py-2 bg-violet-600 text-white rounded-lg">
              Sign In Again
            </button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-gray-100 dark:from-gray-900 dark:via-black dark:to-gray-900">
      {/* Header */}
      <div className="border-b border-gray-200/50 dark:border-white/10 bg-white/80 dark:bg-black/20 backdrop-blur-xl">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Link href="/dashboard">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  className="flex items-center space-x-3 text-gray-700 dark:text-white hover:text-violet-500 dark:hover:text-violet-400 transition-colors"
                >
                  <Home className="w-5 h-5" />
                  <span className="font-semibold">Dashboard</span>
                </motion.button>
              </Link>
              
              <div className="text-gray-400 dark:text-gray-600">|</div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-violet-600 to-indigo-600 dark:from-violet-400 dark:to-indigo-400 bg-clip-text text-transparent">
                Personal Settings
              </h1>
            </div>
            
            <div className="flex items-center space-x-4">
              {saveStatus === 'success' && (
                <motion.div 
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center text-emerald-600 dark:text-emerald-400 text-sm bg-emerald-100 dark:bg-emerald-400/10 px-3 py-2 rounded-lg"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Changes saved!
                </motion.div>
              )}
              
              {saveStatus === 'error' && (
                <motion.div 
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center text-red-600 dark:text-red-400 text-sm bg-red-100 dark:bg-red-400/10 px-3 py-2 rounded-lg"
                >
                  <AlertCircle className="w-4 h-4 mr-2" />
                  Error saving
                </motion.div>
              )}
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                onClick={handleSave}
                disabled={isSaving}
                className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 text-white rounded-xl transition-all disabled:opacity-50 shadow-lg"
              >
                <Save className="w-4 h-4" />
                <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                onClick={handleSignOut}
                className="flex items-center space-x-2 px-4 py-3 bg-red-100 dark:bg-red-600/20 hover:bg-red-200 dark:hover:bg-red-600/30 border border-red-300 dark:border-red-500/30 text-red-600 dark:text-red-400 rounded-xl transition-all"
              >
                <LogOut className="w-4 h-4" />
                <span>Sign Out</span>
              </motion.button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Profile Summary Card */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white/80 dark:bg-gradient-to-br dark:from-white/10 dark:to-white/5 backdrop-blur-xl border border-gray-200 dark:border-white/10 rounded-3xl p-6 sticky top-8 shadow-lg"
            >
              {/* Profile Header */}
              <div className="text-center mb-6">
                <div className="relative inline-block">
                  {userProfile.image ? (
                    <img
                      src={userProfile.image}
                      alt={getDisplayName()}
                      className="w-24 h-24 rounded-2xl object-cover border-4 border-gray-200/50 dark:border-white/20 mb-4 mx-auto"
                    />
                  ) : (
                    <div className="w-24 h-24 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-4 mx-auto">
                      {getUserInitials()}
                    </div>
                  )}
                  <button className="absolute -bottom-1 -right-1 w-8 h-8 bg-violet-600 hover:bg-violet-700 rounded-full flex items-center justify-center text-white transition-colors">
                    <Camera className="w-4 h-4" />
                  </button>
                </div>
                <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-1">
                  {getDisplayName()}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-2">{userProfile.email}</p>
                <div className="flex items-center justify-center space-x-1 mb-4">
                  <Crown className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 text-sm font-medium">
                    {userProfile.subscription?.plan === 'free' ? 'Free Plan' : 
                     userProfile.subscription?.plan === 'pro' ? 'Pro Member' : 'Member'}
                  </span>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-white/5 rounded-xl">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-500/20 rounded-lg flex items-center justify-center">
                      <BarChart3 className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <span className="text-gray-600 dark:text-gray-300 text-sm">Content Created</span>
                  </div>
                  <span className="text-gray-800 dark:text-white font-semibold">{userProfile.stats?.totalContent || 0}</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-white/5 rounded-xl">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-emerald-100 dark:bg-emerald-500/20 rounded-lg flex items-center justify-center">
                      <TrendingUp className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                    </div>
                    <span className="text-gray-600 dark:text-gray-300 text-sm">Quality Score</span>
                  </div>
                  <span className="text-gray-800 dark:text-white font-semibold">9.8/10</span>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-white/5 rounded-xl">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-100 dark:bg-purple-500/20 rounded-lg flex items-center justify-center">
                      <Clock className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                    </div>
                    <span className="text-gray-600 dark:text-gray-300 text-sm">Time Saved</span>
                  </div>
                  <span className="text-gray-800 dark:text-white font-semibold">48h</span>
                </div>
              </div>

              {/* Navigation Tabs */}
              <div className="space-y-2">
                {tabs.map((tab) => (
                  <motion.button
                    key={tab.id}
                    whileHover={{ scale: 1.02 }}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 p-3 rounded-xl transition-all ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-violet-100 to-indigo-100 dark:from-violet-600/20 dark:to-indigo-600/20 border border-violet-300 dark:border-violet-500/30 text-violet-700 dark:text-white'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-white/5'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      activeTab === tab.id
                        ? `bg-gradient-to-r ${tab.color}`
                        : 'bg-gray-200 dark:bg-white/10'
                    }`}>
                      <tab.icon className="w-4 h-4" />
                    </div>
                    <span className="font-medium">{tab.label}</span>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              {activeTab === 'profile' && (
                <div className="bg-white/80 dark:bg-gradient-to-br dark:from-white/10 dark:to-white/5 backdrop-blur-xl border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-lg">
                  <div className="flex items-center space-x-4 mb-8">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center">
                      <User className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">Profile Information</h2>
                      <p className="text-gray-600 dark:text-gray-400">Manage your personal details and bio</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">First Name</label>
                      <input
                        type="text"
                        value={userProfile.firstName || ''}
                        onChange={(e) => updateProfile('firstName', e.target.value)}
                        className="w-full px-4 py-4 bg-gray-50 dark:bg-white/5 border border-gray-300 dark:border-white/10 rounded-xl text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-violet-500 dark:focus:border-violet-500/50 focus:bg-white dark:focus:bg-white/10 transition-all"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Last Name</label>
                      <input
                        type="text"
                        value={userProfile.lastName || ''}
                        onChange={(e) => updateProfile('lastName', e.target.value)}
                        className="w-full px-4 py-4 bg-gray-50 dark:bg-white/5 border border-gray-300 dark:border-white/10 rounded-xl text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-violet-500 dark:focus:border-violet-500/50 focus:bg-white dark:focus:bg-white/10 transition-all"
                      />
                    </div>
                  </div>
                  
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Email Address</label>
                    <input
                      type="email"
                      value={userProfile.email || ''}
                      onChange={(e) => updateProfile('email', e.target.value)}
                      className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-violet-500/50 focus:bg-white/10 transition-all"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Bio</label>
                    <textarea
                      value={userProfile.bio || ''}
                      onChange={(e) => updateProfile('bio', e.target.value)}
                      rows={4}
                      placeholder="Tell us about yourself..."
                      className="w-full px-4 py-4 bg-gray-50 dark:bg-white/5 border border-gray-300 dark:border-white/10 rounded-xl text-gray-800 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-violet-500 dark:focus:border-violet-500/50 focus:bg-white dark:focus:bg-white/10 transition-all resize-none"
                    />
                  </div>
                </div>
              )}

              {activeTab === 'preferences' && (
                <div className="bg-white/80 dark:bg-gradient-to-br dark:from-white/10 dark:to-white/5 backdrop-blur-xl border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-lg">
                  <div className="flex items-center space-x-4 mb-8">
                    <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center">
                      <SettingsIcon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">Content Preferences</h2>
                      <p className="text-gray-600 dark:text-gray-400">Customize your content creation defaults</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Default Word Count</label>
                      <input
                        type="number"
                        min="100"
                        max="10000"
                        value={userProfile.settings?.defaultWordCount}
                        onChange={(e) => updateSetting('defaultWordCount', parseInt(e.target.value))}
                        className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Default Tone</label>
                      <select
                        value={userProfile.settings?.defaultTone}
                        onChange={(e) => updateSetting('defaultTone', e.target.value)}
                        className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all"
                      >
                        <option value="professional">Professional</option>
                        <option value="casual">Casual</option>
                        <option value="authoritative">Authoritative</option>
                        <option value="conversational">Conversational</option>
                        <option value="technical">Technical</option>
                        <option value="friendly">Friendly</option>
                      </select>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <label className="flex items-center justify-between p-4 bg-gray-50 dark:bg-white/5 rounded-xl hover:bg-gray-100 dark:hover:bg-white/10 transition-all cursor-pointer">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center">
                          <Target className="w-5 h-5 text-blue-400" />
                        </div>
                        <div>
                          <span className="text-gray-800 dark:text-white font-medium">Include Research by Default</span>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">Automatically include research in content generation</p>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={userProfile.settings?.includeResearchByDefault}
                        onChange={(e) => updateSetting('includeResearchByDefault', e.target.checked)}
                        className="w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"
                      />
                    </label>
                    
                    <label className="flex items-center justify-between p-4 bg-gray-50 dark:bg-white/5 rounded-xl hover:bg-gray-100 dark:hover:bg-white/10 transition-all cursor-pointer">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-emerald-500/20 rounded-xl flex items-center justify-center">
                          <Zap className="w-5 h-5 text-emerald-400" />
                        </div>
                        <div>
                          <span className="text-gray-800 dark:text-white font-medium">Auto-Save</span>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">Automatically save your work as you type</p>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        checked={userProfile.settings?.autoSaveEnabled}
                        onChange={(e) => updateSetting('autoSaveEnabled', e.target.checked)}
                        className="w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"
                      />
                    </label>
                  </div>
                </div>
              )}

              {activeTab === 'notifications' && (
                <div className="bg-white/80 dark:bg-gradient-to-br dark:from-white/10 dark:to-white/5 backdrop-blur-xl border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-lg">
                  <div className="flex items-center space-x-4 mb-8">
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center">
                      <Bell className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">Notification Preferences</h2>
                      <p className="text-gray-600 dark:text-gray-400">Choose how you want to be notified</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {[
                      { key: 'emailNotifications', title: 'Email Notifications', desc: 'Receive updates and alerts via email' },
                      { key: 'pushNotifications', title: 'Push Notifications', desc: 'Get instant browser notifications' },
                      { key: 'weeklyReports', title: 'Weekly Reports', desc: 'Summary of your content creation activity' },
                      { key: 'marketingEmails', title: 'Marketing Emails', desc: 'Product updates, tips, and special offers' }
                    ].map((item) => (
                      <label key={item.key} className="flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer">
                        <div>
                          <span className="text-gray-800 dark:text-white font-medium">{item.title}</span>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">{item.desc}</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={userProfile.settings?.[item.key as keyof typeof userProfile.settings] as boolean}
                          onChange={(e) => updateSetting(item.key as keyof typeof userProfile.settings, e.target.checked)}
                          className="w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"
                        />
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'appearance' && (
                <div className="bg-white/80 dark:bg-gradient-to-br dark:from-white/10 dark:to-white/5 backdrop-blur-xl border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-lg">
                  <div className="flex items-center space-x-4 mb-8">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                      <Palette className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">Appearance & Theme</h2>
                      <p className="text-gray-600 dark:text-gray-400">Customize how the interface looks</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Theme</label>
                      <select
                        value={userProfile.settings?.theme || theme}
                        onChange={(e) => {
                          const newTheme = e.target.value as 'dark' | 'light' | 'auto'
                          updateSetting('theme', newTheme)
                          setTheme(newTheme)
                        }}
                        className="w-full px-4 py-4 bg-gray-50 dark:bg-white/5 border border-gray-300 dark:border-white/10 rounded-xl text-gray-800 dark:text-white focus:border-violet-500 dark:focus:border-violet-500/50 focus:bg-white dark:focus:bg-white/10 transition-all"
                      >
                        <option value="dark">Dark</option>
                        <option value="light">Light</option>
                        <option value="auto">Auto</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Accent Color</label>
                      <div className="grid grid-cols-6 gap-3">
                        {[
                          { name: 'blue', color: 'bg-blue-500', ring: 'ring-blue-500' },
                          { name: 'purple', color: 'bg-purple-500', ring: 'ring-purple-500' },
                          { name: 'green', color: 'bg-green-500', ring: 'ring-green-500' },
                          { name: 'red', color: 'bg-red-500', ring: 'ring-red-500' },
                          { name: 'orange', color: 'bg-orange-500', ring: 'ring-orange-500' },
                          { name: 'pink', color: 'bg-pink-500', ring: 'ring-pink-500' },
                          { name: 'cyan', color: 'bg-cyan-500', ring: 'ring-cyan-500' },
                          { name: 'yellow', color: 'bg-yellow-500', ring: 'ring-yellow-500' },
                          { name: 'indigo', color: 'bg-indigo-500', ring: 'ring-indigo-500' },
                          { name: 'teal', color: 'bg-teal-500', ring: 'ring-teal-500' },
                          { name: 'emerald', color: 'bg-emerald-500', ring: 'ring-emerald-500' },
                          { name: 'rose', color: 'bg-rose-500', ring: 'ring-rose-500' }
                        ].map((colorOption) => (
                          <motion.button
                            key={colorOption.name}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => {
                              updateSetting('accentColor', colorOption.name)
                              setAccentColor(colorOption.name)
                            }}
                            className={`w-12 h-12 rounded-xl ${colorOption.color} transition-all ${
                              (userProfile.settings?.accentColor || accentColor) === colorOption.name
                                ? `ring-4 ${colorOption.ring} ring-opacity-50 scale-110`
                                : 'hover:scale-105'
                            }`}
                            title={colorOption.name.charAt(0).toUpperCase() + colorOption.name.slice(1)}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <label className="flex items-center justify-between p-4 bg-gray-50 dark:bg-white/5 rounded-xl hover:bg-gray-100 dark:hover:bg-white/10 transition-all cursor-pointer">
                      <div>
                        <span className="text-gray-800 dark:text-white font-medium">Enable Animations</span>
                        <p className="text-gray-600 dark:text-gray-400 text-sm">Smooth transitions and motion effects</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={userProfile.settings?.animationsEnabled ?? animationsEnabled}
                        onChange={(e) => {
                          updateSetting('animationsEnabled', e.target.checked)
                          setAnimationsEnabled(e.target.checked)
                        }}
                        className="w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"
                      />
                    </label>
                    
                    <label className="flex items-center justify-between p-4 bg-gray-50 dark:bg-white/5 rounded-xl hover:bg-gray-100 dark:hover:bg-white/10 transition-all cursor-pointer">
                      <div>
                        <span className="text-gray-800 dark:text-white font-medium">Compact Mode</span>
                        <p className="text-gray-600 dark:text-gray-400 text-sm">Reduce spacing for more content on screen</p>
                      </div>
                      <input
                        type="checkbox"
                        checked={userProfile.settings?.compactMode ?? compactMode}
                        onChange={(e) => {
                          updateSetting('compactMode', e.target.checked)
                          setCompactMode(e.target.checked)
                        }}
                        className="w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"
                      />
                    </label>
                  </div>
                </div>
              )}

              {activeTab === 'privacy' && (
                <div className="bg-white/80 dark:bg-gradient-to-br dark:from-white/10 dark:to-white/5 backdrop-blur-xl border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-lg">
                  <div className="flex items-center space-x-4 mb-8">
                    <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl flex items-center justify-center">
                      <Shield className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">Privacy & Security</h2>
                      <p className="text-gray-600 dark:text-gray-400">Control your data and privacy settings</p>
                    </div>
                  </div>

                  <div className="mb-8">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Profile Visibility</label>
                    <select
                      value={userProfile.settings?.profileVisibility}
                      onChange={(e) => updateSetting('profileVisibility', e.target.value)}
                      className="w-full px-4 py-4 bg-gray-50 dark:bg-white/5 border border-gray-300 dark:border-white/10 rounded-xl text-gray-800 dark:text-white focus:border-violet-500 dark:focus:border-violet-500/50 focus:bg-white dark:focus:bg-white/10 transition-all"
                    >
                      <option value="private">Private</option>
                      <option value="team">Team Only</option>
                      <option value="public">Public</option>
                    </select>
                  </div>
                  
                  <div className="space-y-4">
                    {[
                      { key: 'dataSharing', title: 'Data Sharing', desc: 'Share anonymized usage data to improve our services' },
                      { key: 'analyticsTracking', title: 'Analytics Tracking', desc: 'Help us improve the product with usage analytics' }
                    ].map((item) => (
                      <label key={item.key} className="flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer">
                        <div>
                          <span className="text-gray-800 dark:text-white font-medium">{item.title}</span>
                          <p className="text-gray-600 dark:text-gray-400 text-sm">{item.desc}</p>
                        </div>
                        <input
                          type="checkbox"
                          checked={userProfile.settings?.[item.key as keyof typeof userProfile.settings] as boolean}
                          onChange={(e) => updateSetting(item.key as keyof typeof userProfile.settings, e.target.checked)}
                          className="w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"
                        />
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
} 