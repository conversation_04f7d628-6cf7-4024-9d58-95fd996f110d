'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowLeft, 
  Sparkles, 
  Zap, 
  Copy, 
  Download, 
  Share2,
  Hash,
  MessageSquare,
  Smile,
  Target,
  TrendingUp,
  BarChart,
  Plus,
  X,
  Check,
  AlertCircle,
  Clock
} from 'lucide-react'
import Link from 'next/link'

// Platform configuration
const PLATFORMS = {
  twitter: {
    name: 'Twitter/X',
    icon: '𝕏',
    color: 'from-black to-gray-800',
    maxLength: 280,
    optimalLength: { min: 71, max: 100 },
    features: ['hashtags', 'mentions', 'threads'],
    placeholder: 'What\'s happening?'
  },
  linkedin: {
    name: 'LinkedIn',
    icon: '💼',
    color: 'from-blue-600 to-blue-700',
    maxLength: 3000,
    optimalLength: { min: 50, max: 150 },
    features: ['hashtags', 'mentions', 'professional'],
    placeholder: 'Share your professional thoughts...'
  },
  facebook: {
    name: 'Facebook',
    icon: '📘',
    color: 'from-blue-500 to-blue-600',
    maxLength: 63206,
    optimalLength: { min: 40, max: 80 },
    features: ['hashtags', 'mentions', 'emojis'],
    placeholder: 'What\'s on your mind?'
  }
}

const CONTENT_TYPES = [
  { id: 'promotional', label: 'Promotional', icon: '📢', description: 'Product launches, offers, announcements' },
  { id: 'educational', label: 'Educational', icon: '🎓', description: 'Tips, how-tos, tutorials' },
  { id: 'engaging', label: 'Engaging', icon: '💬', description: 'Questions, polls, discussions' },
  { id: 'behind_scenes', label: 'Behind the Scenes', icon: '🎬', description: 'Company culture, process insights' },
  { id: 'news', label: 'News & Updates', icon: '📰', description: 'Industry news, company updates' },
  { id: 'inspirational', label: 'Inspirational', icon: '✨', description: 'Motivational quotes, success stories' }
]

const TONES = [
  { id: 'professional', label: 'Professional', emoji: '💼' },
  { id: 'casual', label: 'Casual', emoji: '😊' },
  { id: 'funny', label: 'Funny', emoji: '😄' },
  { id: 'inspiring', label: 'Inspiring', emoji: '🌟' },
  { id: 'urgent', label: 'Urgent', emoji: '⚡' },
  { id: 'conversational', label: 'Conversational', emoji: '💭' },
  { id: 'enthusiastic', label: 'Enthusiastic', emoji: '🎉' },
  { id: 'helpful', label: 'Helpful', emoji: '🤝' }
]

interface GeneratedContent {
  [key: string]: {
    content: string
    hashtags: string[]
    characterCount: number
    engagementScore?: number
    optimizationTips?: string[]
  }
}

export default function SocialMediaGeneratorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Form state
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['twitter', 'linkedin', 'facebook'])
  const [contentInput, setContentInput] = useState('')
  const [contentType, setContentType] = useState('promotional')
  const [tone, setTone] = useState('professional')
  const [targetAudience, setTargetAudience] = useState('')
  const [keywords, setKeywords] = useState('')
  const [sourceUrl, setSourceUrl] = useState('')
  const [includeHashtags, setIncludeHashtags] = useState(true)
  const [includeEmojis, setIncludeEmojis] = useState(true)
  const [includeCTA, setIncludeCTA] = useState(true)
  const [isScrapingUrl, setIsScrapingUrl] = useState(false)
  const [scrapedContent, setScrapedContent] = useState('')

  // Generation state
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent>({})
  const [activeTab, setActiveTab] = useState('input')
  const [copied, setCopied] = useState<string | null>(null)
  const [saved, setSaved] = useState(false)
  const [isViewMode, setIsViewMode] = useState(false)
  const [viewModeTitle, setViewModeTitle] = useState('')

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Handle view mode from content library
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const viewId = urlParams.get('view')
    const content = urlParams.get('content')
    const title = urlParams.get('title')

    if (viewId && content) {
      try {
        const parsedContent = JSON.parse(decodeURIComponent(content))
        setGeneratedContent(parsedContent)
        setActiveTab('preview') // Always open in preview mode
        setIsViewMode(true) // Mark as view mode
        
        if (title) {
          setViewModeTitle(decodeURIComponent(title))
        }

        // Remove URL parameters to clean up the URL
        window.history.replaceState({}, '', '/social-media-generator')
      } catch (error) {
        console.error('Error parsing view content:', error)
      }
    }
  }, [])

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-emerald-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return null
  }

  const handlePlatformToggle = (platform: string) => {
    setSelectedPlatforms(prev => 
      prev.includes(platform) 
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    )
  }

  const handleUrlScrape = async () => {
    if (!sourceUrl.trim()) return

    setIsScrapingUrl(true)
    try {
      const response = await fetch('/api/scrape-url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: sourceUrl })
      })

      const data = await response.json()
      
      if (data.success) {
        setScrapedContent(data.content)
      } else {
        alert('Error scraping URL: ' + data.error)
      }
    } catch (error) {
      console.error('URL scraping error:', error)
      alert('Failed to scrape URL content')
    } finally {
      setIsScrapingUrl(false)
    }
  }

  const handleGenerate = async () => {
    if (!contentInput.trim() || selectedPlatforms.length === 0) {
      return
    }

    setIsGenerating(true)
    setActiveTab('preview')

    try {
      const response = await fetch('/api/generate/social', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: contentInput,
          platforms: selectedPlatforms,
          contentType,
          tone,
          targetAudience,
          keywords,
          sourceUrl,
          scrapedContent,
          includeHashtags,
          includeEmojis,
          includeCTA
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setGeneratedContent(data.generated)
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Generation error:', error)
      alert('Failed to generate social media content')
    } finally {
      setIsGenerating(false)
    }
  }

  const copyToClipboard = async (platform: string, content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopied(platform)
      setTimeout(() => setCopied(null), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const saveContent = async () => {
    try {
      const response = await fetch('/api/content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'social_media',
          title: `Social Media Post - ${new Date().toLocaleDateString()}`,
          content: JSON.stringify(generatedContent),
          metadata: {
            platforms: selectedPlatforms,
            contentType,
            tone,
            targetAudience,
            keywords,
            generatedAt: new Date().toISOString()
          }
        })
      })

      if (response.ok) {
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)
      }
    } catch (error) {
      console.error('Failed to save content:', error)
    }
  }

  const getCharacterColor = (count: number, platform: string) => {
    const config = PLATFORMS[platform as keyof typeof PLATFORMS]
    if (count > config.maxLength) return 'text-red-400'
    if (count > config.optimalLength.max) return 'text-yellow-400'
    if (count >= config.optimalLength.min) return 'text-green-400'
    return 'text-gray-400'
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-emerald-900/20 via-black to-teal-900/20" />
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-emerald-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-teal-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Header */}
      <div className="relative z-10">
        <div className="border-b border-white/10 bg-black/60 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all"
                  >
                    <ArrowLeft className="w-5 h-5 text-white" />
                  </motion.button>
                </Link>
                <div className="flex items-center space-x-3">
                  <div className="p-3 rounded-2xl bg-gradient-to-br from-emerald-600 to-teal-600">
                    <Share2 className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-white">
                      {isViewMode ? 'Viewing Social Media Post' : 'Social Media Generator'}
                    </h1>
                    <p className="text-gray-400">
                      {isViewMode 
                        ? (viewModeTitle || 'Viewing generated social media content')
                        : 'AI-Powered Multi-Platform Content Creation'
                      }
                    </p>
                  </div>
                </div>
              </div>
              
              {saved && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600/20 border border-green-500/30 rounded-lg"
                >
                  <Check className="w-4 h-4 text-green-400" />
                  <span className="text-green-400 font-medium">Saved to Library</span>
                </motion.div>
              )}
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex space-x-1 bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-1">
            {!isViewMode && (
              <button
                onClick={() => setActiveTab('input')}
                className={`flex-1 px-4 py-2 rounded-lg transition-all ${
                  activeTab === 'input'
                    ? 'bg-emerald-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <Plus className="w-4 h-4" />
                  <span>Create Content</span>
                </div>
              </button>
            )}
            <button
              onClick={() => setActiveTab('preview')}
              className={`${isViewMode ? 'w-full' : 'flex-1'} px-4 py-2 rounded-lg transition-all ${
                activeTab === 'preview'
                  ? 'bg-emerald-600 text-white'
                  : 'text-gray-400 hover:text-white hover:bg-white/10'
              }`}
            >
              <div className="flex items-center justify-center space-x-2">
                <MessageSquare className="w-4 h-4" />
                <span>{isViewMode ? 'View & Export Content' : 'Preview & Export'}</span>
              </div>
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-6 pb-8">
          <AnimatePresence mode="wait">
            {activeTab === 'input' && (
              <motion.div
                key="input"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-8"
              >
                {/* Platform Selection */}
                <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                  <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2 text-emerald-400" />
                    Select Platforms
                  </h2>
                  
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {Object.entries(PLATFORMS).map(([key, platform]) => (
                      <motion.button
                        key={key}
                        onClick={() => handlePlatformToggle(key)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        className={`p-4 rounded-xl border transition-all ${
                          selectedPlatforms.includes(key)
                            ? 'bg-emerald-600/20 border-emerald-500/50 text-white'
                            : 'bg-white/5 border-white/20 text-gray-400 hover:bg-white/10 hover:text-white'
                        }`}
                      >
                        <div className="text-2xl mb-2">{platform.icon}</div>
                        <div className="font-medium">{platform.name}</div>
                        <div className="text-xs opacity-75">
                          {platform.optimalLength.min}-{platform.optimalLength.max} chars optimal
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* Content Input */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left Column - Main Input */}
                  <div className="space-y-6">
                    <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                      <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                        <Sparkles className="w-5 h-5 mr-2 text-emerald-400" />
                        Content Details
                      </h2>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Main Content/Topic *
                          </label>
                          <textarea
                            value={contentInput}
                            onChange={(e) => setContentInput(e.target.value)}
                            placeholder="Describe your content, announcement, or topic..."
                            className="w-full h-32 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all resize-none"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Target Audience
                          </label>
                          <input
                            type="text"
                            value={targetAudience}
                            onChange={(e) => setTargetAudience(e.target.value)}
                            placeholder="e.g., Small business owners, Tech enthusiasts..."
                            className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Keywords (comma-separated)
                          </label>
                          <input
                            type="text"
                            value={keywords}
                            onChange={(e) => setKeywords(e.target.value)}
                            placeholder="e.g., AI, productivity, marketing..."
                            className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-300 mb-2">
                            Source URL (optional)
                          </label>
                          <div className="relative">
                            <input
                              type="url"
                              value={sourceUrl}
                              onChange={(e) => setSourceUrl(e.target.value)}
                              placeholder="https://example.com/article-to-reference..."
                              className="w-full px-4 py-3 pr-12 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"
                            />
                            {sourceUrl && (
                              <button
                                type="button"
                                onClick={handleUrlScrape}
                                disabled={isScrapingUrl}
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-emerald-400 hover:text-emerald-300 disabled:opacity-50"
                                title="Extract content from URL"
                              >
                                {isScrapingUrl ? (
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-emerald-400"></div>
                                ) : (
                                  <Zap className="w-4 h-4" />
                                )}
                              </button>
                            )}
                          </div>
                          {scrapedContent && (
                            <div className="mt-2 p-3 bg-emerald-500/10 border border-emerald-500/20 rounded-lg">
                              <p className="text-emerald-300 text-xs mb-1">✓ Content extracted from URL</p>
                              <p className="text-gray-300 text-sm line-clamp-2">{scrapedContent.substring(0, 150)}...</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Column - Configuration */}
                  <div className="space-y-6">
                    {/* Content Type */}
                    <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                      <h3 className="text-lg font-semibold text-white mb-4">Content Type</h3>
                      <div className="grid grid-cols-2 gap-3">
                        {CONTENT_TYPES.map((type) => (
                          <button
                            key={type.id}
                            onClick={() => setContentType(type.id)}
                            className={`p-3 rounded-lg border text-left transition-all ${
                              contentType === type.id
                                ? 'bg-emerald-600/20 border-emerald-500/50 text-white'
                                : 'bg-white/5 border-white/20 text-gray-400 hover:bg-white/10'
                            }`}
                          >
                            <div className="text-lg mb-1">{type.icon}</div>
                            <div className="text-sm font-medium">{type.label}</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Tone */}
                    <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                      <h3 className="text-lg font-semibold text-white mb-4">Tone</h3>
                      <div className="grid grid-cols-2 gap-2">
                        {TONES.map((toneOption) => (
                          <button
                            key={toneOption.id}
                            onClick={() => setTone(toneOption.id)}
                            className={`p-2 rounded-lg border text-center transition-all ${
                              tone === toneOption.id
                                ? 'bg-emerald-600/20 border-emerald-500/50 text-white'
                                : 'bg-white/5 border-white/20 text-gray-400 hover:bg-white/10'
                            }`}
                          >
                            <div className="text-sm">{toneOption.emoji}</div>
                            <div className="text-xs font-medium">{toneOption.label}</div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Options */}
                    <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
                      <h3 className="text-lg font-semibold text-white mb-4">Options</h3>
                      <div className="space-y-3">
                        <label className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={includeHashtags}
                            onChange={(e) => setIncludeHashtags(e.target.checked)}
                            className="w-4 h-4 text-emerald-600 bg-white/10 border-white/20 rounded focus:ring-emerald-500/50"
                          />
                          <span className="text-gray-300">Include hashtags</span>
                        </label>
                        <label className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={includeEmojis}
                            onChange={(e) => setIncludeEmojis(e.target.checked)}
                            className="w-4 h-4 text-emerald-600 bg-white/10 border-white/20 rounded focus:ring-emerald-500/50"
                          />
                          <span className="text-gray-300">Include emojis</span>
                        </label>
                        <label className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={includeCTA}
                            onChange={(e) => setIncludeCTA(e.target.checked)}
                            className="w-4 h-4 text-emerald-600 bg-white/10 border-white/20 rounded focus:ring-emerald-500/50"
                          />
                          <span className="text-gray-300">Include call-to-action</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Generate Button */}
                <div className="text-center">
                  <motion.button
                    onClick={handleGenerate}
                    disabled={!contentInput.trim() || selectedPlatforms.length === 0 || isGenerating}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="px-8 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center space-x-2 mx-auto"
                  >
                    {isGenerating ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Generating Content...</span>
                      </>
                    ) : (
                      <>
                        <Zap className="w-5 h-5" />
                        <span>Generate Social Media Content</span>
                      </>
                    )}
                  </motion.button>
                </div>
              </motion.div>
            )}

            {activeTab === 'preview' && (
              <motion.div
                key="preview"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                className="space-y-6"
              >
                {Object.keys(generatedContent).length > 0 ? (
                  <>
                    {/* Action Bar */}
                    <div className="flex items-center justify-between bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4">
                      <div className="flex items-center space-x-4">
                        <div className="text-white">
                          <span className="font-semibold">{Object.keys(generatedContent).length}</span>
                          <span className="text-gray-400 ml-1">platform{Object.keys(generatedContent).length !== 1 ? 's' : ''} {isViewMode ? 'in this post' : 'generated'}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        {isViewMode && (
                          <Link href="/content">
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                            >
                              <ArrowLeft className="w-4 h-4" />
                              <span>Back to Library</span>
                            </motion.button>
                          </Link>
                        )}
                        {!isViewMode && (
                          <motion.button
                            onClick={saveContent}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="flex items-center space-x-2 px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg transition-colors"
                          >
                            <Download className="w-4 h-4" />
                            <span>Save to Library</span>
                          </motion.button>
                        )}
                      </div>
                    </div>

                    {/* Generated Content */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {Object.entries(generatedContent).map(([platform, data]) => {
                        const platformConfig = PLATFORMS[platform as keyof typeof PLATFORMS]
                        return (
                          <motion.div
                            key={platform}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
                          >
                            {/* Platform Header */}
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center space-x-3">
                                <div className={`p-2 rounded-lg bg-gradient-to-r ${platformConfig.color}`}>
                                  <span className="text-white text-lg">{platformConfig.icon}</span>
                                </div>
                                <div>
                                  <h3 className="text-lg font-semibold text-white">{platformConfig.name}</h3>
                                  <p className="text-xs text-gray-400">
                                    {data.characterCount} / {platformConfig.maxLength} characters
                                  </p>
                                </div>
                              </div>
                              <button
                                onClick={() => copyToClipboard(platform, data.content)}
                                className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                                title="Copy to clipboard"
                              >
                                {copied === platform ? <Check className="w-4 h-4 text-green-400" /> : <Copy className="w-4 h-4" />}
                              </button>
                            </div>

                            {/* Character Count Indicator */}
                            <div className="mb-4">
                              <div className="flex items-center justify-between text-xs mb-1">
                                <span className={getCharacterColor(data.characterCount, platform)}>
                                  {data.characterCount} characters
                                </span>
                                <span className="text-gray-500">
                                  {data.characterCount <= platformConfig.optimalLength.max ? 'Optimal' : 'Long'}
                                </span>
                              </div>
                              <div className="w-full bg-white/10 rounded-full h-1">
                                <div
                                  className={`h-1 rounded-full transition-all ${
                                    data.characterCount > platformConfig.maxLength
                                      ? 'bg-red-500'
                                      : data.characterCount > platformConfig.optimalLength.max
                                      ? 'bg-yellow-500'
                                      : 'bg-green-500'
                                  }`}
                                  style={{
                                    width: `${Math.min(100, (data.characterCount / platformConfig.maxLength) * 100)}%`
                                  }}
                                />
                              </div>
                            </div>

                            {/* Content Preview */}
                            <div className="bg-black/40 rounded-xl p-4 mb-4">
                              <div className="text-gray-300 whitespace-pre-wrap leading-relaxed">
                                {data.content}
                              </div>
                            </div>

                            {/* Hashtags */}
                            {data.hashtags && data.hashtags.length > 0 && (
                              <div className="mb-4">
                                <h4 className="text-sm font-medium text-gray-300 mb-2 flex items-center">
                                  <Hash className="w-4 h-4 mr-1" />
                                  Suggested Hashtags
                                </h4>
                                <div className="flex flex-wrap gap-2">
                                  {data.hashtags.map((hashtag, index) => (
                                    <span
                                      key={index}
                                      className="px-2 py-1 bg-emerald-600/20 border border-emerald-500/30 rounded-lg text-xs text-emerald-300"
                                    >
                                      #{hashtag}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}

                            {/* Engagement Score */}
                            {data.engagementScore && (
                              <div className="flex items-center space-x-2 text-xs">
                                <TrendingUp className="w-4 h-4 text-emerald-400" />
                                <span className="text-gray-300">Engagement Score:</span>
                                <span className="text-emerald-400 font-medium">{data.engagementScore}/100</span>
                              </div>
                            )}
                          </motion.div>
                        )
                      })}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-20">
                    <MessageSquare className="w-16 h-16 text-gray-600 mx-auto mb-6" />
                    <h3 className="text-xl font-semibold text-white mb-2">
                      {isViewMode ? 'No content to display' : 'No content generated yet'}
                    </h3>
                    <p className="text-gray-400 mb-8">
                      {isViewMode 
                        ? 'There was an issue loading the social media content'
                        : 'Create your content in the previous tab to see previews here'
                      }
                    </p>
                    {isViewMode ? (
                      <Link href="/content">
                        <button className="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
                          Back to Content Library
                        </button>
                      </Link>
                    ) : (
                      <button
                        onClick={() => setActiveTab('input')}
                        className="px-6 py-3 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg transition-colors"
                      >
                        Go to Content Creator
                      </button>
                    )}
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  )
}