'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { 
  ArrowLeft, 
  Copy, 
  Download, 
  Share, 
  Clock, 
  Sparkles,
  ExternalLink,
  Search,
  Target,
  Zap,
  TrendingUp,
  FileText,
  Award,
  Shield,
  ChevronRight,
  Eye,
  Calendar,
  User,
  Globe,
  Check,
  Bookmark,
  Share2,
  Heart,
  MessageCircle,
  Star,
  Crown,
  Brain,
  BarChart,
  Lightbulb,
  X as CloseIcon
} from 'lucide-react';
import Link from 'next/link';
import { cn, safeDecodeURIComponent } from '@/lib/utils';

interface ContentScoring {
  seoScore: number;
  aeoScore: number;
  geoScore: number;
  readabilityScore: number;
  uniquenessScore: number;
  externalLinkingScore: number;
  overallScore: number;
  recommendations: string[];
}

export default function ArticleViewPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [article, setArticle] = useState('');
  const [title, setTitle] = useState('AI Generated Article');
  const [scores, setScores] = useState<ContentScoring | null>(null);
  const [copied, setCopied] = useState(false);
  const [loading, setLoading] = useState(true);
  const [readingProgress, setReadingProgress] = useState(0);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [showLargeArticleNotice, setShowLargeArticleNotice] = useState(false);

  // Handle scroll progress - moved to top level
  useEffect(() => {
    const handleScroll = () => {
      const totalHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = (window.scrollY / totalHeight) * 100;
      setReadingProgress(Math.min(progress, 100));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle authentication redirect - moved to top level
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Handle content loading and URL parameters - moved to top level
  useEffect(() => {
    // Handle URL parameters and migrate to clean URLs
    const urlParams = new URLSearchParams(window.location.search);
    
    if (urlParams.toString()) {
      const articleFromUrl = urlParams.get('article');
      const titleFromUrl = urlParams.get('title');
      const scoresFromUrl = urlParams.get('scores');
      
      if (articleFromUrl) {
        // Safely decode URI parameters with automatic fallback
        const decodedArticle = safeDecodeURIComponent(articleFromUrl);
        const decodedTitle = safeDecodeURIComponent(titleFromUrl || '', 'AI Generated Article');
        const decodedScores = scoresFromUrl ? safeDecodeURIComponent(scoresFromUrl) : null;
        
        // Set state immediately for direct rendering
        setArticle(decodedArticle);
        setTitle(decodedTitle);
        setLoading(false);
        
        // Also attempt migration to clean URL system
        console.log('🔄 Migrating from legacy URL to clean URL...');
        
        // Store article using new API and redirect to clean URL
        const storeAndRedirect = async () => {
          try {
            const metadata: any = {};
            if (decodedScores) {
              try {
                metadata.scores = JSON.parse(decodedScores);
              } catch (error) {
                console.warn('Failed to parse scores for migration:', error);
              }
            }

            const response = await fetch('/api/articles/store', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                title: decodedTitle,
                content: decodedArticle,
                type: 'blog', // Default type for legacy content
                metadata: Object.keys(metadata).length > 0 ? metadata : undefined,
              }),
            });

            const data = await response.json();

            if (response.ok && data.success && data.url) {
              console.log('✅ Article stored with clean URL:', data.url);
              
              // Clear localStorage to avoid conflicts
              localStorage.removeItem('generatedArticle');
              localStorage.removeItem('articleTitle');
              localStorage.removeItem('articleScores');
              
              // Redirect to clean URL
              router.push(data.url);
              return;
            } else {
              throw new Error(data.error || 'Failed to store article');
            }
          } catch (error) {
            console.error('❌ Migration failed, using fallback:', error);
            
            // Fallback: store in localStorage as before
            localStorage.setItem('generatedArticle', decodedArticle);
            localStorage.setItem('articleTitle', decodedTitle);
            
            if (decodedScores) {
              localStorage.setItem('articleScores', decodedScores);
            }
            
            window.history.replaceState({}, '', '/article-view');
          }
        };

        // Perform migration
        storeAndRedirect();
        return; // Exit early during migration
      }
    }
    
    // Check for VideoAlchemy result first (newest)
    const videoAlchemyResult = sessionStorage.getItem('videoAlchemyResult');
    let savedArticle = localStorage.getItem('generatedArticle');
    let savedTitle = localStorage.getItem('articleTitle');
    let savedScores = localStorage.getItem('articleScores');
    
    // If we have VideoAlchemy result, use that first
    if (videoAlchemyResult) {
      try {
        const result = JSON.parse(videoAlchemyResult);
        if (result.success && result.article) {
          savedArticle = result.article.content;
          savedTitle = result.article.title;
          
          // Create enhanced scores for VideoAlchemy content
          const mockScores = {
            seoScore: 88 + Math.random() * 10, // High SEO score for video-based content
            aeoScore: 85 + Math.random() * 12, // Excellent AEO from video transcripts
            geoScore: 82 + Math.random() * 15, // Good GEO optimization
            readabilityScore: 90 + Math.random() * 8, // Human-like from video speech
            uniquenessScore: 92 + Math.random() * 6, // Very high uniqueness from video source
            externalLinkingScore: 75 + Math.random() * 20,
            overallScore: 88 + Math.random() * 10,
            recommendations: [
              `Successfully processed ${result.article.metadata?.processedVideos || 0} videos`,
              'Excellent SEO/AEO/GEO optimization applied',
              'Human-like conversational tone from video transcripts',
              `Generated in ${result.article.metadata?.language || 'English'} language`,
              `${result.article.metadata?.wordCount || 'N/A'} words of comprehensive content`
            ]
          };
          savedScores = JSON.stringify(mockScores);
          
          // Store in localStorage for persistence with quota handling
          if (savedArticle) {
            try {
              localStorage.setItem('generatedArticle', savedArticle);
            } catch (error) {
              if (error instanceof DOMException && error.name === 'QuotaExceededError') {
                console.warn('Article too large for localStorage, using compression...');
                try {
                  // Store more content for larger articles (up to 20k words ≈ 100k chars)
                  const truncatedArticle = savedArticle.substring(0, 15000) + '\n\n[Article truncated due to size - full version available in session]';
                  localStorage.setItem('generatedArticle', truncatedArticle);
                  localStorage.setItem('fullArticleInSession', 'true');
                  // Keep full article in sessionStorage temporarily
                  sessionStorage.setItem('fullArticleContent', savedArticle);
                } catch (compressionError) {
                  console.error('Failed to store even compressed article:', compressionError);
                  // Store minimal info and let component handle it
                  localStorage.setItem('articleStatus', 'large-article');
                  sessionStorage.setItem('fullArticleContent', savedArticle);
                }
              } else {
                console.error('Failed to store article:', error);
              }
            }
          }
          if (savedTitle) {
            try {
              localStorage.setItem('articleTitle', savedTitle);
            } catch (error) {
              console.warn('Failed to store title:', error);
            }
          }
          if (savedScores) {
            try {
              localStorage.setItem('articleScores', savedScores);
            } catch (error) {
              console.warn('Failed to store scores:', error);
            }
          }
          
          // Clear the session storage
          sessionStorage.removeItem('videoAlchemyResult');
        }
      } catch (error) {
        console.error('Failed to parse VideoAlchemy result:', error);
      }
    }
    
    // Check for full article in session storage first
    const fullArticleContent = sessionStorage.getItem('fullArticleContent');
    const fullArticleInSession = localStorage.getItem('fullArticleInSession');
    
    let finalArticle = savedArticle;
    
    // If we have a full article in session storage, use that
    if (fullArticleContent && fullArticleInSession === 'true') {
      console.log('Using full article from session storage');
      finalArticle = fullArticleContent;
      setShowLargeArticleNotice(true); // Show notice that this was a large article
      // Clean up the session storage after loading
      sessionStorage.removeItem('fullArticleContent');
      localStorage.removeItem('fullArticleInSession');
    }
    
    if (finalArticle) {
      setArticle(finalArticle);
      
      // Extract title from article if not in localStorage
      let displayTitle = savedTitle || 'AI Generated Article';
      if (!savedTitle || savedTitle === 'AI Generated Article') {
        const extractedTitle = extractTitleFromContent(finalArticle);
        if (extractedTitle) {
          displayTitle = extractedTitle;
          try {
            localStorage.setItem('articleTitle', displayTitle);
          } catch (error) {
            console.warn('Failed to update title in localStorage:', error);
          }
        }
      }
      setTitle(displayTitle);
      
      if (savedScores) {
        try {
          setScores(JSON.parse(savedScores));
        } catch (error) {
          console.error('Failed to parse scores:', error);
        }
      }
    } else {
      // No article found, redirect to dashboard
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 3000);
    }
    
    setLoading(false);
  }, []);

  // Extract title from markdown content
  const extractTitleFromContent = (content: string): string | null => {
    const lines = content.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('# ')) {
        return trimmed.substring(2).trim();
      }
    }
    return null;
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(article);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const downloadArticle = () => {
    const blob = new Blob([article], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/\s+/g, '-').toLowerCase()}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const getScoreColor = (score: number) => {
    if (score >= 85) return 'from-emerald-500 to-green-500';
    if (score >= 70) return 'from-blue-500 to-cyan-500';
    if (score >= 50) return 'from-amber-500 to-orange-500';
    return 'from-red-500 to-rose-500';
  };

  const getScoreGrade = (score: number) => {
    if (score >= 90) return 'A+';
    if (score >= 85) return 'A';
    if (score >= 80) return 'A-';
    if (score >= 75) return 'B+';
    if (score >= 70) return 'B';
    if (score >= 65) return 'B-';
    if (score >= 60) return 'C+';
    if (score >= 55) return 'C';
    return 'D';
  };

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading article...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        {/* Animated Background */}
        <div className="fixed inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10" />
          <motion.div
            animate={{
              x: [0, 100, 0],
              y: [0, -100, 0],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]"
          />
        </div>
        
        <div className="relative z-10 text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-16 h-16 border-4 border-violet-600 border-t-transparent rounded-full mx-auto mb-4"
          />
          <h2 className="text-xl font-semibold text-white mb-2">Loading Article</h2>
          <p className="text-gray-400">Preparing your superior content...</p>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        {/* Animated Background */}
        <div className="fixed inset-0 z-0">
          <div className="absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10" />
        </div>
        
        <div className="relative z-10 text-center">
          <div className="p-3 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-2xl mb-6 w-fit mx-auto">
            <FileText className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-xl font-semibold text-white mb-2">No Article Found</h2>
          <p className="text-gray-400 mb-6">No generated article found in your session.</p>
          <Link
            href="/dashboard"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-violet-600 to-indigo-600 text-white rounded-xl hover:scale-105 transition-transform"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Go to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  const wordCount = article.split(/\s+/).filter(word => word.length > 0).length;
  const readingTime = Math.ceil(wordCount / 200);

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10" />
        
        {/* Animated orbs */}
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]"
        />
      </div>

      {/* Reading Progress Bar */}
      <motion.div 
        className="fixed top-0 left-0 h-1 bg-gradient-to-r from-violet-600 to-indigo-600 z-50"
        style={{ width: `${readingProgress}%` }}
      />

      {/* Modern Header */}
      <motion.header 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40"
      >
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            {/* Left Side */}
            <div className="flex items-center space-x-6">
              <Link 
                href="/dashboard" 
                className="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group"
              >
                <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
                <span className="font-medium">Back to Dashboard</span>
              </Link>
              
              <div className="h-6 w-px bg-white/20" />
              
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg opacity-70" />
                  <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                    <Crown className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">Superior Article</h1>
                  <p className="text-sm text-gray-400">AI Generated Content</p>
                </div>
              </div>
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-400">
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>{new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>{readingTime} min read</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FileText className="w-4 h-4" />
                  <span>{wordCount.toLocaleString()} words</span>
                </div>
              </div>
              
              <div className="h-6 w-px bg-white/20" />
              
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setIsBookmarked(!isBookmarked)}
                  className={cn(
                    "p-2.5 rounded-lg transition-colors",
                    isBookmarked ? 'bg-violet-600/20 text-violet-400' : 'text-gray-400 hover:text-white hover:bg-white/10'
                  )}
                >
                  <Bookmark className={`w-5 h-5 ${isBookmarked ? 'fill-current' : ''}`} />
                </button>
                
                <button
                  onClick={() => setIsLiked(!isLiked)}
                  className={cn(
                    "p-2.5 rounded-lg transition-colors",
                    isLiked ? 'bg-red-600/20 text-red-400' : 'text-gray-400 hover:text-white hover:bg-white/10'
                  )}
                >
                  <Heart className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
                </button>
                
                <button
                  onClick={copyToClipboard}
                  className="flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg hover:bg-white/20 transition-all"
                >
                  {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                  <span>{copied ? 'Copied' : 'Copy'}</span>
                </button>
                
                <motion.button
                  onClick={downloadArticle}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg hover:shadow-lg transition-all"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </motion.button>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Large Article Notice */}
      {showLargeArticleNotice && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative z-10 max-w-6xl mx-auto px-6 pt-6"
        >
          <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-blue-500/30 rounded-xl p-4 mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600/20 rounded-lg">
                <FileText className="w-5 h-5 text-blue-400" />
              </div>
              <div className="flex-1">
                <h4 className="text-white font-medium">Large Article Generated</h4>
                <p className="text-sm text-gray-300">
                  This article exceeded normal storage limits due to its comprehensive length. 
                  Your full content has been successfully loaded and is ready for viewing.
                </p>
              </div>
              <button
                onClick={() => setShowLargeArticleNotice(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <CloseIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Main Content */}
      <main className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Article Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8">
            <Sparkles className="w-4 h-4 text-violet-400" />
            <span className="text-sm text-gray-200">AI Generated</span>
            <Crown className="w-4 h-4 text-yellow-400" />
          </div>
          
          <div className="flex flex-wrap justify-center gap-3 mb-8">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-violet-600/20 text-violet-300 border border-violet-500/30">
              <Sparkles className="w-4 h-4 mr-2" />
              AI Generated
            </span>
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-emerald-600/20 text-emerald-300 border border-emerald-500/30">
              <Shield className="w-4 h-4 mr-2" />
              SEO Optimized
            </span>
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-amber-600/20 text-amber-300 border border-amber-500/30">
              <Award className="w-4 h-4 mr-2" />
              Superior Quality
            </span>
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight mb-6">
            {title}
          </h1>
          
          <div className="flex items-center justify-center space-x-8 mb-8">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full blur-md opacity-70" />
                <div className="relative w-12 h-12 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full flex items-center justify-center">
                  <Brain className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="text-left">
                <p className="text-sm font-medium text-white">AI Content Generator</p>
                <p className="text-xs text-gray-400">AI Content Agent</p>
              </div>
            </div>
            
            <div className="h-8 w-px bg-white/20" />
            
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>{readingTime} min read</span>
              </div>
              <div className="flex items-center space-x-2">
                <FileText className="w-4 h-4" />
                <span>{wordCount.toLocaleString()} words</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quality Scores */}
        {scores && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8 mb-12"
          >
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg">
                  <BarChart className="w-5 h-5 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">Content Quality Analysis</h3>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-400">Overall Grade:</span>
                <span className={`text-3xl font-bold bg-gradient-to-r ${getScoreColor(scores.overallScore)} bg-clip-text text-transparent`}>
                  {getScoreGrade(scores.overallScore)}
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
              {[
                { label: 'SEO Score', score: scores.seoScore, icon: Search, color: 'violet' },
                { label: 'AEO Score', score: scores.aeoScore, icon: MessageCircle, color: 'blue' },
                { label: 'GEO Score', score: scores.geoScore, icon: Globe, color: 'emerald' },
                { label: 'Readability', score: scores.readabilityScore, icon: Eye, color: 'amber' },
                { label: 'Uniqueness', score: scores.uniquenessScore, icon: Sparkles, color: 'pink' },
                { label: 'External Links', score: scores.externalLinkingScore, icon: ExternalLink, color: 'cyan' }
              ].map((metric, idx) => (
                <motion.div
                  key={metric.label}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.05 * idx }}
                  className="text-center p-4 rounded-xl bg-white/5 border border-white/10 hover:bg-white/10 transition-all"
                >
                  <div className="relative mb-4">
                    <svg className="w-24 h-24 mx-auto transform -rotate-90">
                      <circle
                        cx="48"
                        cy="48"
                        r="40"
                        stroke="currentColor"
                        strokeWidth="6"
                        fill="none"
                        className="text-white/20"
                      />
                      <circle
                        cx="48"
                        cy="48"
                        r="40"
                        stroke={`url(#gradient-${idx})`}
                        strokeWidth="6"
                        fill="none"
                        strokeDasharray={`${(metric.score / 100) * 251.33} 251.33`}
                        strokeLinecap="round"
                        className="transition-all duration-1000"
                      />
                      <defs>
                        <linearGradient id={`gradient-${idx}`}>
                          <stop offset="0%" stopColor={`var(--${metric.color}-500)`} />
                          <stop offset="100%" stopColor={`var(--${metric.color}-400)`} />
                        </linearGradient>
                      </defs>
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-white">{Math.round(metric.score)}</p>
                        <p className="text-xs text-gray-400">/ 100</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className={cn(
                      "inline-flex p-2 rounded-lg",
                      metric.color === 'violet' && "bg-violet-600/20",
                      metric.color === 'blue' && "bg-blue-600/20",
                      metric.color === 'emerald' && "bg-emerald-600/20",
                      metric.color === 'amber' && "bg-amber-600/20",
                      metric.color === 'pink' && "bg-pink-600/20",
                      metric.color === 'cyan' && "bg-cyan-600/20"
                    )}>
                      <metric.icon className={cn(
                        "w-4 h-4",
                        metric.color === 'violet' && "text-violet-400",
                        metric.color === 'blue' && "text-blue-400",
                        metric.color === 'emerald' && "text-emerald-400",
                        metric.color === 'amber' && "text-amber-400",
                        metric.color === 'pink' && "text-pink-400",
                        metric.color === 'cyan' && "text-cyan-400"
                      )} />
                    </div>
                    <p className="text-sm font-medium text-white">{metric.label}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Article Content */}
        <motion.article
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden"
        >
          <div className="prose prose-lg prose-invert max-w-none p-8 md:p-12">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                h1: ({ children }) => (
                  <h1 className="text-3xl md:text-4xl font-bold text-white mb-6 mt-8 first:mt-0">
                    {children}
                  </h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-4 mt-10 pb-3 border-b border-white/20">
                    {children}
                  </h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-xl md:text-2xl font-semibold text-gray-200 mb-3 mt-8">
                    {children}
                  </h3>
                ),
                p: ({ children }) => (
                  <p className="text-gray-300 leading-relaxed mb-6 text-base md:text-lg">
                    {children}
                  </p>
                ),
                ul: ({ children }) => (
                  <ul className="text-gray-300 mb-6 ml-6 space-y-3 list-disc list-outside">
                    {children}
                  </ul>
                ),
                ol: ({ children }) => (
                  <ol className="text-gray-300 mb-6 ml-6 space-y-3 list-decimal list-outside">
                    {children}
                  </ol>
                ),
                li: ({ children }) => (
                  <li className="text-gray-300 leading-relaxed">
                    {children}
                  </li>
                ),
                strong: ({ children }) => (
                  <strong className="font-semibold text-white">
                    {children}
                  </strong>
                ),
                em: ({ children }) => (
                  <em className="italic text-gray-300">
                    {children}
                  </em>
                ),
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-violet-500 pl-6 my-8 text-gray-300 italic bg-white/5 py-4 rounded-r-lg">
                    {children}
                  </blockquote>
                ),
                code: ({ children }) => (
                  <code className="bg-slate-100 text-slate-800 px-2 py-1 rounded text-sm font-mono">
                    {children}
                  </code>
                ),
                pre: ({ children }) => (
                  <pre className="bg-slate-900 text-slate-100 p-4 rounded-lg overflow-x-auto my-6">
                    {children}
                  </pre>
                ),
                table: ({ children }) => (
                  <div className="overflow-x-auto my-8 rounded-2xl shadow-2xl border border-white/20 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm">
                    <table className="min-w-full border-collapse">
                      {children}
                    </table>
                  </div>
                ),
                thead: ({ children }) => (
                  <thead className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20">
                    {children}
                  </thead>
                ),
                tbody: ({ children }) => (
                  <tbody className="divide-y divide-white/10">
                    {children}
                  </tbody>
                ),
                tr: ({ children }) => (
                  <tr className="hover:bg-white/5 transition-all duration-200">
                    {children}
                  </tr>
                ),
                th: ({ children }) => (
                  <th className="px-6 py-4 text-left font-semibold text-white border-r border-white/10 last:border-r-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 relative">
                    <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                    {children}
                  </th>
                ),
                td: ({ children }) => (
                  <td className="px-6 py-4 text-gray-200 border-r border-white/10 last:border-r-0 font-medium">
                    {children}
                  </td>
                ),
                a: ({ href, children }) => (
                  <a
                    href={href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-indigo-600 hover:text-indigo-700 font-medium underline decoration-1 underline-offset-2 inline-flex items-center gap-1 transition-colors"
                  >
                    {children}
                    <ExternalLink className="w-3.5 h-3.5" />
                  </a>
                ),
              }}
            >
              {article}
            </ReactMarkdown>
          </div>
        </motion.article>

        {/* Article Footer */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-16 text-center"
        >
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8 mb-8">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="p-2 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-lg">
                <TrendingUp className="w-5 h-5 text-white" />
              </div>
              <span className="text-lg font-semibold text-white">Superior Content Quality</span>
            </div>
            <p className="text-gray-300 mb-6">
              This article was generated using our advanced AI system, 
              competitive analysis, and human writing patterns for maximum impact.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 rounded-xl bg-white/5 border border-white/10">
                <Shield className="w-8 h-8 text-emerald-400 mx-auto mb-2" />
                <p className="text-sm font-medium text-white">SEO Optimized</p>
                <p className="text-xs text-gray-400">Built for search visibility</p>
              </div>
              <div className="text-center p-4 rounded-xl bg-white/5 border border-white/10">
                <Brain className="w-8 h-8 text-violet-400 mx-auto mb-2" />
                <p className="text-sm font-medium text-white">AI-Powered</p>
                <p className="text-xs text-gray-400">Advanced reasoning model</p>
              </div>
              <div className="text-center p-4 rounded-xl bg-white/5 border border-white/10">
                <Crown className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                <p className="text-sm font-medium text-white">Superior Quality</p>
                <p className="text-xs text-gray-400">Beats all competition</p>
              </div>
            </div>
          </div>
          
          <Link href="/dashboard">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-violet-600 to-indigo-600 text-white font-semibold rounded-xl hover:shadow-2xl transition-all"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Dashboard</span>
              <Sparkles className="w-5 h-5" />
            </motion.button>
          </Link>
        </motion.div>
      </main>
    </div>
  );
} 