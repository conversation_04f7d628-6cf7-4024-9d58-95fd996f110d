import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params as required by Next.js 15
    const { id } = await params

    // Get content from cache
    const cache = (global as any).contentCache || new Map()
    const contentData = cache.get(id)

    if (!contentData) {
      return NextResponse.json(
        { error: 'Content not found or expired' },
        { status: 404 }
      )
    }

    // Check if content is expired (older than 1 hour)
    const oneHourAgo = Date.now() - (60 * 60 * 1000)
    if (contentData.createdAt < oneHourAgo) {
      cache.delete(id)
      return NextResponse.json(
        { error: 'Content expired' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      content: contentData.content,
      title: contentData.title,
      createdAt: contentData.createdAt
    })

  } catch (error) {
    console.error('Error fetching content:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
