import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { IdeogramService } from '@/lib/ideogram'
import { imageStorageService } from '@/lib/image-storage'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { headingText, context, blogTitle, contentId } = await request.json()

    if (!headingText) {
      return NextResponse.json(
        { error: 'Heading text is required' },
        { status: 400 }
      )
    }

    console.log(`🎨 Generating image for heading: "${headingText}"`)

    // Initialize Ideogram service (will use environment variable)
    const ideogramService = new IdeogramService()

    // Generate image for the heading with enhanced prompts
    const imageUrl = await ideogramService.generateBlogHeadingImage(
      headingText,
      context,
      blogTitle
    )

    console.log(`✅ Image generated successfully: ${imageUrl}`)

    // Store image locally and convert to WebP
    let storedImage = null
    try {
      console.log(`💾 Storing image locally: ${headingText}`)
      storedImage = await imageStorageService.storeImage(
        imageUrl,
        headingText,
        contentId
      )
      console.log(`✅ Image stored locally: ${storedImage.localPath}`)
    } catch (storageError) {
      console.warn('Failed to store image locally, using original URL:', storageError)
      // Continue with original URL if storage fails
    }

    return NextResponse.json({
      success: true,
      imageUrl: storedImage ? storedImage.localPath : imageUrl, // Use local path if available
      originalUrl: imageUrl, // Keep original URL as backup
      localPath: storedImage?.localPath,
      storedImageId: storedImage?.id,
      headingText,
      imageMetadata: storedImage ? {
        width: storedImage.width,
        height: storedImage.height,
        size: storedImage.size,
        filename: storedImage.filename
      } : null
    })

  } catch (error) {
    console.error('❌ Image generation failed:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Image generation failed'
    
    return NextResponse.json(
      { 
        error: errorMessage,
        success: false 
      },
      { status: 500 }
    )
  }
}
