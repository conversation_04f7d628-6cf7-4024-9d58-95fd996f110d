import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

interface StoreArticleRequest {
  title: string;
  content: string;
  type: string;
  metadata?: any;
  tone?: string;
  language?: string;
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: StoreArticleRequest = await request.json();
    const { title, content, type, metadata, tone, language } = body;

    // Validate required fields
    if (!title || !content || !type) {
      return NextResponse.json({ 
        error: 'Missing required fields: title, content, and type are required' 
      }, { status: 400 });
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Ensure content is a string and calculate word count
    const contentString = typeof content === 'string' ? content : 
                         typeof content === 'object' ? JSON.stringify(content) : 
                         String(content);
    
    // Validate content string is not empty
    if (!contentString || contentString.trim() === '') {
      return NextResponse.json({ 
        error: 'Content cannot be empty' 
      }, { status: 400 });
    }
    
    const wordCount = contentString.split(/\s+/).filter(word => word.length > 0).length;

    // Store the article
    const article = await prisma.content.create({
      data: {
        userId: user.id,
        title: title,
        content: contentString,
        type: type,
        metadata: metadata ? JSON.stringify(metadata) : null,
        wordCount: wordCount,
        tone: tone || 'professional',
        language: language || 'en',
        status: 'published'
      }
    });

    return NextResponse.json({
      success: true,
      article: {
        id: article.id,
        title: article.title,
        type: article.type,
        wordCount: article.wordCount,
        createdAt: article.createdAt
      },
      url: `/article-view/${article.id}`
    });

  } catch (error) {
    console.error('Error storing article:', error);
    return NextResponse.json({ 
      error: 'Failed to store article' 
    }, { status: 500 });
  }
} 