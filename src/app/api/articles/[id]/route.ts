import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    // Find the article by ID
    const article = await prisma.content.findFirst({
      where: {
        id: id,
        user: {
          email: session.user.email
        }
      },
      select: {
        id: true,
        title: true,
        content: true,
        metadata: true,
        type: true,
        wordCount: true,
        tone: true,
        language: true,
        createdAt: true,
        updatedAt: true,
        status: true,
        images: {
          select: {
            id: true,
            localPath: true,
            originalUrl: true,
            filename: true,
            headingText: true,
            width: true,
            height: true,
            size: true,
            createdAt: true
          }
        }
      }
    });

    if (!article) {
      return NextResponse.json({ 
        error: 'Article not found or access denied' 
      }, { status: 404 });
    }

    // Parse metadata if it exists
    let parsedMetadata = null;
    if (article.metadata) {
      try {
        parsedMetadata = JSON.parse(article.metadata);
      } catch (error) {
        console.warn('Failed to parse article metadata:', error);
      }
    }

    return NextResponse.json({
      success: true,
      article: {
        ...article,
        metadata: parsedMetadata
      }
    });

  } catch (error) {
    console.error('Error retrieving article:', error);
    return NextResponse.json({ 
      error: 'Failed to retrieve article' 
    }, { status: 500 });
  }
} 