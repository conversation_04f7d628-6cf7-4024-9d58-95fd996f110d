import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { QuotaManager } from '@/lib/quota';
import { TavilySearchService } from '@/lib/search';
import { EnhancedWebExtractor } from '@/lib/services/enhanced-web-extractor';
import { CompetitiveAnalysisEngine } from '@/lib/services/competitive-analysis-engine';
import { KnowledgeBaseService } from '@/lib/services/knowledge-base-service';

/**
 * Enhanced Blog Generator Analysis Endpoint
 * Phase 1: Research, extract, and analyze competitors
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      console.error('Authentication failed - no session or user:', { session, user: session?.user });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const userId = session.user.id || session.user.email || 'temp-user';
    console.log('✅ Authentication successful:', { userId, email: session.user.email });

    // Check quota (skip for test users)
    if (userId !== 'temp-user' && userId !== 'test-user-123') {
      try {
        const quotaCheck = await QuotaManager.checkQuota(userId, 'blog_posts');
        if (!quotaCheck.hasQuota) {
          return NextResponse.json(
            { 
              error: 'Blog post quota exceeded',
              quota: {
                used: quotaCheck.used,
                limit: quotaCheck.limit,
                resetDate: quotaCheck.resetDate
              }
            },
            { status: 429 }
          );
        }
      } catch (quotaError) {
        console.warn('Quota check failed, proceeding anyway:', quotaError);
      }
    }

    const { topic, targetKeyword, numResults = 10 } = await request.json();

    if (!topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      );
    }

    console.log(`🔍 Starting enhanced blog analysis for topic: "${topic}"`);
    
    // Phase 1: Tavily Search
    console.log('📡 Phase 1: Conducting Tavily search...');
    const searchService = new TavilySearchService();
    
    // Use single exact topic search query
    const searchQuery = targetKeyword ? `${targetKeyword} ${topic}` : topic;
    
    let searchResults: any[] = [];
    try {
      const searchResult = await searchService.search(searchQuery, numResults, {
        searchDepth: 'advanced',
        prioritizeRecent: true,
        temporalFocus: 'current'
      });
      searchResults = searchResult.items;
    } catch (searchError) {
      console.error(`Search failed for query "${searchQuery}":`, searchError);
      return NextResponse.json(
        { error: 'Failed to search for topic' },
        { status: 500 }
      );
    }
    
    // Use search results directly
    const uniqueResults = searchResults.slice(0, numResults);
    
    if (uniqueResults.length === 0) {
      return NextResponse.json(
        { error: 'No search results found for the topic' },
        { status: 404 }
      );
    }
    
    console.log(`✅ Phase 1 complete: Found ${uniqueResults.length} unique search results`);
    
    // Phase 2: Enhanced Web Extraction
    console.log('🕷️ Phase 2: Extracting content from top pages...');
    const extractor = EnhancedWebExtractor.getInstance();
    const extractedContent = await extractor.extractMultiple(
      uniqueResults.map(r => r.url),
      {
        maxConcurrent: 3,
        timeout: 15000,
        maxContentLength: 8000
      }
    );
    
    const successfulExtractions = extractedContent.filter(content => content.success);
    console.log(`✅ Phase 2 complete: Successfully extracted ${successfulExtractions.length} pages`);
    
    if (successfulExtractions.length === 0) {
      return NextResponse.json(
        { error: 'Failed to extract content from any pages' },
        { status: 500 }
      );
    }
    
    // Phase 3: Competitive Analysis (skip database storage for now)
    console.log('🏆 Phase 3: Conducting competitive analysis...');
    const analysisEngine = new CompetitiveAnalysisEngine();
    const competitiveReport = await analysisEngine.analyzeCompetitors(
      topic,
      successfulExtractions,
      targetKeyword
    );
    
    // Create summary without database
    const topicSummary = {
      totalEntries: successfulExtractions.length,
      totalWordCount: successfulExtractions.reduce((sum, content) => sum + content.seoData.wordCount, 0),
      averageContentScore: 75, // Mock score
      topSources: successfulExtractions.slice(0, 5).map(content => ({
        url: content.url,
        title: content.title,
        score: 75,
      })),
      competitorCount: competitiveReport.competitors.length,
      lastUpdated: new Date(),
    };
    
    console.log('✅ All phases complete - Analysis ready for content generation');
    
    // Return comprehensive analysis results
    return NextResponse.json({
      success: true,
      phase: 'analysis_complete',
      data: {
        // Search Results Summary
        searchSummary: {
          searchQuery,
          totalResults: uniqueResults.length,
          successfulExtractions: successfulExtractions.length,
        },
        
        // Knowledge Base Summary
        knowledgeBase: {
          entriesStored: successfulExtractions.length,
          topicSummary,
        },
        
        // Competitive Analysis Summary
        competitiveAnalysis: {
          competitorsAnalyzed: competitiveReport.competitors.length,
          averageCompetitorScore: competitiveReport.competitors.reduce((sum, comp) => 
            sum + comp.overallScore, 0) / competitiveReport.competitors.length,
          commonKeywords: competitiveReport.commonKeywords.slice(0, 10),
          contentGaps: competitiveReport.contentGaps.slice(0, 5),
          opportunities: competitiveReport.opportunities.slice(0, 5),
          recommendedWordCount: competitiveReport.recommendedWordCount,
          contentStrategy: competitiveReport.contentStrategy,
        },
        
        // Top Competitors Summary
        topCompetitors: competitiveReport.competitors
          .sort((a, b) => b.overallScore - a.overallScore)
          .slice(0, 5)
          .map(comp => ({
            url: comp.url,
            title: comp.title,
            overallScore: comp.overallScore,
            seoScore: comp.seoScore,
            contentScore: comp.contentScore,
            wordCount: comp.contentStructure.wordCount,
            strengths: comp.contentStrengths.slice(0, 3),
            gaps: comp.contentGaps.slice(0, 3),
          })),
        
        // SEO Insights
        seoInsights: {
          targetKeywordOpportunity: targetKeyword ? {
            competitorsUsingKeyword: competitiveReport.competitors.filter(comp => 
              comp.titleAnalysis.hasTargetKeyword || 
              comp.metaAnalysis.hasTargetKeyword ||
              comp.headingAnalysis.hasTargetKeyword
            ).length,
            averageTitleLength: competitiveReport.competitors.reduce((sum, comp) => 
              sum + comp.titleAnalysis.length, 0) / competitiveReport.competitors.length,
            averageMetaLength: competitiveReport.competitors.reduce((sum, comp) => 
              sum + comp.metaAnalysis.descriptionLength, 0) / competitiveReport.competitors.length,
          } : null,
          commonHeadingPatterns: competitiveReport.commonHeadingPatterns.slice(0, 8),
          recommendedStructure: competitiveReport.recommendedStructure.slice(0, 8),
        },
        
        // Content Recommendations
        contentRecommendations: {
          uniqueAngles: competitiveReport.uniqueAngles.slice(0, 5),
          competitiveAdvantages: competitiveReport.competitiveAdvantages.slice(0, 5),
          contentDepth: competitiveReport.contentStrategy.contentDepth,
          targetAudience: competitiveReport.contentStrategy.targetAudience,
        },
      },
      
      // Metadata for next phase
      metadata: {
        userId,
        topic,
        targetKeyword,
        analysisId: `analysis_${Date.now()}`,
        readyForGeneration: true,
      },
    });

  } catch (error) {
    console.error('Enhanced blog analysis error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to analyze topic',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}