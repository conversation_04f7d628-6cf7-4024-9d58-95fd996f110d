import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { TavilySearchService } from '@/lib/search'
import { NodeWebScraperService } from '@/lib/web-scraper'
import { GeminiService } from '@/lib/gemini'
import { prisma } from '@/lib/prisma'

// Helper function to prioritize official sources
function prioritizeOfficialSources(searchResults: any[]): any[] {
  return searchResults.sort((a, b) => {
    const scoreA = getOfficialSourceScore(a.url)
    const scoreB = getOfficialSourceScore(b.url)
    return scoreB - scoreA // Higher scores first
  })
}

// Helper function to check if article is properly completed
function isArticleComplete(content: string): boolean {
  const trimmedContent = content.trim()

  // Check if ends with proper punctuation
  const endsWithPunctuation = /[.!?]$/.test(trimmedContent)

  // Check if has a conclusion section
  const hasConclusion = /(?:conclusion|summary|final|wrap|end)/i.test(content)

  // Check if doesn't end abruptly (no incomplete sentences)
  const lastSentence = trimmedContent.split(/[.!?]/).pop()?.trim() || ''
  const seemsIncomplete = lastSentence.length > 50 && !endsWithPunctuation

  return endsWithPunctuation && hasConclusion && !seemsIncomplete
}

function getOfficialSourceScore(url: string): number {
  try {
    const domain = new URL(url).hostname.toLowerCase()

    // Official/Primary sources (highest priority)
    const officialDomains = [
      '.gov', '.edu', '.org',
      'github.com', 'docs.', 'developer.',
      'api.', 'support.', 'help.',
      'documentation', 'official'
    ]

    // Company/Product official sites
    const companyDomains = [
      'microsoft.com', 'google.com', 'apple.com', 'amazon.com',
      'meta.com', 'openai.com', 'anthropic.com', 'nvidia.com',
      'adobe.com', 'salesforce.com', 'oracle.com', 'ibm.com'
    ]

    // News/Blog sites (lower priority)
    const newsDomains = [
      'techcrunch.com', 'forbes.com', 'wired.com', 'verge.com',
      'medium.com', 'blog.', 'news.', 'article.'
    ]

    // Check for official domains
    if (officialDomains.some(official => domain.includes(official))) {
      return 100
    }

    // Check for company domains
    if (companyDomains.some(company => domain.includes(company))) {
      return 80
    }

    // Lower score for news/blog sites
    if (newsDomains.some(news => domain.includes(news))) {
      return 20
    }

    // Default score
    return 50
  } catch {
    return 0
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { topic, wordCount = 2000, tone = 'professional' } = await request.json()

    if (!topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      )
    }

    // Create a streaming response
    const encoder = new TextEncoder()
    const stream = new ReadableStream({
      async start(controller) {
        const sendUpdate = (data: any) => {
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`))
        }

        try {
          // Step 1: Search for content
          sendUpdate({ step: 'Searching for relevant content...', progress: 10 })
          
          const searchService = new TavilySearchService()
          const searchResults = await searchService.search(topic, 10, {
            searchDepth: 'advanced',
            prioritizeRecent: true,
            temporalFocus: 'current'
          })

          if (searchResults.items.length === 0) {
            sendUpdate({ error: 'No search results found for the topic' })
            controller.close()
            return
          }

          sendUpdate({ step: 'Found search results, extracting content...', progress: 30 })

          // Step 2: Prioritize official websites and extract content
          const prioritizedUrls = prioritizeOfficialSources(searchResults.items.slice(0, 10))

          const scraper = new NodeWebScraperService()
          const extractedContent = await scraper.scrapeMultipleUrls(
            prioritizedUrls.map(r => r.url),
            {
              timeout: 15000,
              maxLength: 8000
            }
          )

          const successfulExtractions = extractedContent.filter(content => content.success)
          
          if (successfulExtractions.length === 0) {
            sendUpdate({ error: 'Failed to extract content from any pages' })
            controller.close()
            return
          }

          sendUpdate({ step: 'Analyzing competition...', progress: 60 })

          // Step 3: Competitive Analysis with Gemini
          const gemini = new GeminiService()
          
          const competitiveAnalysisPrompt = `
Analyze the following content about "${topic}" and provide a competitive analysis:

${successfulExtractions.map((content, index) => `
Source URL: ${content.url}
Title: ${content.title}
Content: ${content.content.substring(0, 2000)}...
`).join('\n\n')}

Provide:
1. Key themes and topics covered
2. Content gaps and opportunities
3. Unique angles not well covered
4. Writing style patterns
5. Recommended content structure

Keep the analysis concise and actionable.`

          const competitiveAnalysis = await gemini.generateContent(
            competitiveAnalysisPrompt,
            {
              maxOutputTokens: 2000,
              thinkingConfig: {
                thinkingBudget: -1,
                includeThoughts: false
              }
            }
          )

          sendUpdate({ step: 'Generating blog content...', progress: 80 })

          // Step 4: Generate the blog post
          const contentGenerationPrompt = `
You are a world-class professional content writer. Create a comprehensive, engaging blog post about "${topic}".

COMPETITIVE ANALYSIS:
${competitiveAnalysis.response}

EXTRACTED RESEARCH DATA WITH SOURCES:
${successfulExtractions.map((content, index) => `
Source URL: ${content.url}
Source Title: ${content.title}
Key Content: ${content.content.substring(0, 1500)}...
`).join('\n\n')}

REQUIREMENTS:
- CRITICAL: Write EXACTLY ${wordCount} words (±5% tolerance: ${Math.floor(wordCount * 0.95)}-${Math.ceil(wordCount * 1.05)} words)
- Count every single word carefully and ensure you meet this requirement
- Use a ${tone} tone
- Create original, superior content that fills gaps identified in the competitive analysis
- Include data, statistics, and insights from the research
- Structure with clear headings and subheadings
- Make it engaging and valuable to readers
- Reference 2025 trends and current information
- Include actionable takeaways
- COMPLETE the entire article - do not cut off mid-sentence or mid-section

LINKING REQUIREMENTS:
- NEVER use source citations like (source1), (source2), etc.
- NEVER quote other websites or mention source names (avoid phrases like "As described by TechCrunch" or "According to Forbes")
- Instead, present information as original content while naturally incorporating external links using markdown format [text](URL)
- Prioritize linking to OFFICIAL WEBSITES and primary sources:
  * Company official websites (e.g., microsoft.com, google.com, openai.com)
  * Official documentation and developer resources
  * Government (.gov) and educational (.edu) sources
  * GitHub repositories and official APIs
  * Product support and help pages
- AVOID linking to news articles, blogs, or third-party reviews
- Use natural anchor text that flows with your original writing (e.g., "learn more about [feature name]", "explore the [official documentation]", "visit the [company website]")
- Include 3-5 well-placed external links throughout the article, focusing on official sources
- Make links contextually relevant and valuable to readers seeking authoritative information

WORD COUNT ENFORCEMENT:
- Before writing, plan your content structure to meet exactly ${wordCount} words
- Monitor your word count as you write each section
- Ensure the article is COMPLETE with a proper conclusion
- Do not stop writing until you reach the target word count
- Do not exceed the maximum word limit of ${Math.ceil(wordCount * 1.05)} words

Write the complete blog post in markdown format with proper headings, bullet points, formatting, and natural inline external links.`

          // Calculate appropriate token limit based on word count
          const estimatedTokens = Math.ceil(wordCount * 1.5) // 1.5 tokens per word estimate
          const maxTokens = Math.min(Math.max(estimatedTokens + 2000, 8000), 32000) // Buffer + limits

          const blogPost = await gemini.generateContent(
            contentGenerationPrompt,
            {
              maxOutputTokens: maxTokens,
              thinkingConfig: {
                thinkingBudget: -1,
                includeThoughts: false
              }
            }
          )

          const finalContent = blogPost.response || ''
          const actualWordCount = finalContent.split(/\s+/).filter(w => w.length > 0).length

          const minWords = Math.floor(wordCount * 0.95)
          const maxWords = Math.ceil(wordCount * 1.05)

          console.log(`📊 Generated ${actualWordCount} words (target: ${wordCount}, range: ${minWords}-${maxWords})`)

          // Check article completion
          const isComplete = isArticleComplete(finalContent)
          console.log(`📝 Article completion check: ${isComplete ? '✅ Complete' : '⚠️ May be incomplete'}`)

          if (actualWordCount >= minWords && actualWordCount <= maxWords && isComplete) {
            console.log(`✅ Word count and completion requirements met`)
          } else {
            console.log(`⚠️ Generated content may not fully meet requirements`)
          }

          sendUpdate({ step: 'Blog post generated successfully!', progress: 100 })

          // Save content to database
          let savedContent = null
          try {
            savedContent = await prisma.content.create({
              data: {
                userId: session.user.id,
                type: 'blog',
                title: topic,
                content: finalContent,
                wordCount: actualWordCount,
                tone: tone || 'professional',
                metadata: JSON.stringify({
                  sourcesUsed: successfulExtractions.length,
                  searchResults: searchResults.items.length,
                  generatedAt: new Date().toISOString(),
                  generationMethod: 'blog-generator-stream'
                }),
                status: 'published'
              }
            })
            console.log('✅ Content saved to database with ID:', savedContent.id)
          } catch (dbError) {
            console.error('❌ Failed to save content to database:', dbError)
            // Continue with in-memory cache as fallback
          }

          // Generate a unique ID for cache (fallback)
          const contentId = savedContent?.id || Math.random().toString(36).substr(2, 9)

          // Store content in cache for immediate access
          ;(global as any).contentCache = (global as any).contentCache || new Map()
          ;(global as any).contentCache.set(contentId, {
            content: finalContent,
            title: topic,
            createdAt: Date.now()
          })

          // Clean up old cache entries (older than 1 hour)
          const oneHourAgo = Date.now() - (60 * 60 * 1000)
          for (const [key, value] of global.contentCache.entries()) {
            if (value.createdAt < oneHourAgo) {
              global.contentCache.delete(key)
            }
          }

          // Send the final content with database ID or cache ID
          sendUpdate({
            content: finalContent,
            contentId: contentId,
            redirectUrl: `/blog-display/${contentId}`,
            savedToDatabase: !!savedContent,
            databaseId: savedContent?.id,
            metadata: {
              wordCount: actualWordCount,
              sourcesUsed: successfulExtractions.length,
              searchResults: searchResults.items.length
            }
          })

        } catch (error) {
          console.error('Blog generation error:', error)
          sendUpdate({ 
            error: error instanceof Error ? error.message : 'Generation failed' 
          })
        } finally {
          controller.close()
        }
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
