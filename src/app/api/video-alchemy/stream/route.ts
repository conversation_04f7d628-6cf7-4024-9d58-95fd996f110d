import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { YouTubeService } from '@/lib/youtube-service';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { prisma } from '@/lib/prisma';
import { searchWebTavily } from '@/lib/search';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || '');

// Get the enhanced Gemini 2.5 Flash model
const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash-lite-preview-06-17' });

// Language-specific prompts
const languagePrompts = {
  english: {
    greeting: 'Hello readers!',
    transition: 'Moving on to',
    conclusion: 'In conclusion',
  },
  hindi: {
    greeting: 'नमस्ते पाठकों!',
    transition: 'आगे बढ़ते हुए',
    conclusion: 'निष्कर्ष में',
  },
  french: {
    greeting: 'Bonjour lecteurs!',
    transition: 'En continuant avec',
    conclusion: 'En conclusion',
  }
};

// Tone-specific instructions
const toneInstructions = {
  professional: 'Write in a formal, authoritative tone suitable for business or academic contexts.',
  conversational: 'Write in a friendly, engaging tone as if having a conversation with the reader.',
  casual: 'Write in a relaxed, informal tone with a personal touch.',
  educational: 'Write in an informative, clear tone focused on teaching and explaining concepts.',
  storytelling: 'Write in a narrative style, using stories and examples to illustrate points.',
  technical: 'Write in a detailed, precise tone with technical accuracy and depth.'
};

// Animated story stages
const storyStages = [
  {
    stage: 'initialization',
    title: 'The Journey Begins',
    description: 'Our magical Video Alchemy laboratory is warming up...',
    animation: 'laboratory-setup',
    duration: 3000
  },
  {
    stage: 'video-processing',
    title: 'Extracting Video Essence',
    description: 'Magical extractors are capturing the essence from your videos...',
    animation: 'video-extraction',
    duration: 15000
  },
  {
    stage: 'content-analysis',
    title: 'The AI Oracle Awakens',
    description: 'Our AI oracle is analyzing your content and discovering hidden knowledge gaps...',
    animation: 'ai-analysis',
    duration: 10000
  },
  {
    stage: 'research-gathering',
    title: 'Knowledge Hunters Dispatched',
    description: 'Digital knowledge hunters are scouring the web for missing information...',
    animation: 'research-hunt',
    duration: 20000
  },
  {
    stage: 'article-generation',
    title: 'The Grand Synthesis',
    description: 'Master writers are weaving your perfect article with golden threads of wisdom...',
    animation: 'article-creation',
    duration: 25000
  },
  {
    stage: 'quality-assurance',
    title: 'The Final Polish',
    description: 'Quality guardians are adding the final touches to your masterpiece...',
    animation: 'final-polish',
    duration: 5000
  },
  {
    stage: 'completion',
    title: 'The Magic is Complete!',
    description: 'Your superior article is ready to captivate readers worldwide!',
    animation: 'completion-celebration',
    duration: 3000
  }
];

  // Enhanced Tavily search function using fallback key system
async function searchWithTavily(query: string): Promise<any> {
  try {
    console.log(`🔍 Searching with enhanced Tavily (fallback keys): ${query}`);
    
    // Use the enhanced search function that has 9+ fallback keys
    const results = await searchWebTavily(query, 5);
    
    if (!results || results.length === 0) {
      console.warn('No results found for query:', query);
      return null;
    }

    // Transform to expected format for the stream
    const response = {
      answer: results[0]?.snippet || 'Information found from search results',
      results: results.map(r => ({
        title: r.title,
        content: r.snippet || r.content,
        url: r.url
      }))
    };

    console.log(`✅ Enhanced Tavily search successful: ${results.length} results`);
    return response;
    
  } catch (error) {
    console.error('Enhanced Tavily search error:', error);
    return null;
  }
}

// Analyze captions and generate research queries
async function analyzeCaptionsAndGenerateQueries(
  captions: string[],
  topic: string,
  userPrompt: string,
  customInstructions: string,
  language: string
): Promise<string[]> {
  console.log('🧠 GEMINI ANALYSIS (STREAM): Starting intelligent video content analysis...');
  console.log(`📊 Processing ${captions.length} video transcript(s) for topic: "${topic}"`);
  
  // Using the global model instance
  // const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash-latest' });

  const prompt = `You are an expert content analyst using advanced reasoning to analyze video transcripts. Use step-by-step thinking to identify content gaps and generate intelligent research queries.

**ANALYSIS CONTEXT:**
Topic: "${topic}"
${userPrompt ? `User's Specific Focus: "${userPrompt}"` : ''}
${customInstructions ? `Custom Instructions: "${customInstructions}"` : ''}
Language: ${language}

**VIDEO TRANSCRIPTS TO ANALYZE:**
${captions.join('\n\n---\n\n')}

**THINKING PROCESS - ANALYZE STEP BY STEP:**

1. **CONTENT COMPREHENSION ANALYSIS:**
   - What are the main topics covered in these video transcripts?
   - What is the overall quality and depth of information provided?
   - Are there any conflicting or unclear statements?

2. **GAP IDENTIFICATION REASONING:**
   - What important information is missing that users would expect?
   - Which claims need verification or additional context?
   - What technical terms or concepts need better explanation?
   - Where are there logical jumps or insufficient explanations?

3. **USER REQUIREMENT ALIGNMENT:**
   - How well do the videos address the user's specific topic focus?
   - What additional information would make the content more valuable?
   - Are there user-specific requirements not covered by the videos?

4. **STRATEGIC RESEARCH QUERY GENERATION:**
   - What specific data, statistics, or facts would strengthen the content?
   - Which expert opinions or authoritative sources are needed?
   - What current information (2024-2025) would make the content more relevant?
   - Which comparison data or case studies would add value?

**RESEARCH QUERY REQUIREMENTS:**
- Generate 8-12 specific, actionable search queries
- Focus on filling identified gaps with concrete information
- Target authoritative sources and current data
- Include verification queries for claims made in videos
- Consider user's specific requirements and custom instructions

**THINKING OUTPUT FORMAT:**
First, show your step-by-step analysis and reasoning process.
Then provide the final queries in this JSON format:

{
  "analysis": {
    "mainTopics": ["topic1", "topic2", ...],
    "contentQuality": "assessment of video content quality",
    "identifiedGaps": ["gap1", "gap2", ...],
    "missingInformation": ["info1", "info2", ...],
    "userAlignmentScore": "1-10 rating of how well videos match user needs"
  },
  "queries": [
    "specific search query 1",
    "specific search query 2",
    ...
  ]
}

Use your advanced reasoning to create queries that will result in a superior article that provides more value than just watching the videos.`;

  try {
    console.log('🔍 GEMINI ANALYSIS (STREAM): Sending analysis request with thinking model...');
    const startTime = Date.now();
    
    // Use enhanced analysis model
    const result = await model.generateContent({
      contents: [
        {
          role: 'user',
          parts: [{ text: prompt }]
        }
      ],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 64000, // MAXIMUM FREEDOM
        // Note: Advanced thinking analysis enabled through prompt structure
      }
    });

    const response = result.response.text();
    const duration = Date.now() - startTime;
    
    console.log(`⏱️  GEMINI ANALYSIS (STREAM): Completed in ${duration}ms`);
    console.log('📋 GEMINI ANALYSIS (STREAM): Processing response...');
    
    // Extract JSON from response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const analysisResult = JSON.parse(jsonMatch[0]);
      
      // Log detailed analysis results
      console.log('✅ GEMINI ANALYSIS (STREAM) COMPLETE:');
      console.log(`   📊 Main Topics: ${analysisResult.analysis?.mainTopics?.join(', ') || 'Not specified'}`);
      console.log(`   📈 Content Quality: ${analysisResult.analysis?.contentQuality || 'Not assessed'}`);
      console.log(`   🔍 Identified Gaps: ${analysisResult.analysis?.identifiedGaps?.length || 0} gaps found`);
      console.log(`   📝 Missing Information: ${analysisResult.analysis?.missingInformation?.length || 0} items identified`);
      console.log(`   🎯 User Alignment Score: ${analysisResult.analysis?.userAlignmentScore || 'Not rated'}/10`);
      console.log(`   🔎 Generated Queries: ${analysisResult.queries?.length || 0} intelligent research queries`);
      
      // Log sample queries
      if (analysisResult.queries && analysisResult.queries.length > 0) {
        console.log('📋 SAMPLE RESEARCH QUERIES (STREAM):');
        analysisResult.queries.slice(0, 3).forEach((query: string, idx: number) => {
          console.log(`   ${idx + 1}. ${query}`);
        });
      }
      
      return Array.isArray(analysisResult.queries) ? analysisResult.queries.slice(0, 12) : [];
    }
    
    console.log('⚠️  GEMINI ANALYSIS (STREAM): No JSON found in response, using fallback extraction...');
    
    // Fallback: Extract queries from text
    const lines = response.split('\n');
    const queries = lines
      .filter(line => line.includes('"') && line.length > 20)
      .map(line => line.replace(/[",\[\]]/g, '').trim())
      .filter(query => query.length > 10)
      .slice(0, 10);
    
    console.log(`🔄 GEMINI ANALYSIS (STREAM): Extracted ${queries.length} queries using fallback method`);
    return queries;
    
  } catch (error) {
    console.error('❌ GEMINI ANALYSIS (STREAM) ERROR:', error);
    console.log('🔄 GEMINI ANALYSIS (STREAM): Using intelligent fallback queries...');
    
    // Intelligent fallback queries based on topic
    const fallbackQueries = [
      `${topic} latest statistics 2024 2025`,
      `${topic} expert opinions authoritative sources`,
      `${topic} case studies real examples`,
      `${topic} benefits risks advantages disadvantages`,
      `${topic} how to implement step by step`,
      `${topic} common mistakes to avoid`,
      `${topic} cost pricing comparison 2024`,
      `${topic} best practices industry standards`,
      `${topic} future trends predictions 2025`,
      `${topic} user reviews testimonials feedback`
    ];
    
    console.log(`✅ GEMINI ANALYSIS (STREAM): Generated ${fallbackQueries.length} fallback queries`);
    return fallbackQueries;
  }
}

// SSE helper function
function sendSSEMessage(controller: ReadableStreamDefaultController, data: any) {
  const message = `data: ${JSON.stringify(data)}\n\n`;
  controller.enqueue(new TextEncoder().encode(message));
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const body = await request.json();
  const { topic, videoLinks, tone, wordCount, customInstructions, language } = body;

  if (!topic || !videoLinks || videoLinks.length === 0) {
    return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
  }

  // Create SSE stream
  const stream = new ReadableStream({
    start(controller) {
      (async () => {
        try {
          // Stage 1: Initialization
          sendSSEMessage(controller, {
            stage: 'initialization',
            progress: 0,
            message: 'Initializing Video Alchemy...',
            storyData: storyStages[0]
          });

          await new Promise(resolve => setTimeout(resolve, 2000));

          // Stage 2: Video Processing
          sendSSEMessage(controller, {
            stage: 'video-processing',
            progress: 10,
            message: 'Extracting captions from videos...',
            storyData: storyStages[1]
          });

          const youtube = new YouTubeService();
          const allCaptions: string[] = [];
          const videoTitles: string[] = [];
          const failedVideos: string[] = [];

          for (let i = 0; i < videoLinks.length; i++) {
            const videoLink = videoLinks[i];
            
            sendSSEMessage(controller, {
              stage: 'video-processing',
              progress: 10 + (i * 15 / videoLinks.length),
              message: `Processing video ${i + 1} of ${videoLinks.length}...`,
              storyData: storyStages[1]
            });

            try {
              const videoIdMatch = videoLink.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/);
              const videoId = videoIdMatch ? videoIdMatch[1] : null;
              
              if (!videoId) {
                failedVideos.push(videoLink);
                continue;
              }

              const videoInfo = await youtube.getVideoMetadata(videoId);
              if (videoInfo) {
                videoTitles.push(videoInfo.title);
              }

              const captions = await youtube.extractCaptions(videoId, language === 'hindi' ? 'hi' : language === 'french' ? 'fr' : 'en');
              
              if (captions && captions.length > 0) {
                const transcript = captions.map(c => c.text).join(' ');
                allCaptions.push(`Video: "${videoInfo?.title || videoLink}"\nTranscript: ${transcript}`);
              } else {
                failedVideos.push(videoLink);
              }
            } catch (error) {
              console.error(`Error processing video ${videoLink}:`, error);
              failedVideos.push(videoLink);
            }
          }

          if (allCaptions.length === 0) {
            sendSSEMessage(controller, {
              stage: 'error',
              progress: 0,
              message: 'Failed to extract captions from any videos',
              error: 'No captions extracted'
            });
            controller.close();
            return;
          }

          // Stage 3: Content Analysis
          sendSSEMessage(controller, {
            stage: 'content-analysis',
            progress: 30,
            message: 'AI oracle analyzing content for knowledge gaps...',
            storyData: storyStages[2]
          });

          console.log('🧠 STARTING GEMINI ANALYSIS PHASE...');
          const researchQueries = await analyzeCaptionsAndGenerateQueries(
            allCaptions, 
            topic, 
            topic,
            customInstructions || '', 
            language
          );

          // Enhanced analysis complete message
          const analysisCompleteMessage = `AI oracle discovered ${researchQueries.length} knowledge gaps to investigate`;
          console.log(`✅ GEMINI ANALYSIS COMPLETE: ${analysisCompleteMessage}`);
          
          sendSSEMessage(controller, {
            stage: 'content-analysis',
            progress: 40,
            message: analysisCompleteMessage,
            storyData: storyStages[2],
            queries: researchQueries.length > 0 ? researchQueries.slice(0, 5) : [], // Send first 5 queries to frontend
            analysisDetails: {
              totalQueries: researchQueries.length,
              analysisComplete: true,
              intelligentAnalysis: true
            }
          });

          // Stage 4: Research Gathering
          sendSSEMessage(controller, {
            stage: 'research-gathering',
            progress: 45,
            message: 'Dispatching knowledge hunters...',
            storyData: storyStages[3]
          });

          console.log(`🌐 STARTING RESEARCH PHASE: Processing ${researchQueries.length} intelligent queries...`);
          const researchResults: any[] = [];
          
          for (let i = 0; i < researchQueries.length; i++) {
            const query = researchQueries[i];
            const progressPercent = 45 + (i * 20 / researchQueries.length);
            
            console.log(`🔍 RESEARCH QUERY ${i + 1}/${researchQueries.length}: ${query}`);
            
            sendSSEMessage(controller, {
              stage: 'research-gathering',
              progress: progressPercent,
              message: `Researching: ${query.substring(0, 50)}...`,
              storyData: storyStages[3],
              currentQuery: query,
              queryProgress: `${i + 1}/${researchQueries.length}`
            });

            const searchResult = await searchWithTavily(query);
            if (searchResult) {
              const resultData = {
                query,
                answer: searchResult.answer || '',
                sources: searchResult.results?.slice(0, 3).map((r: any) => ({
                  title: r.title,
                  content: r.content,
                  url: r.url
                })) || []
              };
              researchResults.push(resultData);
              
              console.log(`✅ RESEARCH SUCCESS: Query ${i + 1} - Found ${resultData.sources.length} sources`);
            } else {
              console.log(`⚠️  RESEARCH SKIPPED: Query ${i + 1} - No results found`);
            }
          }

          console.log(`✅ RESEARCH PHASE COMPLETE: Successfully researched ${researchResults.length}/${researchQueries.length} queries`);
          
          // Send final research summary
          sendSSEMessage(controller, {
            stage: 'research-gathering',
            progress: 65,
            message: `Research complete! Gathered ${researchResults.length} knowledge sources`,
            storyData: storyStages[3],
            researchSummary: {
              totalQueries: researchQueries.length,
              successfulQueries: researchResults.length,
              totalSources: researchResults.reduce((sum, r) => sum + r.sources.length, 0),
              researchComplete: true
            }
          });

          // Stage 5: Article Generation
          sendSSEMessage(controller, {
            stage: 'article-generation',
            progress: 70,
            message: 'Master writers weaving your article...',
            storyData: storyStages[4]
          });

          console.log('🚀 STARTING ARTICLE GENERATION PHASE...');
          console.log(`📝 Combining ${allCaptions.length} video transcripts + ${researchResults.length} research sources`);
          
          const combinedTranscripts = allCaptions.join('\n\n---\n\n');
          const researchData = researchResults.length > 0 ? 
            '\n\n## Additional Research Data:\n' + 
            researchResults.map(r => 
              `**Query**: ${r.query}\n**Answer**: ${r.answer}\n**Sources**:\n${r.sources.map((s: any) => `- ${s.title}: ${s.content.substring(0, 200)}...`).join('\n')}`
            ).join('\n\n') : '';

          const langElements = languagePrompts[language as keyof typeof languagePrompts] || languagePrompts.english;
          const toneInstruction = toneInstructions[tone as keyof typeof toneInstructions] || toneInstructions.conversational;

          console.log(`🎯 Generation Parameters: ${wordCount} words, ${tone} tone, ${language} language`);
          console.log(`📊 Content Sources: ${allCaptions.length} videos + ${researchResults.length} research queries`);

          const systemPrompt = `You are an elite content writer with expertise in creating viral, SEO-optimized articles that rank #1 on Google and provide exceptional value to readers.

🎯 CRITICAL REQUIREMENTS:
1. **EXACT WORD COUNT**: Write precisely ${wordCount} words (count internally, never show counts in output)
2. **LANGUAGE**: Write in ${language === 'hindi' ? 'Hindi (हिंदी)' : language === 'french' ? 'French (Français)' : 'English'}
3. **TONE**: ${toneInstruction}

📊 CONTENT OPTIMIZATION FRAMEWORK:

### SEO Mastery (Search Engine Optimization):
- **Keyword Strategy**: Use "${topic}" naturally 3-5 times per 1000 words
- **Semantic SEO**: Include related terms, synonyms, and LSI keywords
- **Title Optimization**: Create magnetic titles with power words and emotional triggers
- **Meta Description**: First 160 characters must compel clicks
- **Header Structure**: Use H2s every 150-200 words, H3s for sub-points
- **Internal Linking**: Reference related concepts naturally
- **Readability**: Flesch Reading Ease score 60-70 (8th-grade level)

### AEO Excellence (Answer Engine Optimization):
- **Direct Answers**: Answer key questions in the first 2-3 sentences of relevant sections
- **Featured Snippets**: Structure content for paragraph, list, and table snippets
- **FAQ Integration**: Include commonly asked questions with concise answers
- **Definition Boxes**: Define key terms clearly in standalone sentences
- **Voice Search**: Use conversational, question-based subheadings

### GEO Innovation (Generative Engine Optimization):
- **AI Comprehension**: Structure content with clear topic sentences
- **Semantic Clarity**: Use unambiguous language that AI can easily parse
- **Factual Accuracy**: Ensure all claims are verifiable and well-supported
- **Logical Flow**: Create content that follows a clear narrative arc
- **Entity Recognition**: Mention relevant people, places, and things clearly

### Human Writing Patterns:
- **Varied Sentence Structure**: Mix short (5-10 words) and long (20-25 words) sentences
- **Emotional Engagement**: Use storytelling, anecdotes, and relatable examples
- **Active Voice**: 80% active voice for energy and clarity
- **Transition Mastery**: Smooth flow between paragraphs with transition phrases
- **Conversational Elements**: Include rhetorical questions, direct address ("you")
- **Micro-Stories**: Add brief examples or case studies to illustrate points

### Content Architecture:
1. **Hook Opening** (10% of word count):
   - Start with a compelling statistic, question, or bold statement
   - Preview the value readers will gain
   - Create urgency or curiosity

2. **Main Content** (75% of word count):
   - Logical progression from basic to advanced concepts
   - Use the PAS formula (Problem-Agitate-Solution) where relevant
   - Include data, statistics, and expert insights
   - Add practical examples and actionable tips
   - Break complex ideas into digestible chunks
   - **CREATE DATA TABLES** when presenting comparative data, statistics, or multi-factor information
   - Format tables using proper markdown (|Column1|Column2| with header row and separator)

3. **Power Conclusion** (15% of word count):
   - Summarize key takeaways
   - Provide clear next steps
   - End with a thought-provoking statement or call-to-action

### Table Generation Requirements:
- Include at least 1-2 data tables when appropriate for the content
- Use tables for comparing options, features, statistics, or numerical data
- Format using proper markdown tables with headers
- Keep tables responsive (4-5 columns maximum)
- Include a brief description or insight above or below each table

### Engagement Optimization:
- **Bucket Brigades**: Use phrases like "Here's the deal:", "But wait:", "The truth is:"
- **Power Words**: Include emotional triggers (amazing, proven, essential, revolutionary)
- **Social Proof**: Reference studies, expert opinions, or common experiences
- **FOMO Creation**: Highlight what readers might miss or lose
- **Value Stacking**: Continuously emphasize benefits and outcomes

${customInstructions ? `### Custom Requirements:\n${customInstructions}` : ''}

🚫 NEVER DO:
- Include word counts, placeholders, or meta-commentary
- Use generic headings like "Section 1", "Part A"
- Write walls of text (max 4 sentences per paragraph)
- Use jargon without explanation
- Make unsupported claims
- Forget the human element`;

          const userPrompt = `Transform the following video transcripts and research data into an exceptional ${wordCount}-word article about "${topic}".

📹 VIDEO TRANSCRIPTS:
${combinedTranscripts}

🔍 SUPPLEMENTARY RESEARCH:
${researchData}

📝 YOUR MISSION:
Create a comprehensive, engaging article that:
1. Synthesizes all video content into a cohesive narrative
2. Fills knowledge gaps with the research data provided
3. Adds value beyond what's in the videos
4. Maintains consistent tone and style throughout
5. Optimizes for both human readers and search engines
6. Includes data tables when presenting comparative information or statistics

🎯 WORD COUNT DISTRIBUTION:
- Introduction: ${Math.ceil(wordCount * 0.10)} words (compelling hook + preview)
- Main Body: ${Math.ceil(wordCount * 0.75)} words (core content + examples)  
- Conclusion: ${Math.ceil(wordCount * 0.15)} words (summary + call-to-action)
- TOTAL: Exactly ${wordCount} words

💡 CONTENT ENHANCEMENT TIPS:
- Use video quotes as social proof
- Transform video examples into written case studies
- Expand on concepts briefly mentioned in videos
- Add context and background information
- Include practical applications and actionable advice
- Create smooth transitions between video topics

Remember: You're not just transcribing videos - you're creating a superior article that provides more value than watching the videos themselves.

Start writing now, ensuring exactly ${wordCount} words of exceptional content.`;

          // Generate article using Gemini with enhanced prompts
          // const model = genAI.getGenerativeModel({ 
          //   model: 'gemini-1.5-flash-latest',
          //   generationConfig: {
          //     temperature: 0.8,
          //     topK: 40,
          //     topP: 0.95,
          //   }
          // });

          sendSSEMessage(controller, {
            stage: 'article-generation',
            progress: 85,
            message: 'Crafting the perfect article...',
            storyData: storyStages[4],
            generationDetails: {
              wordCount: wordCount,
              tone: tone,
              language: language,
              sourceCount: allCaptions.length + researchResults.length
            }
          });

          console.log('📝 GENERATING ARTICLE: Sending enhanced prompts to Gemini...');
          const generationStartTime = Date.now();
          
          const result = await model.generateContent({
            contents: [
              {
                role: 'user',
                parts: [{ text: systemPrompt + '\n\n' + userPrompt }]
              }
            ],
          });

          const generatedContent = result.response.text();
          const generationDuration = Date.now() - generationStartTime;
          
          console.log(`✅ ARTICLE GENERATION COMPLETE in ${generationDuration}ms`);
          console.log(`📊 Generated content length: ${generatedContent.length} characters`);

          // Stage 6: Quality Assurance
          sendSSEMessage(controller, {
            stage: 'quality-assurance',
            progress: 95,
            message: 'Quality guardians polishing your masterpiece...',
            storyData: storyStages[5]
          });

          const actualWordCount = generatedContent.split(/\s+/).filter(word => word.length > 0).length;
          const wordCountAccuracy = Math.abs(actualWordCount - wordCount);
          const wordCountPercentage = ((actualWordCount / wordCount) * 100).toFixed(1);
          
          console.log(`📝 QUALITY ASSURANCE: Word count analysis complete`);
          console.log(`   🎯 Target: ${wordCount} words`);
          console.log(`   ✅ Actual: ${actualWordCount} words`);
          console.log(`   📊 Accuracy: ${wordCountPercentage}% (${wordCountAccuracy} words difference)`);
          
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Save to database
          const article = await prisma.content.create({
            data: {
              title: topic,
              content: generatedContent,
              type: 'video_alchemy',
              userId: session.user.id,
              metadata: JSON.stringify({
                videoLinks,
                videoTitles,
                failedVideos,
                researchQueries,
                researchResults: researchResults.length,
                tone,
                wordCount,
                language,
                customInstructions: customInstructions || null,
                generatedAt: new Date().toISOString(),
                analysisData: {
                  videosProcessed: allCaptions.length,
                  queriesGenerated: researchQueries.length,
                  successfulResearch: researchResults.length,
                  totalSources: researchResults.reduce((sum, r) => sum + r.sources.length, 0)
                }
              }),
              wordCount: actualWordCount,
              tone,
              language: language === 'hindi' ? 'hi' : language === 'french' ? 'fr' : 'en'
            }
          });

          console.log('💾 DATABASE SAVE COMPLETE:');
          console.log(`   📝 Article ID: ${article.id}`);
          console.log(`   📊 Word Count: ${actualWordCount} words`);
          console.log(`   🎯 Accuracy: ${wordCountPercentage}% of target`);
          console.log(`   📱 Language: ${language}`);
          console.log(`   🎨 Tone: ${tone}`);

          // Stage 7: Completion
          const completionMessage = `Your magical article is ready! ${actualWordCount} words of pure value created from ${allCaptions.length} videos + ${researchResults.length} research sources.`;
          console.log(`🎉 COMPLETION: ${completionMessage}`);
          
          sendSSEMessage(controller, {
            stage: 'completion',
            progress: 100,
            message: completionMessage,
            storyData: storyStages[6],
            article: {
              id: article.id,
              title: topic,
              content: generatedContent,
              metadata: {
                processedVideos: videoLinks.length - failedVideos.length,
                failedVideos,
                videoTitles,
                researchQueries,
                researchResults: researchResults.length,
                language,
                tone,
                wordCount: actualWordCount,
                targetWordCount: wordCount,
                wordCountAccuracy: wordCountPercentage,
                totalSources: researchResults.reduce((sum, r) => sum + r.sources.length, 0),
                generationTime: generationDuration,
                analysisComplete: true
              }
            },
            finalStats: {
              videosProcessed: allCaptions.length,
              totalQueries: researchQueries.length,
              successfulResearch: researchResults.length,
              totalSources: researchResults.reduce((sum, r) => sum + r.sources.length, 0),
              wordCount: actualWordCount,
              wordCountAccuracy: wordCountPercentage,
              generationTime: generationDuration,
              overallSuccess: true
            }
          });

          await new Promise(resolve => setTimeout(resolve, 2000));
          controller.close();

        } catch (error) {
          console.error('SSE Generation error:', error);
          sendSSEMessage(controller, {
            stage: 'error',
            progress: 0,
            message: 'An error occurred during generation',
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          controller.close();
        }
      })();
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
} 