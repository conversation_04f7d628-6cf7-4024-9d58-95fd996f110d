import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { YouTubeService } from '@/lib/youtube-service';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { prisma } from '@/lib/prisma';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || '');

// Get the enhanced Gemini 2.5 Flash model
const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash-lite-preview-06-17' });

// Language-specific prompts
const languagePrompts = {
  english: {
    greeting: 'Hello readers!',
    transition: 'Moving on to',
    conclusion: 'In conclusion',
  },
  hindi: {
    greeting: 'नमस्ते पाठकों!',
    transition: 'आगे बढ़ते हुए',
    conclusion: 'निष्कर्ष में',
  },
  french: {
    greeting: 'Bonjour lecteurs!',
    transition: 'En continuant avec',
    conclusion: 'En conclusion',
  }
};

// Tone-specific instructions
const toneInstructions = {
  professional: 'Write in a formal, authoritative tone suitable for business or academic contexts.',
  conversational: 'Write in a friendly, engaging tone as if having a conversation with the reader.',
  casual: 'Write in a relaxed, informal tone with a personal touch.',
  educational: 'Write in an informative, clear tone focused on teaching and explaining concepts.',
  storytelling: 'Write in a narrative style, using stories and examples to illustrate points.',
  technical: 'Write in a detailed, precise tone with technical accuracy and depth.'
};

// Add Tavily search function
async function searchWithTavily(query: string): Promise<any> {
  try {
    const apiKey = process.env.TAVILY_API_KEY;
    if (!apiKey) {
      console.warn('Tavily API key not found, skipping search');
      return null;
    }

    const response = await fetch('https://api.tavily.com/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        api_key: apiKey,
        query,
        search_depth: 'advanced',
        include_answer: true,
        include_raw_content: true,
        max_results: 5
      }),
    });

    if (!response.ok) {
      console.error('Tavily search failed:', response.statusText);
      return null;
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Tavily search error:', error);
    return null;
  }
}

// Analyze captions and generate research queries
async function analyzeCaptionsAndGenerateQueries(
  captions: string[],
  topic: string,
  userPrompt: string,
  customInstructions: string,
  language: string
): Promise<string[]> {
  console.log('🧠 GEMINI ANALYSIS: Starting intelligent video content analysis...');
  console.log(`📊 Processing ${captions.length} video transcript(s) for topic: "${topic}"`);
  
  // const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash-latest' }); // This line is removed

  const prompt = `You are an expert content analyst using advanced reasoning to analyze video transcripts. Use step-by-step thinking to identify content gaps and generate intelligent research queries.

**ANALYSIS CONTEXT:**
Topic: "${topic}"
${userPrompt ? `User's Specific Focus: "${userPrompt}"` : ''}
${customInstructions ? `Custom Instructions: "${customInstructions}"` : ''}
Language: ${language}

**VIDEO TRANSCRIPTS TO ANALYZE:**
${captions.join('\n\n---\n\n')}

**THINKING PROCESS - ANALYZE STEP BY STEP:**

1. **CONTENT COMPREHENSION ANALYSIS:**
   - What are the main topics covered in these video transcripts?
   - What is the overall quality and depth of information provided?
   - Are there any conflicting or unclear statements?

2. **GAP IDENTIFICATION REASONING:**
   - What important information is missing that users would expect?
   - Which claims need verification or additional context?
   - What technical terms or concepts need better explanation?
   - Where are there logical jumps or insufficient explanations?

3. **USER REQUIREMENT ALIGNMENT:**
   - How well do the videos address the user's specific topic focus?
   - What additional information would make the content more valuable?
   - Are there user-specific requirements not covered by the videos?

4. **STRATEGIC RESEARCH QUERY GENERATION:**
   - What specific data, statistics, or facts would strengthen the content?
   - Which expert opinions or authoritative sources are needed?
   - What current information (2024-2025) would make the content more relevant?
   - Which comparison data or case studies would add value?

**RESEARCH QUERY REQUIREMENTS:**
- Generate 8-12 specific, actionable search queries
- Focus on filling identified gaps with concrete information
- Target authoritative sources and current data
- Include verification queries for claims made in videos
- Consider user's specific requirements and custom instructions

**THINKING OUTPUT FORMAT:**
First, show your step-by-step analysis and reasoning process.
Then provide the final queries in this JSON format:

{
  "analysis": {
    "mainTopics": ["topic1", "topic2", ...],
    "contentQuality": "assessment of video content quality",
    "identifiedGaps": ["gap1", "gap2", ...],
    "missingInformation": ["info1", "info2", ...],
    "userAlignmentScore": "1-10 rating of how well videos match user needs"
  },
  "queries": [
    "specific search query 1",
    "specific search query 2",
    ...
  ]
}

Use your advanced reasoning to create queries that will result in a superior article that provides more value than just watching the videos.`;

  try {
    console.log('🔍 GEMINI ANALYSIS: Sending analysis request with thinking model...');
    const startTime = Date.now();
    
    // Use thinking model for comprehensive analysis
    const result = await model.generateContent({
      contents: [
        {
          role: 'user',
          parts: [{ text: prompt }]
        }
      ],
      generationConfig: {
        temperature: 0.7,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 64000, // MAXIMUM FREEDOM
        // Note: Advanced thinking analysis enabled through prompt structure
      }
    });

    const response = result.response.text();
    const duration = Date.now() - startTime;
    
    console.log(`⏱️  GEMINI ANALYSIS: Completed in ${duration}ms`);
    console.log('📋 GEMINI ANALYSIS: Processing response...');
    
    // Extract JSON from response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const analysisResult = JSON.parse(jsonMatch[0]);
      
      // Log detailed analysis results
      console.log('✅ GEMINI ANALYSIS COMPLETE:');
      console.log(`   📊 Main Topics: ${analysisResult.analysis?.mainTopics?.join(', ') || 'Not specified'}`);
      console.log(`   📈 Content Quality: ${analysisResult.analysis?.contentQuality || 'Not assessed'}`);
      console.log(`   🔍 Identified Gaps: ${analysisResult.analysis?.identifiedGaps?.length || 0} gaps found`);
      console.log(`   📝 Missing Information: ${analysisResult.analysis?.missingInformation?.length || 0} items identified`);
      console.log(`   🎯 User Alignment Score: ${analysisResult.analysis?.userAlignmentScore || 'Not rated'}/10`);
      console.log(`   🔎 Generated Queries: ${analysisResult.queries?.length || 0} intelligent research queries`);
      
      // Log sample queries
      if (analysisResult.queries && analysisResult.queries.length > 0) {
        console.log('📋 SAMPLE RESEARCH QUERIES:');
        analysisResult.queries.slice(0, 3).forEach((query: string, idx: number) => {
          console.log(`   ${idx + 1}. ${query}`);
        });
      }
      
      return Array.isArray(analysisResult.queries) ? analysisResult.queries.slice(0, 12) : [];
    }
    
    console.log('⚠️  GEMINI ANALYSIS: No JSON found in response, using fallback extraction...');
    
    // Fallback: Extract queries from text
    const lines = response.split('\n');
    const queries = lines
      .filter(line => line.includes('"') && line.length > 20)
      .map(line => line.replace(/[",\[\]]/g, '').trim())
      .filter(query => query.length > 10)
      .slice(0, 10);
    
    console.log(`🔄 GEMINI ANALYSIS: Extracted ${queries.length} queries using fallback method`);
    return queries;
    
  } catch (error) {
    console.error('❌ GEMINI ANALYSIS ERROR:', error);
    console.log('🔄 GEMINI ANALYSIS: Using intelligent fallback queries...');
    
    // Intelligent fallback queries based on topic
    const fallbackQueries = [
      `${topic} latest statistics 2024 2025`,
      `${topic} expert opinions authoritative sources`,
      `${topic} case studies real examples`,
      `${topic} benefits risks advantages disadvantages`,
      `${topic} how to implement step by step`,
      `${topic} common mistakes to avoid`,
      `${topic} cost pricing comparison 2024`,
      `${topic} best practices industry standards`,
      `${topic} future trends predictions 2025`,
      `${topic} user reviews testimonials feedback`
    ];
    
    console.log(`✅ GEMINI ANALYSIS: Generated ${fallbackQueries.length} fallback queries`);
    return fallbackQueries;
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { topic, videoLinks, tone, wordCount, customInstructions, language } = body;

    if (!topic || !videoLinks || videoLinks.length === 0) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Initialize YouTube service
    const youtube = new YouTubeService();
    
    // Extract captions from all videos
    const allCaptions: string[] = [];
    const videoTitles: string[] = [];
    const failedVideos: string[] = [];

    console.log('🎥 Step 1: Extracting captions from videos...');

    for (const videoLink of videoLinks) {
      try {
        // Extract video ID from URL
        const videoIdMatch = videoLink.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/);
        const videoId = videoIdMatch ? videoIdMatch[1] : null;
        
        if (!videoId) {
          failedVideos.push(videoLink);
          continue;
        }

        // Get video info
        const videoInfo = await youtube.getVideoMetadata(videoId);
        if (videoInfo) {
          videoTitles.push(videoInfo.title);
        }

        // Extract captions
        const captions = await youtube.extractCaptions(videoId, language === 'hindi' ? 'hi' : language === 'french' ? 'fr' : 'en');
        
        if (captions && captions.length > 0) {
          const transcript = captions.map(c => c.text).join(' ');
          allCaptions.push(`Video: "${videoInfo?.title || videoLink}"\nTranscript: ${transcript}`);
        } else {
          failedVideos.push(videoLink);
        }
      } catch (error) {
        console.error(`Error processing video ${videoLink}:`, error);
        failedVideos.push(videoLink);
      }
    }

    if (allCaptions.length === 0) {
      return NextResponse.json({ 
        error: 'Failed to extract captions from any of the provided videos', 
        failedVideos 
      }, { status: 400 });
    }

    console.log(`✅ Extracted captions from ${allCaptions.length} videos`);

    // Step 2: Analyze captions and generate research queries
    console.log('🔍 Step 2: Analyzing content and generating research queries...');
    const researchQueries = await analyzeCaptionsAndGenerateQueries(
      allCaptions, 
      topic, 
      topic, // Using topic as userPrompt for now
      customInstructions || '', 
      language
    );
    console.log(`📋 Generated ${researchQueries.length} research queries`);

    // Step 3: Perform Tavily searches for additional context
    console.log('🌐 Step 3: Researching additional information...');
    const researchResults: any[] = [];
    
    for (const query of researchQueries) {
      console.log(`  🔎 Searching: ${query}`);
      const searchResult = await searchWithTavily(query);
      if (searchResult) {
        researchResults.push({
          query,
          answer: searchResult.answer || '',
          sources: searchResult.results?.slice(0, 3).map((r: any) => ({
            title: r.title,
            content: r.content,
            url: r.url
          })) || []
        });
      }
    }

    console.log(`✅ Completed ${researchResults.length} research searches`);

    // Combine all data
    const combinedTranscripts = allCaptions.join('\n\n---\n\n');
    const researchData = researchResults.length > 0 ? 
      '\n\n## Additional Research Data:\n' + 
      researchResults.map(r => 
        `**Query**: ${r.query}\n**Answer**: ${r.answer}\n**Sources**:\n${r.sources.map((s: any) => `- ${s.title}: ${s.content.substring(0, 200)}...`).join('\n')}`
      ).join('\n\n') : '';

    // Get language-specific elements
    const langElements = languagePrompts[language as keyof typeof languagePrompts] || languagePrompts.english;
    const toneInstruction = toneInstructions[tone as keyof typeof toneInstructions] || toneInstructions.conversational;

    // Enhanced system prompt
    const systemPrompt = `You are an elite content writer with expertise in creating viral, SEO-optimized articles that rank #1 on Google and provide exceptional value to readers.

🎯 CRITICAL REQUIREMENTS:
1. **EXACT WORD COUNT**: Write precisely ${wordCount} words (count internally, never show counts in output)
2. **LANGUAGE**: Write in ${language === 'hindi' ? 'Hindi (हिंदी)' : language === 'french' ? 'French (Français)' : 'English'}
3. **TONE**: ${toneInstruction}

📊 CONTENT OPTIMIZATION FRAMEWORK:

### SEO Mastery (Search Engine Optimization):
- **Keyword Strategy**: Use "${topic}" naturally 3-5 times per 1000 words
- **Semantic SEO**: Include related terms, synonyms, and LSI keywords
- **Title Optimization**: Create magnetic titles with power words and emotional triggers
- **Meta Description**: First 160 characters must compel clicks
- **Header Structure**: Use H2s every 150-200 words, H3s for sub-points
- **Internal Linking**: Reference related concepts naturally
- **Readability**: Flesch Reading Ease score 60-70 (8th-grade level)

### AEO Excellence (Answer Engine Optimization):
- **Direct Answers**: Answer key questions in the first 2-3 sentences of relevant sections
- **Featured Snippets**: Structure content for paragraph, list, and table snippets
- **FAQ Integration**: Include commonly asked questions with concise answers
- **Definition Boxes**: Define key terms clearly in standalone sentences
- **Voice Search**: Use conversational, question-based subheadings

### GEO Innovation (Generative Engine Optimization):
- **AI Comprehension**: Structure content with clear topic sentences
- **Semantic Clarity**: Use unambiguous language that AI can easily parse
- **Factual Accuracy**: Ensure all claims are verifiable and well-supported
- **Logical Flow**: Create content that follows a clear narrative arc
- **Entity Recognition**: Mention relevant people, places, and things clearly

### Human Writing Patterns:
- **Varied Sentence Structure**: Mix short (5-10 words) and long (20-25 words) sentences
- **Emotional Engagement**: Use storytelling, anecdotes, and relatable examples
- **Active Voice**: 80% active voice for energy and clarity
- **Transition Mastery**: Smooth flow between paragraphs with transition phrases
- **Conversational Elements**: Include rhetorical questions, direct address ("you")
- **Micro-Stories**: Add brief examples or case studies to illustrate points

### Content Architecture:
1. **Hook Opening** (10% of word count):
   - Start with a compelling statistic, question, or bold statement
   - Preview the value readers will gain
   - Create urgency or curiosity

2. **Main Content** (75% of word count):
   - Logical progression from basic to advanced concepts
   - Use the PAS formula (Problem-Agitate-Solution) where relevant
   - Include data, statistics, and expert insights
   - Add practical examples and actionable tips
   - Break complex ideas into digestible chunks
   - **CREATE DATA TABLES** when presenting comparative data, statistics, or multi-factor information
   - Format tables using proper markdown (|Column1|Column2| with header row and separator)

3. **Power Conclusion** (15% of word count):
   - Summarize key takeaways
   - Provide clear next steps
   - End with a thought-provoking statement or call-to-action

### Table Generation Requirements:
- Include at least 1-2 data tables when appropriate for the content
- Use tables for comparing options, features, statistics, or numerical data
- Format using proper markdown tables with headers
- Keep tables responsive (4-5 columns maximum)
- Include a brief description or insight above or below each table

### Engagement Optimization:
- **Bucket Brigades**: Use phrases like "Here's the deal:", "But wait:", "The truth is:"
- **Power Words**: Include emotional triggers (amazing, proven, essential, revolutionary)
- **Social Proof**: Reference studies, expert opinions, or common experiences
- **FOMO Creation**: Highlight what readers might miss or lose
- **Value Stacking**: Continuously emphasize benefits and outcomes

${customInstructions ? `### Custom Requirements:\n${customInstructions}` : ''}

🚫 NEVER DO:
- Include word counts, placeholders, or meta-commentary
- Use generic headings like "Section 1", "Part A"
- Write walls of text (max 4 sentences per paragraph)
- Use jargon without explanation
- Make unsupported claims
- Forget the human element`;

    // Enhanced user prompt
    const userPrompt = `Transform the following video transcripts and research data into an exceptional ${wordCount}-word article about "${topic}".

📹 VIDEO TRANSCRIPTS:
${combinedTranscripts}

🔍 SUPPLEMENTARY RESEARCH:
${researchData}

📝 YOUR MISSION:
Create a comprehensive, engaging article that:
1. Synthesizes all video content into a cohesive narrative
2. Fills knowledge gaps with the research data provided
3. Adds value beyond what's in the videos
4. Maintains consistent tone and style throughout
5. Optimizes for both human readers and search engines
6. Includes data tables when presenting comparative information or statistics

🎯 WORD COUNT DISTRIBUTION:
- Introduction: ${Math.ceil(wordCount * 0.10)} words (compelling hook + preview)
- Main Body: ${Math.ceil(wordCount * 0.75)} words (core content + examples)  
- Conclusion: ${Math.ceil(wordCount * 0.15)} words (summary + call-to-action)
- TOTAL: Exactly ${wordCount} words

💡 CONTENT ENHANCEMENT TIPS:
- Use video quotes as social proof
- Transform video examples into written case studies
- Expand on concepts briefly mentioned in videos
- Add context and background information
- Include practical applications and actionable advice
- Create smooth transitions between video topics

Remember: You're not just transcribing videos - you're creating a superior article that provides more value than watching the videos themselves.

Start writing now, ensuring exactly ${wordCount} words of exceptional content.`;

    // Generate article using Gemini with enhanced prompts
    // const model = genAI.getGenerativeModel({  // This line is removed
    //   model: 'gemini-1.5-flash-latest', // This line is removed
    //   generationConfig: { // This line is removed
    //     temperature: 0.8, // This line is removed
    //     topK: 40, // This line is removed
    //     topP: 0.95, // This line is removed
    //   } // This line is removed
    // }); // This line is removed
    
    console.log(`🚀 Step 4: Generating ${wordCount}-word article with enhanced prompts...`);
    
    const result = await model.generateContent({
      contents: [
        {
          role: 'user',
          parts: [{ text: systemPrompt + '\n\n' + userPrompt }]
        }
      ],
    });

    const generatedContent = result.response.text();
    
    // Verify word count
    const actualWordCount = generatedContent.split(/\s+/).filter(word => word.length > 0).length;
    const tolerance = Math.ceil(wordCount * 0.25); // 25% tolerance for more flexibility
    const minWords = wordCount - tolerance;
    const maxWords = wordCount + tolerance;
    
    console.log(`✅ Generated article with ${actualWordCount} words (target: ${wordCount})`);
    console.log(`   Tolerance: ±${tolerance} words (${minWords}-${maxWords})`);
    console.log(`   Accuracy: ${Math.abs(actualWordCount - wordCount)} words off (${((1 - Math.abs(actualWordCount - wordCount) / wordCount) * 100).toFixed(1)}% accurate)`);

    // Calculate section word targets for structured generation
    const introWords = Math.ceil(wordCount * 0.15); // 15% intro
    const mainBodyWords = Math.ceil(wordCount * 0.70); // 70% main content
    const conclusionWords = Math.ceil(wordCount * 0.15); // 15% conclusion
    
    // If word count is significantly off, attempt one retry with adjusted prompt
    if (actualWordCount < minWords || actualWordCount > maxWords) {
      console.log(`Word count outside tolerance. Attempting retry...`);
      
      const retryPrompt = `🚨 CRITICAL WORD COUNT ERROR 🚨
      
The previous response was ${actualWordCount} words but must be EXACTLY ${wordCount} words.

🎯 MANDATORY: Write exactly ${wordCount} words. Count every single word as you write.

${actualWordCount < minWords ? `❌ PREVIOUS ATTEMPT TOO SHORT: ${actualWordCount} words (need ${wordCount - actualWordCount} more words)
✅ SOLUTION: Write MORE content - add examples, detailed explanations, case studies, or additional sections` : ''}

${actualWordCount > maxWords ? `❌ PREVIOUS ATTEMPT TOO LONG: ${actualWordCount} words (need to remove ${actualWordCount - wordCount} words)  
✅ SOLUTION: Write LESS content - be more concise, remove unnecessary details, shorter paragraphs` : ''}

${systemPrompt}

🎯 WORD COUNT TARGET: ${wordCount} WORDS EXACTLY

📊 REQUIRED STRUCTURE:
- INTRODUCTION: ${introWords} words 
- MAIN BODY: ${mainBodyWords} words
- CONCLUSION: ${conclusionWords} words
- TOTAL: ${wordCount} words

📝 SPECIFIC CORRECTIONS NEEDED:
${actualWordCount < minWords ? `- ADD ${wordCount - actualWordCount} MORE WORDS by expanding sections with:
  * More detailed explanations
  * Additional examples from the videos
  * Practical applications and tips
  * Case studies and real-world scenarios
  * Historical context or background
  * Step-by-step processes
  * Quotes and data from the transcripts` : `- REMOVE ${actualWordCount - wordCount} WORDS by:
  * Being more concise in explanations
  * Removing redundant information
  * Shorter paragraphs
  * Eliminating unnecessary details`}

⚠️ CRITICAL FORMATTING RULES:
- NO "(Word Count: X)" anywhere in output
- Use DESCRIPTIVE headings, not "Section 1", "Section 2"
- Keep paragraphs SHORT (2-4 sentences max)
- Use SIMPLE, easy-to-read language
- Include transition words for better flow
- Proper markdown: # for title, ## for descriptive sections, ### for subsections

${userPrompt}

🚨 FINAL WARNING: This is your last chance. Requirements:
- Write exactly ${wordCount} words (count internally)
- Use DESCRIPTIVE headings (NOT "Section 1", "Section 2")
- SHORT paragraphs (2-4 sentences) with SIMPLE language
- NO "(Word Count: X)" anywhere in output
- Easy-to-read, SEO-optimized, publication-ready article`;

      try {
        const retryResult = await model.generateContent({
          contents: [
            {
              role: 'user',
              parts: [{ text: retryPrompt }]
            }
          ],
          generationConfig: {
            temperature: 0.7, // Slightly lower temperature for more controlled output
            topK: 40,
            topP: 0.95,
            // No token restrictions for retry either
          },
        });

        const retryContent = retryResult.response.text();
        const retryWordCount = retryContent.split(/\s+/).filter(word => word.length > 0).length;
        
        console.log(`Retry word count: ${retryWordCount}`);
        
        // Use retry result if it's better, otherwise use original
        if (Math.abs(retryWordCount - wordCount) < Math.abs(actualWordCount - wordCount)) {
          console.log('Using retry result as it has better word count');
          return NextResponse.json({
            success: true,
            article: {
              id: 'temp-' + Date.now(),
              title: topic,
              content: retryContent,
              metadata: {
                processedVideos: videoLinks.length - failedVideos.length,
                failedVideos,
                videoTitles,
                language,
                tone,
                wordCount: retryWordCount,
                targetWordCount: wordCount,
                wordCountStatus: 'retry_improved'
              }
            }
          });
        }
      } catch (retryError) {
        console.error('Retry failed:', retryError);
        // Continue with original result
      }
    }

    // Save to database
    const article = await prisma.content.create({
      data: {
        title: topic,
        content: generatedContent,
        type: 'video_alchemy',
        userId: session.user.id,
        metadata: JSON.stringify({
          videoLinks,
          videoTitles,
          failedVideos,
          tone,
          wordCount,
          language,
          customInstructions: customInstructions || null,
          generatedAt: new Date().toISOString()
        }),
        wordCount: generatedContent.split(/\s+/).filter(word => word.length > 0).length,
        tone,
        language: language === 'hindi' ? 'hi' : language === 'french' ? 'fr' : 'en'
      }
    });

    // Calculate final word count
    const finalWordCount = generatedContent.split(/\s+/).filter(word => word.length > 0).length;
    const wordCountAccuracy = Math.abs(finalWordCount - wordCount);
    const wordCountStatus = wordCountAccuracy <= Math.ceil(wordCount * 0.25) ? 'within_tolerance' : 'outside_tolerance';

    // Return the generated article
    return NextResponse.json({
      success: true,
      article: {
        id: article.id,
        title: topic,
        content: generatedContent,
        metadata: {
          processedVideos: videoLinks.length - failedVideos.length,
          failedVideos,
          videoTitles,
          language,
          tone,
          wordCount: finalWordCount,
          targetWordCount: wordCount,
          wordCountAccuracy,
          wordCountStatus
        }
      }
    });

  } catch (error) {
    console.error('VideoAlchemy generation error:', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to generate article' 
    }, { status: 500 });
  }
} 