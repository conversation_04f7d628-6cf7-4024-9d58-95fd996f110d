import { NextRequest, NextResponse } from 'next/server'
import { InvincibleSupervisor } from '@/lib/agents/invincible/InvincibleSupervisor'
import { InvincibleRequest } from '@/lib/agents/invincible/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request
    if (!body.topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      )
    }

    const invincibleRequest: InvincibleRequest = {
      topic: body.topic,
      wordCount: body.wordCount || 1500,
      tone: body.tone || 'Professional and informative',
      targetAudience: body.targetAudience || 'General audience',
      includeYouTube: body.includeYouTube !== false,
      additionalInstructions: body.additionalInstructions || ''
    }

    console.log('🚀 Invincible .1V API: Starting content generation')
    console.log('📝 Request:', invincibleRequest)

    // Initialize supervisor
    const supervisor = new InvincibleSupervisor()

    // Generate content
    const result = await supervisor.generateContent(invincibleRequest)

    if (result.success) {
      console.log('✅ Invincible .1V API: Content generation successful')
      return NextResponse.json(result)
    } else {
      console.error('❌ Invincible .1V API: Content generation failed')
      return NextResponse.json(
        { error: result.error || 'Content generation failed' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('❌ Invincible .1V API Error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  // Return agent information
  const supervisor = new InvincibleSupervisor()
  const agentInfo = supervisor.getAgentInfo()

  return NextResponse.json({
    status: 'active',
    agent: agentInfo,
    endpoints: {
      generate: '/api/invincible (POST)',
      stream: '/api/invincible/stream (POST)'
    },
    timestamp: new Date().toISOString()
  })
}