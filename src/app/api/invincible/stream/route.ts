import { NextRequest, NextResponse } from 'next/server'
import { InvincibleSupervisor } from '@/lib/agents/invincible/InvincibleSupervisor'
import { InvincibleRequest, ProgressUpdate } from '@/lib/agents/invincible/types'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate request
    if (!body.topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      )
    }

    const invincibleRequest: InvincibleRequest = {
      topic: body.topic,
      wordCount: body.wordCount || 1500,
      tone: body.tone || 'Professional and informative',
      targetAudience: body.targetAudience || 'General audience',
      includeYouTube: body.includeYouTube !== false,
      additionalInstructions: body.additionalInstructions || ''
    }

    console.log('🚀 Invincible .1V Stream API: Starting content generation')
    console.log('📝 Request:', invincibleRequest)

    // Create SSE stream
    const stream = new ReadableStream({
      start(controller) {
        (async () => {
          try {
            // Initialize supervisor
            const supervisor = new InvincibleSupervisor()

            // Generate content with progress updates
            const result = await supervisor.generateContent(
              invincibleRequest,
              (update: ProgressUpdate) => {
                // Send progress update via SSE
                sendSSEMessage(controller, {
                  type: 'progress',
                  data: update
                })
              }
            )

            // Send final result
            if (result.success) {
              sendSSEMessage(controller, {
                type: 'completed',
                data: result
              })
              console.log('✅ Invincible .1V Stream: Content generation completed')
            } else {
              sendSSEMessage(controller, {
                type: 'error',
                data: { error: result.error || 'Content generation failed' }
              })
              console.error('❌ Invincible .1V Stream: Content generation failed')
            }

            controller.close()

          } catch (error) {
            console.error('❌ Invincible .1V Stream Error:', error)
            sendSSEMessage(controller, {
              type: 'error',
              data: { 
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
              }
            })
            controller.close()
          }
        })()
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    })

  } catch (error) {
    console.error('❌ Invincible .1V Stream API Error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// SSE helper function
function sendSSEMessage(controller: ReadableStreamDefaultController, data: any) {
  const message = `data: ${JSON.stringify(data)}\n\n`
  controller.enqueue(new TextEncoder().encode(message))
}
