import { NextRequest, NextResponse } from 'next/server'
import { GoogleSearchService } from '@/lib/search'
import { GeminiService } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
    const { urls } = await request.json()

    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return NextResponse.json(
        { error: 'URLs array is required' },
        { status: 400 }
      )
    }

    console.log('🔍 Extracting keywords from competition pages...')
    const searchService = new GoogleSearchService()
    const gemini = new GeminiService()
    
    // Extract content from selected URLs
    const extractionPromises = urls.map(async (url: string) => {
      try {
        const content = await searchService.extractContent(url)
        return { url, content, success: true }
      } catch (error) {
        console.error(`Failed to extract from ${url}:`, error)
        return { url, content: '', success: false }
      }
    })
    
    const extractedData = await Promise.all(extractionPromises)
    const successfulExtractions = extractedData.filter(item => item.success && item.content.length > 100)
    
    if (successfulExtractions.length === 0) {
      return NextResponse.json(
        { error: 'Failed to extract content from any of the provided URLs' },
        { status: 400 }
      )
    }
    
    // Combine all content for keyword extraction
    const combinedContent = successfulExtractions
      .map(item => item.content.substring(0, 1000)) // Limit content per URL
      .join('\n\n')
    
    // Extract keywords using Gemini
    const keywords = await gemini.extractKeywordsFromContent(combinedContent)
    
    console.log(`🎯 Extracted keywords: ${keywords}`)

    return NextResponse.json({
      success: true,
      keywords: keywords.trim(),
      extractedFrom: successfulExtractions.length,
      totalUrls: urls.length
    })

  } catch (error) {
    console.error('Keyword extraction error:', error)
    return NextResponse.json(
      { error: 'Failed to extract keywords from competition pages' },
      { status: 500 }
    )
  }
}
