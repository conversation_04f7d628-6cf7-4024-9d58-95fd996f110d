import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { YouTubeService } from '@/lib/youtube-service';
import { GeminiService } from '@/lib/gemini';

interface AnalysisRequest {
  youtubeUrl: string;
}

interface VideoAnalysis {
  videoTitle: string;
  videoUrl: string;
  analysisType: 'single' | 'multiple';
  topics: {
    id: string;
    title: string;
    summary: string[];
    timestamp?: string;
  }[];
}

// Utility function to extract video ID from YouTube URLs
function extractVideoId(url: string): string | null {
  const patterns = [
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/,
    /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([a-zA-Z0-9_-]{11})/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  
  return null;
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { youtubeUrl }: AnalysisRequest = await request.json();

    if (!youtubeUrl) {
      return NextResponse.json({ error: 'YouTube URL is required' }, { status: 400 });
    }

    // Extract video ID
    const videoId = extractVideoId(youtubeUrl);
    if (!videoId) {
      return NextResponse.json({ error: 'Invalid YouTube URL' }, { status: 400 });
    }

    console.log(`🎬 Megatron: Analyzing video ${videoId}`);

    // Initialize services
    const youtube = new YouTubeService();
    const gemini = new GeminiService();

    // Get video metadata
    console.log('📋 Fetching video metadata...');
    const videoInfo = await youtube.getVideoMetadata(videoId);
    if (!videoInfo) {
      return NextResponse.json({ error: 'Could not fetch video information' }, { status: 400 });
    }

    // Extract captions
    console.log('📝 Extracting video captions...');
    const captions = await youtube.extractCaptions(videoId, 'en');
    if (!captions || captions.length === 0) {
      return NextResponse.json({ error: 'Could not extract captions from this video' }, { status: 400 });
    }

    // Combine captions into transcript
    const transcript = captions.map(caption => caption.text).join(' ');
    console.log(`📄 Transcript extracted: ${transcript.length} characters`);

    // Analyze with Gemini
    console.log('🧠 Analyzing content with Gemini...');
    const analysisPrompt = `
You are Megatron, an advanced YouTube video analyzer. Analyze the following video transcript and determine if it covers a single topic or multiple distinct topics.

Video Title: "${videoInfo.title}"
Video Transcript: "${transcript}"

Your task:
1. Determine if this video covers ONE main topic or MULTIPLE distinct topics
2. Extract the key topics and create structured summaries
3. For each topic, provide 3-5 bullet points of what was covered

Response format (JSON only):
{
  "analysisType": "single" | "multiple",
  "topics": [
    {
      "id": "unique_id",
      "title": "Clear, descriptive topic title",
      "summary": [
        "Key point 1 covered in the video",
        "Key point 2 covered in the video",
        "Key point 3 covered in the video",
        "Key point 4 covered in the video (if applicable)",
        "Key point 5 covered in the video (if applicable)"
      ],
      "timestamp": "approximate time range (optional)"
    }
  ]
}

Guidelines:
- If the video has one main theme with subtopics, classify as "single"
- If the video covers completely different subjects (like news roundups, multiple product reviews, etc.), classify as "multiple"
- Each topic should be substantial enough to create a full article or script
- Topic titles should be clear and specific
- Summary points should be actionable and informative
- Keep topic titles under 60 characters
- Each summary point should be under 100 characters

Analyze now:`;

    const result = await gemini.generateContent(analysisPrompt, {
      temperature: 0.3,
      maxOutputTokens: 4096
    });

    // Parse the JSON response
    let analysisData;
    try {
      // Extract JSON from the response
      const jsonMatch = result.response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      analysisData = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      console.error('Failed to parse Gemini response:', parseError);
      return NextResponse.json({ error: 'Failed to analyze video content' }, { status: 500 });
    }

    // Validate and structure the response
    const analysis: VideoAnalysis = {
      videoTitle: videoInfo.title,
      videoUrl: youtubeUrl,
      analysisType: analysisData.analysisType || 'single',
      topics: (analysisData.topics || []).map((topic: any, index: number) => ({
        id: topic.id || `topic_${index + 1}`,
        title: topic.title || `Topic ${index + 1}`,
        summary: Array.isArray(topic.summary) ? topic.summary : [],
        timestamp: topic.timestamp
      }))
    };

    // Ensure we have at least one topic
    if (analysis.topics.length === 0) {
      analysis.topics = [{
        id: 'main_topic',
        title: videoInfo.title,
        summary: ['Video content analysis', 'Key insights and information', 'Main discussion points'],
        timestamp: undefined
      }];
    }

    console.log(`✅ Analysis complete: ${analysis.analysisType} topic(s), ${analysis.topics.length} items`);

    return NextResponse.json({
      success: true,
      analysis
    });

  } catch (error) {
    console.error('Megatron analysis error:', error);
    return NextResponse.json(
      { error: 'Failed to analyze video' },
      { status: 500 }
    );
  }
}
