import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { url } = await request.json()

    if (!url) {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      )
    }

    // Validate URL format
    let validUrl: URL
    try {
      validUrl = new URL(url)
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      )
    }

    // Security check - only allow HTTP/HTTPS
    if (!['http:', 'https:'].includes(validUrl.protocol)) {
      return NextResponse.json(
        { error: 'Only HTTP and HTTPS URLs are allowed' },
        { status: 400 }
      )
    }

    console.log('🌐 Scraping URL:', validUrl.href)

    try {
      // Fetch the webpage content
      const response = await fetch(validUrl.href, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; SocialMediaBot/1.0)',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
        // Add timeout
        signal: AbortSignal.timeout(10000) // 10 second timeout
      })

      if (!response.ok) {
        console.error('❌ HTTP Error:', response.status, response.statusText)
        return NextResponse.json(
          { error: `Failed to fetch URL: ${response.status} ${response.statusText}` },
          { status: 400 }
        )
      }

      const html = await response.text()
      
      // Extract text content from HTML
      const extractedContent = extractTextFromHTML(html)
      
      if (!extractedContent || extractedContent.trim().length < 50) {
        return NextResponse.json(
          { error: 'Unable to extract meaningful content from the URL' },
          { status: 400 }
        )
      }

      console.log('✅ Successfully scraped content:', extractedContent.length, 'characters')

      return NextResponse.json({
        success: true,
        content: extractedContent,
        url: validUrl.href,
        scrapedAt: new Date().toISOString()
      })

    } catch (fetchError) {
      console.error('❌ Failed to fetch URL:', fetchError)
      
      if (fetchError instanceof Error) {
        if (fetchError.name === 'TimeoutError') {
          return NextResponse.json(
            { error: 'Request timeout - the website took too long to respond' },
            { status: 408 }
          )
        }
        
        return NextResponse.json(
          { error: `Network error: ${fetchError.message}` },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to fetch URL content' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('URL scraping error:', error)
    return NextResponse.json(
      { error: 'Failed to scrape URL' },
      { status: 500 }
    )
  }
}

function extractTextFromHTML(html: string): string {
  // Remove script and style elements
  let cleaned = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
  cleaned = cleaned.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
  
  // Remove HTML comments
  cleaned = cleaned.replace(/<!--[\s\S]*?-->/g, '')
  
  // Extract title
  const titleMatch = cleaned.match(/<title[^>]*>([\s\S]*?)<\/title>/i)
  const title = titleMatch ? titleMatch[1].trim() : ''
  
  // Extract meta description
  const metaDescMatch = cleaned.match(/<meta[^>]*name=['"]description['"][^>]*content=['"]([^'"]*)['"]/i)
  const metaDesc = metaDescMatch ? metaDescMatch[1].trim() : ''
  
  // Extract content from common content containers
  const contentSelectors = [
    /<article[^>]*>([\s\S]*?)<\/article>/gi,
    /<main[^>]*>([\s\S]*?)<\/main>/gi,
    /<div[^>]*class=['"][^'"]*content[^'"]*['"][^>]*>([\s\S]*?)<\/div>/gi,
    /<div[^>]*class=['"][^'"]*post[^'"]*['"][^>]*>([\s\S]*?)<\/div>/gi,
    /<div[^>]*class=['"][^'"]*entry[^'"]*['"][^>]*>([\s\S]*?)<\/div>/gi
  ]
  
  let content = ''
  for (const selector of contentSelectors) {
    const matches = cleaned.match(selector)
    if (matches && matches.length > 0) {
      content = matches.join(' ')
      break
    }
  }
  
  // If no specific content found, extract from body
  if (!content) {
    const bodyMatch = cleaned.match(/<body[^>]*>([\s\S]*?)<\/body>/i)
    content = bodyMatch ? bodyMatch[1] : cleaned
  }
  
  // Remove all HTML tags
  content = content.replace(/<[^>]*>/g, ' ')
  
  // Decode HTML entities
  content = content
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#x27;/g, "'")
    .replace(/&#x2F;/g, '/')
    .replace(/&#39;/g, "'")
    .replace(/&nbsp;/g, ' ')
  
  // Clean up whitespace
  content = content
    .replace(/\s+/g, ' ')
    .replace(/\n\s*\n/g, '\n')
    .trim()
  
  // Combine title, description, and content
  let result = ''
  if (title) result += `Title: ${title}\n\n`
  if (metaDesc) result += `Description: ${metaDesc}\n\n`
  if (content) result += `Content: ${content}`
  
  // Limit to reasonable length (about 4000 characters for context)
  if (result.length > 4000) {
    result = result.substring(0, 4000) + '...'
  }
  
  return result.trim()
}