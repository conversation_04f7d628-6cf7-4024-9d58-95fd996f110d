import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { QuotaManager } from '@/lib/quota'
import { prisma } from '@/lib/prisma'
import { GeminiService } from '@/lib/gemini'
import { TavilySearchService, searchAndExtractWebTavily } from '@/lib/search'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check quota
    const quotaCheck = await QuotaManager.checkQuota(session.user.id, 'blog_posts')
    if (!quotaCheck.hasQuota) {
      return NextResponse.json(
        { 
          error: 'Blog post quota exceeded',
          quota: {
            used: quotaCheck.used,
            limit: quotaCheck.limit,
            resetDate: quotaCheck.resetDate
          }
        },
        { status: 429 }
      )
    }

    const { topic, wordCount, tone, includeResearch, targetKeyword, competitors, targetAudience, title } = await request.json()

    if (!topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      )
    }

    const gemini = new GeminiService()
    let researchData = ''

    // Perform research if requested
    if (includeResearch) {
      try {
        console.log('🔍 Starting research phase...')
        const searchService = new TavilySearchService()

        // Extract keywords from topic using Gemini
        const keywords = await gemini.extractKeywords(topic)

        console.log(`🎯 Extracted keywords: ${keywords.trim()}`)

        // Perform search and content extraction using exact keywords
        const { extractedContent } = await searchAndExtractWebTavily(
          keywords.trim(),
          5
        )

        if (extractedContent.length > 0) {
          // Format research data with URLs for inline linking
          researchData = extractedContent
            .map((item, index) => {
              return `Source URL: ${item.url}\nSource Title: ${item.title || 'Untitled'}\nContent: ${item.content}\n`
            })
            .join('\n---\n\n')
          console.log(`📚 Research completed: ${researchData.length} characters of research data`)
        } else {
          console.log('⚠️ No research data extracted, proceeding without research')
        }
      } catch (researchError) {
        console.error('Research phase failed:', researchError)
        console.log('📝 Proceeding with content generation without research data')
      }
    }

    // Generate blog post with enhanced word count enforcement
    console.log('✍️ Starting content generation...')
    const targetWordCount = wordCount || 1000

    const blogPost = await gemini.generateBlogPost(
      topic,
      targetWordCount,
      tone || 'professional',
      researchData,
      {
        targetKeyword,
        competitors,
        targetAudience,
        title
      }
    )

    // Validate word count (for logging purposes)
    const actualWordCount = blogPost.split(/\s+/).filter(w => w.length > 0).length
    const minWords = Math.floor(targetWordCount * 0.95)
    const maxWords = Math.ceil(targetWordCount * 1.05)

    console.log(`📊 Generated ${actualWordCount} words (target: ${targetWordCount}, range: ${minWords}-${maxWords})`)

    if (actualWordCount >= minWords && actualWordCount <= maxWords) {
      console.log(`✅ Word count requirement met`)
    } else {
      console.log(`⚠️ Word count outside target range`)
    }

    console.log('✅ Blog post generated successfully')

    // Use quota
    const quotaUsed = await QuotaManager.useQuota(session.user.id, 'blog_posts')
    if (!quotaUsed) {
      console.error('Failed to update quota after successful generation')
    }

    // Save content to database
    try {
      await prisma.content.create({
        data: {
          userId: session.user.id,
          type: 'blog',
          title: title || topic,
          content: blogPost,
          wordCount: actualWordCount,
          tone: tone || 'professional',
          metadata: JSON.stringify({
            targetKeyword,
            competitors,
            targetAudience,
            includeResearch,
            researchUsed: !!researchData
          })
        }
      })
    } catch (dbError) {
      console.error('Failed to save content to database:', dbError)
      // Don't fail the request if saving fails
    }

    return NextResponse.json({
      success: true,
      content: blogPost,
      researchUsed: !!researchData,
      researchDataLength: researchData.length,
      quota: {
        used: quotaCheck.used + 1,
        limit: quotaCheck.limit,
        remaining: quotaCheck.limit === -1 ? -1 : quotaCheck.limit - (quotaCheck.used + 1)
      }
    })

  } catch (error) {
    console.error('Blog generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate blog post' },
      { status: 500 }
    )
  }
}
