import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { QuotaManager } from '@/lib/quota'
import { prisma } from '@/lib/prisma'
import { GeminiService } from '@/lib/gemini'

// Platform configurations with character limits and optimization rules
const PLATFORM_CONFIG = {
  twitter: {
    name: 'Twitter/X',
    maxLength: 280,
    optimalLength: { min: 71, max: 100 },
    hashtagLimit: 3,
    style: 'concise, engaging, news-worthy',
    features: ['hashtags', 'mentions', 'threads']
  },
  linkedin: {
    name: 'LinkedIn',
    maxLength: 3000,
    optimalLength: { min: 50, max: 150 },
    hashtagLimit: 5,
    style: 'professional, thought-leadership, business-focused',
    features: ['hashtags', 'mentions', 'professional']
  },
  facebook: {
    name: 'Facebook',
    maxLength: 63206,
    optimalLength: { min: 40, max: 80 },
    hashtagLimit: 3,
    style: 'conversational, community-focused, personal',
    features: ['hashtags', 'mentions', 'emojis']
  }
}

// Content type templates
const CONTENT_TYPE_PROMPTS = {
  promotional: 'Create promotional content that highlights benefits and drives action without being overly sales-y',
  educational: 'Create educational content that teaches something valuable and positions expertise',
  engaging: 'Create engaging content that encourages interaction, comments, and shares',
  behind_scenes: 'Create behind-the-scenes content that shows authentic process and builds connection',
  news: 'Create news and update content that informs and establishes thought leadership',
  inspirational: 'Create inspirational content that motivates and resonates emotionally'
}

// Hashtag research function
function generatePlatformHashtags(platform: string, keywords: string[], contentType: string): string[] {
  const platformConfig = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG]
  if (!platformConfig) return []

  const baseHashtags: Record<string, string[]> = {
    twitter: ['trending', 'viral', 'tech', 'ai', 'startup', 'innovation', 'business', 'news'],
    linkedin: ['professional', 'career', 'business', 'leadership', 'industry', 'networking', 'growth', 'strategy'],
    facebook: ['community', 'family', 'lifestyle', 'local', 'events', 'social', 'connect', 'share']
  }

  const contentTypeHashtags: Record<string, string[]> = {
    promotional: ['sale', 'offer', 'new', 'launch', 'exclusive', 'deal'],
    educational: ['tips', 'howto', 'learn', 'tutorial', 'guide', 'education'],
    engaging: ['question', 'poll', 'discuss', 'thoughts', 'opinions', 'community'],
    behind_scenes: ['bts', 'process', 'making', 'team', 'work', 'culture'],
    news: ['news', 'update', 'announcement', 'breaking', 'latest', 'industry'],
    inspirational: ['motivation', 'inspiration', 'success', 'goals', 'mindset', 'quotes']
  }

  // Combine platform-specific, content-type, and keyword-based hashtags
  const platformTags = baseHashtags[platform] || []
  const typeTags = contentTypeHashtags[contentType] || []
  const keywordTags = keywords.map(k => k.toLowerCase().replace(/\s+/g, ''))

  const allTags = [...platformTags, ...typeTags, ...keywordTags]
  
  // Return limited number based on platform
  return allTags.slice(0, platformConfig.hashtagLimit)
}

// Engagement score calculator
function calculateEngagementScore(content: string, platform: string, hashtags: string[]): number {
  const platformConfig = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG]
  if (!platformConfig) return 60

  // Ensure content is a string
  const contentStr = typeof content === 'string' ? content : String(content || '')
  
  let score = 70 // Base score

  // Character count optimization
  const charCount = contentStr.length
  if (charCount >= platformConfig.optimalLength.min && charCount <= platformConfig.optimalLength.max) {
    score += 15
  } else if (charCount > platformConfig.maxLength) {
    score -= 20
  } else if (charCount < platformConfig.optimalLength.min) {
    score -= 10
  }

  // Hashtag optimization
  if (hashtags.length > 0 && hashtags.length <= platformConfig.hashtagLimit) {
    score += 10
  }

  // Content quality indicators
  if (contentStr.includes('?')) score += 5 // Questions increase engagement
  if (contentStr.match(/[!]{1,2}/g)) score += 3 // Exclamation points
  if (contentStr.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu)) score += 5 // Emojis

  return Math.min(100, Math.max(0, score))
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check quota
    const quotaCheck = await QuotaManager.checkQuota(session.user.id, 'social_media')
    if (!quotaCheck.hasQuota) {
      return NextResponse.json(
        { 
          error: 'Social media quota exceeded',
          quota: {
            used: quotaCheck.used,
            limit: quotaCheck.limit,
            resetDate: quotaCheck.resetDate
          }
        },
        { status: 429 }
      )
    }

    const { 
      content, 
      platforms, 
      contentType, 
      tone, 
      targetAudience, 
      keywords, 
      sourceUrl,
      scrapedContent,
      includeHashtags, 
      includeEmojis, 
      includeCTA 
    } = await request.json()

    if (!content || !platforms || platforms.length === 0) {
      return NextResponse.json(
        { error: 'Content and platforms are required' },
        { status: 400 }
      )
    }

    const gemini = new GeminiService()
    const keywordList = keywords ? keywords.split(',').map((k: string) => k.trim()).filter(Boolean) : []
    const generated: Record<string, any> = {}

    console.log('🚀 Generating social media content for platforms:', platforms)

    // Generate content for each platform
    for (const platform of platforms) {
      const platformConfig = PLATFORM_CONFIG[platform as keyof typeof PLATFORM_CONFIG]
      if (!platformConfig) continue

      console.log(`📱 Generating content for ${platformConfig.name}...`)

      // Build platform-specific prompt with optional context
      let contextSection = ''
      if (scrapedContent && scrapedContent.trim()) {
        contextSection = `
REFERENCE CONTEXT (Use as inspiration and source material):
${scrapedContent.substring(0, 2000)}...

INSTRUCTIONS FOR CONTEXT USE:
- Use the reference context to understand the topic better
- Extract key insights, facts, or interesting points from the context
- Create original content inspired by but not copying the reference material
- Incorporate relevant information that adds value to your social media post
`
      }

      const prompt = `Create a ${tone} social media post for ${platformConfig.name} with the following requirements:

CONTENT: ${content}
CONTENT TYPE: ${CONTENT_TYPE_PROMPTS[contentType as keyof typeof CONTENT_TYPE_PROMPTS]}
PLATFORM STYLE: ${platformConfig.style}
TARGET AUDIENCE: ${targetAudience || 'General audience'}
KEYWORDS TO INCLUDE: ${keywordList.join(', ')}
${sourceUrl ? `SOURCE URL: ${sourceUrl}` : ''}
${contextSection}
REQUIREMENTS:
- Keep content between ${platformConfig.optimalLength.min}-${platformConfig.optimalLength.max} characters for optimal engagement
- Maximum ${platformConfig.maxLength} characters (STRICT LIMIT)
- Platform style: ${platformConfig.style}
- Tone: ${tone}
${includeEmojis ? '- Include relevant emojis to increase engagement' : '- No emojis'}
${includeCTA ? '- Include a subtle call-to-action' : '- No call-to-action'}
- Make it platform-appropriate and engaging
- Focus on the main message: ${content}
${scrapedContent ? '- Use insights from the reference context to enhance your content' : ''}

OUTPUT FORMAT:
Return only the social media post content, nothing else. No quotes, no extra text, just the post content.`

      try {
        const generatedResult = await gemini.generateContent(prompt)
        const generatedPost = generatedResult.response // Extract the actual content string
        
        // Generate hashtags if requested
        let hashtags: string[] = []
        if (includeHashtags) {
          hashtags = generatePlatformHashtags(platform, keywordList, contentType)
        }

        // Ensure we have a valid string
        const contentStr = typeof generatedPost === 'string' ? generatedPost.trim() : String(generatedPost || '').trim()
        
        // Calculate engagement score
        const engagementScore = calculateEngagementScore(contentStr, platform, hashtags)

        // Add to results
        generated[platform] = {
          content: contentStr,
          hashtags,
          characterCount: contentStr.length,
          engagementScore,
          optimizationTips: []
        }

        // Add optimization tips
        const tips = []
        if (contentStr.length > platformConfig.optimalLength.max) {
          tips.push(`Consider shortening for better engagement (optimal: ${platformConfig.optimalLength.min}-${platformConfig.optimalLength.max} chars)`)
        }
        if (hashtags.length === 0 && includeHashtags) {
          tips.push('Add relevant hashtags to increase discoverability')
        }
        if (!contentStr.includes('?') && contentType === 'engaging') {
          tips.push('Consider adding a question to encourage interaction')
        }
        
        generated[platform].optimizationTips = tips

        console.log(`✅ Generated ${contentStr.length} character post for ${platformConfig.name}`)

      } catch (error) {
        console.error(`❌ Failed to generate content for ${platform}:`, error)
        generated[platform] = {
          content: `Error generating content for ${platformConfig.name}`,
          hashtags: [],
          characterCount: 0,
          engagementScore: 0,
          optimizationTips: ['Generation failed - please try again']
        }
      }
    }

    // Use quota
    const quotaUsed = await QuotaManager.useQuota(session.user.id, 'social_media')
    if (!quotaUsed) {
      console.error('Failed to update quota after successful generation')
    }

    // Save to database
    try {
      await prisma.content.create({
        data: {
          userId: session.user.id,
          type: 'social_media',
          title: `Social Media Posts - ${new Date().toLocaleDateString()}`,
          content: JSON.stringify(generated),
          metadata: JSON.stringify({
            platforms,
            contentType,
            tone,
            targetAudience,
            keywords: keywordList,
            sourceUrl: sourceUrl || null,
            hasScrapedContent: !!(scrapedContent && scrapedContent.trim()),
            options: {
              includeHashtags,
              includeEmojis,
              includeCTA
            },
            generatedAt: new Date().toISOString(),
            originalContent: content
          })
        }
      })
    } catch (dbError) {
      console.error('Failed to save social media content to database:', dbError)
      // Don't fail the request if saving fails
    }

    console.log('🎉 Social media content generation completed successfully')

    return NextResponse.json({
      success: true,
      generated,
      quota: {
        used: quotaCheck.used + 1,
        limit: quotaCheck.limit,
        remaining: quotaCheck.limit === -1 ? -1 : quotaCheck.limit - (quotaCheck.used + 1)
      }
    })

  } catch (error) {
    console.error('Social media generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate social media content' },
      { status: 500 }
    )
  }
}