import { NextRequest } from 'next/server'

// Store for progress tracking
const progressStore = new Map<string, any>()

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const sessionId = searchParams.get('sessionId')
  
  if (!sessionId) {
    return new Response('Session ID required', { status: 400 })
  }

  // Set up SSE headers
  const headers = new Headers({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET',
    'Access-Control-Allow-Headers': 'Content-Type',
  })

  // Create readable stream for SSE
  const stream = new ReadableStream({
    start(controller) {
      let isCompleted = false
      
      // Send initial connection message
      try {
        controller.enqueue(`data: ${JSON.stringify({
          type: 'connected',
          sessionId,
          timestamp: Date.now()
        })}\n\n`)
      } catch (error) {
        console.log('Controller already closed during initialization')
        return
      }

      // Set up interval to check for progress updates
      const interval = setInterval(() => {
        if (isCompleted) return
        
        try {
          const progress = progressStore.get(sessionId)
          if (progress) {
            controller.enqueue(`data: ${JSON.stringify({
              type: 'progress',
              ...progress,
              timestamp: Date.now()
            })}\n\n`)
            
            // If completed, clean up and close
            if (progress.phase === 'completed') {
              isCompleted = true
              progressStore.delete(sessionId)
              clearInterval(interval)
              
              try {
                controller.enqueue(`data: ${JSON.stringify({
                  type: 'completed',
                  timestamp: Date.now()
                })}\n\n`)
                controller.close()
              } catch (closeError) {
                console.log('Controller already closed during completion')
              }
            }
          }
        } catch (error) {
          console.log('Controller closed during progress update')
          clearInterval(interval)
          progressStore.delete(sessionId)
        }
      }, 1000) // Check every second

      // Clean up on close
      request.signal?.addEventListener('abort', () => {
        if (!isCompleted) {
          isCompleted = true
          clearInterval(interval)
          progressStore.delete(sessionId)
          try {
            if (!controller.closed) {
              controller.close()
            }
          } catch (error) {
            console.log('Controller already closed during abort')
          }
        }
      })

      // Auto-cleanup after 10 minutes
      const timeout = setTimeout(() => {
        if (!isCompleted) {
          isCompleted = true
          clearInterval(interval)
          progressStore.delete(sessionId)
          try {
            if (!controller.closed) {
              controller.close()
            }
          } catch (error) {
            console.log('Controller already closed during timeout cleanup')
          }
        }
      }, 600000)

      // Store cleanup function for the session
      const cleanup = () => {
        if (!isCompleted) {
          isCompleted = true
          clearInterval(interval)
          clearTimeout(timeout)
          progressStore.delete(sessionId)
        }
      }

      // Store cleanup for external access if needed
      progressStore.set(`${sessionId}_cleanup`, cleanup)
    }
  })

  return new Response(stream, { headers })
}

export async function POST(request: NextRequest) {
  try {
    const { sessionId, progress } = await request.json()
    
    if (!sessionId) {
      return new Response('Session ID required', { status: 400 })
    }

    // Store progress update
    progressStore.set(sessionId, progress)
    
    // If this is a completion or error, trigger cleanup after a short delay
    if (progress.phase === 'completed' || progress.phase === 'error') {
      setTimeout(() => {
        const cleanup = progressStore.get(`${sessionId}_cleanup`)
        if (cleanup && typeof cleanup === 'function') {
          cleanup()
          progressStore.delete(`${sessionId}_cleanup`)
        }
      }, 2000) // Give time for final messages to be sent
    }
    
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error('Progress update error:', error)
    return new Response('Internal server error', { status: 500 })
  }
}

// Utility function to update progress (internal use only)
const updateProgress = (sessionId: string, progress: any) => {
  progressStore.set(sessionId, progress)
}