import { NextRequest, NextResponse } from 'next/server'

// This would typically connect to your database
// For now, we'll simulate with localStorage-like behavior

export async function GET(request: NextRequest) {
  try {
    // In a real app, you would:
    // 1. Authenticate the user
    // 2. Fetch settings from database
    // 3. Return user-specific settings

    // For now, return default settings
    const defaultSettings = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      bio: 'Content creator and digital marketer passionate about AI-powered writing.',
      avatar: '',
      defaultLanguage: 'en',
      timezone: 'America/New_York',
      defaultWordCount: 1000,
      defaultTone: 'professional',
      includeResearchByDefault: true,
      autoSaveEnabled: true,
      emailNotifications: true,
      pushNotifications: false,
      weeklyReports: true,
      marketingEmails: false,
      theme: 'dark',
      accentColor: 'blue',
      animationsEnabled: true,
      compactMode: false,
      profileVisibility: 'private',
      dataSharing: false,
      analyticsTracking: true,
    }

    return NextResponse.json({
      success: true,
      settings: defaultSettings
    })

  } catch (error) {
    console.error('Error fetching settings:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch settings' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const settings = await request.json()

    // Validate email format only if email is provided
    if (settings.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(settings.email)) {
        return NextResponse.json(
          { success: false, error: 'Invalid email format' },
          { status: 400 }
        )
      }
    }

    // Validate word count only if provided
    if (settings.defaultWordCount && (settings.defaultWordCount < 100 || settings.defaultWordCount > 10000)) {
      return NextResponse.json(
        { success: false, error: 'Word count must be between 100 and 10,000' },
        { status: 400 }
      )
    }

    // Validate tone only if provided
    if (settings.defaultTone) {
      const validTones = ['professional', 'casual', 'authoritative', 'conversational', 'technical', 'friendly']
      if (!validTones.includes(settings.defaultTone)) {
        return NextResponse.json(
          { success: false, error: 'Invalid tone selection' },
          { status: 400 }
        )
      }
    }

    // Validate theme only if provided
    if (settings.theme) {
      const validThemes = ['dark', 'light', 'auto']
      if (!validThemes.includes(settings.theme)) {
        return NextResponse.json(
          { success: false, error: 'Invalid theme selection' },
          { status: 400 }
        )
      }
    }

    // Validate accent color only if provided
    if (settings.accentColor) {
      const validColors = ['blue', 'purple', 'green', 'red', 'orange', 'pink', 'cyan', 'yellow', 'indigo', 'teal', 'emerald', 'rose']
      if (!validColors.includes(settings.accentColor)) {
        return NextResponse.json(
          { success: false, error: 'Invalid accent color selection' },
          { status: 400 }
        )
      }
    }

    // Validate profile visibility only if provided
    if (settings.profileVisibility) {
      const validVisibility = ['private', 'team', 'public']
      if (!validVisibility.includes(settings.profileVisibility)) {
        return NextResponse.json(
          { success: false, error: 'Invalid profile visibility setting' },
          { status: 400 }
        )
      }
    }

    // In a real app, you would:
    // 1. Authenticate the user
    // 2. Save settings to database
    // 3. Update user session/cache
    // 4. Send confirmation email if email changed
    // 5. Log the settings change for audit

    // Simulate database save delay
    await new Promise(resolve => setTimeout(resolve, 500))

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Settings saved successfully',
      settings: settings
    })

  } catch (error) {
    console.error('Error saving settings:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to save settings' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { action } = await request.json()

    if (action === 'reset') {
      // Reset to default settings
      const defaultSettings = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        bio: 'Content creator and digital marketer passionate about AI-powered writing.',
        avatar: '',
        defaultLanguage: 'en',
        timezone: 'America/New_York',
        defaultWordCount: 1000,
        defaultTone: 'professional',
        includeResearchByDefault: true,
        autoSaveEnabled: true,
        emailNotifications: true,
        pushNotifications: false,
        weeklyReports: true,
        marketingEmails: false,
        theme: 'dark',
        accentColor: 'blue',
        animationsEnabled: true,
        compactMode: false,
        profileVisibility: 'private',
        dataSharing: false,
        analyticsTracking: true,
      }

      // In a real app, save these defaults to the database
      await new Promise(resolve => setTimeout(resolve, 300))

      return NextResponse.json({
        success: true,
        message: 'Settings reset to defaults',
        settings: defaultSettings
      })
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Error updating settings:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update settings' },
      { status: 500 }
    )
  }
}

// Settings schema for validation (internal use only)
const settingsSchema = {
  firstName: { type: 'string', required: true, minLength: 1, maxLength: 50 },
  lastName: { type: 'string', required: true, minLength: 1, maxLength: 50 },
  email: { type: 'string', required: true, format: 'email' },
  bio: { type: 'string', required: false, maxLength: 500 },
  avatar: { type: 'string', required: false },
  defaultLanguage: { type: 'string', required: true, enum: ['en', 'es', 'fr', 'de', 'it', 'pt', 'ja', 'ko'] },
  timezone: { type: 'string', required: true },
  defaultWordCount: { type: 'number', required: true, min: 100, max: 10000 },
  defaultTone: { type: 'string', required: true, enum: ['professional', 'casual', 'authoritative', 'conversational', 'technical', 'friendly'] },
  includeResearchByDefault: { type: 'boolean', required: true },
  autoSaveEnabled: { type: 'boolean', required: true },
  emailNotifications: { type: 'boolean', required: true },
  pushNotifications: { type: 'boolean', required: true },
  weeklyReports: { type: 'boolean', required: true },
  marketingEmails: { type: 'boolean', required: true },
  theme: { type: 'string', required: true, enum: ['dark', 'light', 'auto'] },
  accentColor: { type: 'string', required: true, enum: ['blue', 'purple', 'green', 'red', 'orange', 'pink', 'cyan', 'yellow', 'indigo', 'teal', 'emerald', 'rose'] },
  animationsEnabled: { type: 'boolean', required: true },
  compactMode: { type: 'boolean', required: true },
  profileVisibility: { type: 'string', required: true, enum: ['private', 'team', 'public'] },
  dataSharing: { type: 'boolean', required: true },
  analyticsTracking: { type: 'boolean', required: true }
}
