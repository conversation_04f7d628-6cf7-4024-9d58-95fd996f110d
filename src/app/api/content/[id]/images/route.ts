import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { imageStorageService } from '@/lib/image-storage'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Await params as required by Next.js 15
    const { id } = await params

    if (!id) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      )
    }

    console.log(`🖼️ Fetching images for content: ${id}`)

    // Get images for the content
    const images = await imageStorageService.getImagesForContent(id)

    console.log(`✅ Found ${images.length} images for content: ${id}`)

    return NextResponse.json({
      success: true,
      images,
      count: images.length
    })

  } catch (error) {
    console.error('Failed to fetch content images:', error)
    return NextResponse.json(
      { error: 'Failed to fetch images' },
      { status: 500 }
    )
  }
}
