import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { imageStorageService } from '@/lib/image-storage'

export async function POST(request: NextRequest) {
  try {
    // Check authentication (admin only for cleanup)
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    console.log('🧹 Starting image cleanup...')

    // Cleanup old images (older than 30 days with no contentId)
    const deletedCount = await imageStorageService.cleanupOldImages()

    console.log(`✅ Cleanup completed: ${deletedCount} images deleted`)

    return NextResponse.json({
      success: true,
      deletedCount,
      message: `Successfully cleaned up ${deletedCount} old images`
    })

  } catch (error) {
    console.error('Image cleanup failed:', error)
    return NextResponse.json(
      { error: 'Failed to cleanup images' },
      { status: 500 }
    )
  }
}
