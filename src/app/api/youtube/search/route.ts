import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { Innertube } from 'youtubei.js'

interface YouTubeSearchResult {
  videoId: string
  title: string
  description: string
  channelTitle: string
  publishedAt: string
  thumbnails: {
    default: { url: string }
    medium: { url: string }
    high: { url: string }
  }
  viewCount?: number
  likeCount?: number
  commentCount?: number
  duration?: string
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { query, maxResults = 5 } = await request.json()

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      )
    }

    console.log('🔍 YouTube Search with InnerTube:', query)

    try {
      // Initialize InnerTube client
      console.log('🚀 Initializing InnerTube client...')
      const youtube = await Innertube.create()
      
      // Search for videos
      console.log('🔍 Searching for videos...')
      const searchResults = await youtube.search(query.trim(), {
        type: 'video'
      })

      if (!searchResults.videos || searchResults.videos.length === 0) {
        return NextResponse.json(
          { error: 'No videos found for the given query' },
          { status: 404 }
        )
      }

      console.log(`✅ Found ${searchResults.videos.length} videos`)

      // Parse duration from seconds to readable format
      const formatDuration = (seconds: number): string => {
        if (!seconds || seconds === 0) return 'Unknown'
        
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        const secs = seconds % 60
        
        if (hours > 0) {
          return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
        } else {
          return `${minutes}:${secs.toString().padStart(2, '0')}`
        }
      }

      // Helper function to safely extract text
      const safeText = (obj: any): string => {
        if (!obj) return ''
        if (typeof obj === 'string') return obj
        if (obj.text) return obj.text
        if (obj.simpleText) return obj.simpleText
        return ''
      }

      // Helper function to safely extract numbers from text
      const safeNumber = (text: string): number | undefined => {
        if (!text) return undefined
        const cleaned = text.replace(/[^0-9]/g, '')
        const num = parseInt(cleaned)
        return isNaN(num) ? undefined : num
      }

      // Convert InnerTube results to our format
      const results: YouTubeSearchResult[] = searchResults.videos
        .slice(0, maxResults)
        .map((video: any) => {
          const thumbnails = video.thumbnails && video.thumbnails.length > 0 ? video.thumbnails[0] : {}
          
          return {
            videoId: video.id || '',
            title: safeText(video.title) || 'Untitled',
            description: safeText(video.description) || '',
            channelTitle: safeText(video.author?.name) || 'Unknown Channel',
            publishedAt: safeText(video.published) || '',
            thumbnails: {
              default: { url: thumbnails.url || '' },
              medium: { url: thumbnails.url || '' },
              high: { url: thumbnails.url || '' }
            },
            viewCount: video.view_count ? safeNumber(safeText(video.view_count)) : undefined,
            likeCount: undefined, // Not available in search results
            commentCount: undefined, // Not available in search results
            duration: video.duration?.seconds_total ? formatDuration(video.duration.seconds_total) : 'Unknown'
          }
        })
        .filter(video => video.videoId) // Remove any videos without IDs

      // Sort by view count if available
      const sortedResults = results.sort((a, b) => {
        if (a.viewCount && b.viewCount) {
          return b.viewCount - a.viewCount
        }
        return 0
      })

      console.log('🎬 YouTube search completed successfully')
      console.log(`📊 Top video: "${sortedResults[0]?.title}" (${sortedResults[0]?.viewCount?.toLocaleString() || 'unknown'} views)`)

      return NextResponse.json({
        success: true,
        query,
        totalResults: searchResults.estimated_results || results.length,
        videos: sortedResults,
        searchedAt: new Date().toISOString()
      })

    } catch (fetchError) {
      console.error('❌ YouTube InnerTube request failed:', fetchError)
      
      if (fetchError instanceof Error) {
        return NextResponse.json(
          { error: `YouTube search failed: ${fetchError.message}` },
          { status: 500 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to search YouTube videos' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('YouTube search error:', error)
    return NextResponse.json(
      { error: 'Failed to search YouTube videos' },
      { status: 500 }
    )
  }
}