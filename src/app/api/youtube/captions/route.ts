import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { Innertube } from 'youtubei.js'

interface CaptionSegment {
  text: string
  start: number
  duration: number
}

interface VideoCaption {
  videoId: string
  title: string
  captions: CaptionSegment[]
  language: string
  duration: number
  extractedAt: string
  wordCount: number
  error?: string
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { videoIds, titles } = await request.json()

    if (!videoIds || !Array.isArray(videoIds) || videoIds.length === 0) {
      return NextResponse.json(
        { error: 'Video IDs array is required' },
        { status: 400 }
      )
    }

    console.log(`🎬 Extracting captions for ${videoIds.length} videos using InnerTube`)

    // Initialize InnerTube client
    const youtube = await Innertube.create()
    const results: VideoCaption[] = []

    // Process each video sequentially to avoid overwhelming the API
    for (let i = 0; i < videoIds.length; i++) {
      const videoId = videoIds[i]
      const title = titles?.[i] || `Video ${i + 1}`
      
      console.log(`📝 Extracting captions for: "${title}" (${videoId})`)

      try {
        // Get video info
        const videoInfo = await youtube.getInfo(videoId)
        
        if (!videoInfo) {
          results.push({
            videoId,
            title,
            captions: [],
            language: 'unknown',
            duration: 0,
            extractedAt: new Date().toISOString(),
            wordCount: 0,
            error: 'Video not found'
          })
          continue
        }

        // Get captions/transcript
        const transcriptData = await videoInfo.getTranscript()
        
        if (!transcriptData || !transcriptData.content) {
          results.push({
            videoId,
            title,
            captions: [],
            language: 'unknown',
            duration: 0,
            extractedAt: new Date().toISOString(),
            wordCount: 0,
            error: 'No captions available'
          })
          continue
        }

        // Convert transcript to our format
        const segments: CaptionSegment[] = []
        let totalWords = 0

        // InnerTube transcript format varies, let's handle different formats
        if (transcriptData.content.body) {
          const transcriptSegments = transcriptData.content.body.initial_segments || []
          
          for (const segment of transcriptSegments) {
            if (segment.snippet && segment.snippet.text) {
              const text = segment.snippet.text.simpleText || segment.snippet.text.runs?.map((r: any) => r.text).join('') || ''
              const startTimeMs = segment.start_offset_msec || 0
              const durationMs = segment.end_offset_msec ? (segment.end_offset_msec - startTimeMs) : 3000
              
              if (text.trim()) {
                segments.push({
                  text: text.trim(),
                  start: startTimeMs / 1000, // Convert to seconds
                  duration: durationMs / 1000 // Convert to seconds
                })
                totalWords += text.split(/\s+/).length
              }
            }
          }
        } else if (Array.isArray(transcriptData.content)) {
          // Alternative format
          for (const item of transcriptData.content) {
            if (item.text) {
              const text = typeof item.text === 'string' ? item.text : item.text.simpleText || ''
              if (text.trim()) {
                segments.push({
                  text: text.trim(),
                  start: item.start_offset_msec ? item.start_offset_msec / 1000 : 0,
                  duration: item.duration_msec ? item.duration_msec / 1000 : 3
                })
                totalWords += text.split(/\s+/).length
              }
            }
          }
        } else {
          // Fallback: try to extract from raw content
          const rawContent = JSON.stringify(transcriptData.content)
          const textMatches = rawContent.match(/"simpleText":"([^"]+)"/g) || []
          
          let currentTime = 0
          for (const match of textMatches) {
            const text = match.replace(/"simpleText":"([^"]+)"/, '$1').replace(/\\u[\d\w]{4}/g, '')
            if (text.trim() && text.length > 3) {
              segments.push({
                text: text.trim(),
                start: currentTime,
                duration: 3 // Default 3 seconds
              })
              totalWords += text.split(/\s+/).length
              currentTime += 3
            }
          }
        }

        // If no segments found, try alternative extraction
        if (segments.length === 0) {
          console.warn(`⚠️ No transcript segments found for ${videoId}, trying alternative extraction`)
          
          // Try to get raw transcript text
          try {
            const rawTranscript = JSON.stringify(transcriptData)
            const sentences = rawTranscript
              .replace(/[{}[\]"]/g, ' ')
              .split(/[.!?]+/)
              .map(s => s.trim())
              .filter(s => s.length > 10 && !/^[0-9\s:]+$/.test(s))
              .slice(0, 50) // Limit to first 50 sentences
            
            let currentTime = 0
            for (const sentence of sentences) {
              if (sentence.length > 10) {
                segments.push({
                  text: sentence,
                  start: currentTime,
                  duration: Math.min(sentence.length * 0.1, 5) // Estimate duration
                })
                totalWords += sentence.split(/\s+/).length
                currentTime += Math.min(sentence.length * 0.1, 5)
              }
            }
          } catch (extractionError) {
            console.error(`Failed alternative extraction for ${videoId}:`, extractionError)
          }
        }

        const videoDuration = videoInfo.basic_info?.duration?.seconds_total || 0
        const language = transcriptData.metadata?.language || 'en'

        results.push({
          videoId,
          title,
          captions: segments,
          language,
          duration: videoDuration,
          extractedAt: new Date().toISOString(),
          wordCount: totalWords
        })

        console.log(`✅ Extracted ${segments.length} segments for "${title}" (${totalWords} words)`)

        // Add a small delay between requests to be respectful
        if (i < videoIds.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }

      } catch (error) {
        console.error(`❌ Failed to extract captions for ${videoId}:`, error)
        
        let errorMessage = 'Unknown error'
        if (error instanceof Error) {
          errorMessage = error.message
        }

        results.push({
          videoId,
          title,
          captions: [],
          language: 'unknown',
          duration: 0,
          extractedAt: new Date().toISOString(),
          wordCount: 0,
          error: errorMessage
        })
      }
    }

    // Calculate success metrics
    const successfulExtractions = results.filter(r => !r.error && r.captions.length > 0)
    const totalWords = successfulExtractions.reduce((sum, r) => sum + r.wordCount, 0)

    console.log('🎉 Caption extraction completed')
    console.log(`📊 Successfully extracted: ${successfulExtractions.length}/${results.length} videos`)
    console.log(`📝 Total words extracted: ${totalWords.toLocaleString()}`)

    return NextResponse.json({
      success: true,
      totalVideos: videoIds.length,
      successfulExtractions: successfulExtractions.length,
      totalWords,
      extractedAt: new Date().toISOString(),
      captions: results
    })

  } catch (error) {
    console.error('Caption extraction error:', error)
    return NextResponse.json(
      { error: 'Failed to extract video captions' },
      { status: 500 }
    )
  }
}