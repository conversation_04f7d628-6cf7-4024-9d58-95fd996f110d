'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { 
  ArrowLeft, 
  Sparkles, 
  Video,
  Wand2,
  Globe,
  Type,
  FileText,
  Plus,
  X,
  Loader2,
  Languages,
  ChevronRight,
  Link2,
  Target,
  MessageSquare,
  Timer,
  Zap,
  Crown,
  Play,
  Youtube,
  BookOpen,
  PenTool,
  Hash
} from 'lucide-react';
import { cn } from '@/lib/utils';
import VideoAlchemyStory from '@/components/VideoAlchemyStory';

interface VideoAlchemyConfig {
  topic: string;
  videoLinks: string[];
  tone: string;
  wordCount: number;
  customInstructions: string;
  language: 'english' | 'hindi' | 'french';
}

export default function VideoAlchemyPage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [config, setConfig] = useState<VideoAlchemyConfig>({
    topic: '',
    videoLinks: [],
    tone: 'conversational',
    wordCount: 1500,
    customInstructions: '',
    language: 'english'
  });
  const [videoLinkInput, setVideoLinkInput] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [storyData, setStoryData] = useState({
    stage: 'initialization',
    title: 'Ready to Begin',
    description: 'Click Transform to Article to start the magical process...',
    animation: 'laboratory-setup',
    duration: 3000
  });
  const [currentStage, setCurrentStage] = useState('initialization');

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      window.location.href = '/login';
    }
  }, [status]);

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null;
  }

  // Extract video ID from various YouTube URL formats
  const extractVideoId = (url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  };

  const addVideoLink = () => {
    if (videoLinkInput.trim() && config.videoLinks.length < 5) {
      const videoId = extractVideoId(videoLinkInput.trim());
      if (videoId) {
        const standardUrl = `https://www.youtube.com/watch?v=${videoId}`;
        if (!config.videoLinks.includes(standardUrl)) {
          setConfig({
            ...config,
            videoLinks: [...config.videoLinks, standardUrl]
          });
          setVideoLinkInput('');
        } else {
          setError('This video is already added');
          setTimeout(() => setError(''), 3000);
        }
      } else {
        setError('Please enter a valid YouTube URL');
        setTimeout(() => setError(''), 3000);
      }
    }
  };

  const removeVideoLink = (link: string) => {
    setConfig({
      ...config,
      videoLinks: config.videoLinks.filter(l => l !== link)
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!config.topic.trim()) {
      setError('Please enter a topic');
      return;
    }

    if (config.videoLinks.length === 0) {
      setError('Please add at least one video link');
      return;
    }

    setIsGenerating(true);
    setError('');
    setProgress(0);
    setProgressMessage('Initializing Video Alchemy...');

    try {
      // Use SSE for real-time updates
      const response = await fetch('/api/video-alchemy/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error('Failed to start generation');
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No response stream available');
      }

      let buffer = '';
      
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.stage === 'error') {
                throw new Error(data.error || 'Generation failed');
              }
              
              if (data.stage === 'completion') {
                setProgress(100);
                setProgressMessage('Complete! Redirecting to your article...');
                
                // Store the result and navigate to article view
                sessionStorage.setItem('videoAlchemyResult', JSON.stringify(data));
                
                setTimeout(() => {
                  router.push('/article-view');
                }, 2000);
                return;
              }
              
              // Update progress and message
              setProgress(data.progress || 0);
              setProgressMessage(data.message || 'Processing...');
              
              // Update story data if provided
              if (data.storyData) {
                setStoryData(data.storyData);
                setCurrentStage(data.stage);
              }
              
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', parseError);
            }
          }
        }
      }
      
    } catch (error) {
      console.error('Error:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setIsGenerating(false);
    }
  };

  const tones = [
    { value: 'professional', label: 'Professional', icon: Target, description: 'Formal and authoritative' },
    { value: 'conversational', label: 'Conversational', icon: MessageSquare, description: 'Friendly and engaging' },
    { value: 'casual', label: 'Casual', icon: Hash, description: 'Relaxed and informal' },
    { value: 'educational', label: 'Educational', icon: BookOpen, description: 'Informative and clear' },
    { value: 'storytelling', label: 'Storytelling', icon: PenTool, description: 'Narrative and compelling' },
    { value: 'technical', label: 'Technical', icon: Zap, description: 'Detailed and precise' }
  ];

  const languages = [
    { value: 'english', label: 'English', flag: '🇬🇧' },
    { value: 'hindi', label: 'हिंदी', flag: '🇮🇳' },
    { value: 'french', label: 'Français', flag: '🇫🇷' }
  ];

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-950/20 via-black to-pink-950/20" />
        
        {/* Animated orbs */}
        <motion.div
          animate={{
            x: [0, 150, 0],
            y: [0, -50, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/3 left-1/5 w-[600px] h-[600px] bg-purple-700/10 rounded-full blur-[120px]"
        />
        <motion.div
          animate={{
            x: [0, -100, 0],
            y: [0, 100, 0],
            scale: [1, 0.8, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute bottom-1/3 right-1/4 w-[500px] h-[500px] bg-pink-700/10 rounded-full blur-[100px]"
        />
      </div>

      {/* Header */}
      <motion.header 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40"
      >
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Link 
                href="/dashboard" 
                className="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group"
              >
                <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
                <span className="font-medium">Back to Dashboard</span>
              </Link>
              
              <div className="h-6 w-px bg-white/20" />
              
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl blur-lg opacity-70" />
                  <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                    <Wand2 className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">Video Alchemy</h1>
                  <p className="text-sm text-gray-400">Transform Videos into Articles</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-gray-400">
                <div className="flex items-center space-x-2">
                  <Youtube className="w-4 h-4 text-red-400" />
                  <span>YouTube Supported</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Languages className="w-4 h-4 text-blue-400" />
                  <span>Multi-language</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <main className="relative z-10 max-w-6xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-gradient-to-r from-purple-900/40 to-pink-900/40 backdrop-blur-md border border-white/20 mb-8">
            <Video className="w-4 h-4 text-purple-400" />
            <span className="text-sm text-gray-200">AI-Powered Video to Article Conversion</span>
            <FileText className="w-4 h-4 text-pink-400" />
          </div>
          
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Transform Videos into
            <span className="block bg-gradient-to-r from-purple-400 via-pink-400 to-red-400 bg-clip-text text-transparent">
              SEO-Optimized Articles
            </span>
          </h2>
          
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8 leading-relaxed">
            Extract captions from YouTube videos and transform them into comprehensive, 
            human-like articles optimized for search engines and reader engagement.
          </p>
        </motion.div>

        {/* Progress Steps */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-12"
        >
          <div className="flex items-center justify-center">
            {[
              { step: 1, label: 'Setup', icon: Video },
              { step: 2, label: 'Configure', icon: Type },
              { step: 3, label: 'Customize', icon: Wand2 }
            ].map((item, index) => (
              <div key={item.step} className="flex items-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2 + index * 0.1 }}
                  className={cn(
                    "relative flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all",
                    currentStep >= item.step
                      ? "bg-gradient-to-r from-purple-600 to-pink-600 border-transparent"
                      : "bg-black/60 border-white/20"
                  )}
                >
                  <item.icon className={cn(
                    "w-5 h-5",
                    currentStep >= item.step ? "text-white" : "text-gray-400"
                  )} />
                  {currentStep > item.step && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute inset-0 flex items-center justify-center"
                    >
                      <Play className="w-5 h-5 text-white" />
                    </motion.div>
                  )}
                </motion.div>
                <div className={cn(
                  "ml-3 mr-8 transition-colors",
                  currentStep >= item.step ? "text-white" : "text-gray-500"
                )}>
                  <p className="text-sm font-medium">{item.label}</p>
                </div>
                {index < 2 && (
                  <div className={cn(
                    "w-20 h-0.5 mr-8 transition-colors",
                    currentStep > item.step
                      ? "bg-gradient-to-r from-purple-600 to-pink-600"
                      : "bg-white/10"
                  )} />
                )}
              </div>
            ))}
          </div>
        </motion.div>

        {/* Main Form */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Step 1: Video Setup */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8"
          >
            <div className="flex items-center space-x-3 mb-8">
              <div className="p-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg">
                <Video className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white">Video Setup</h3>
            </div>

            <div className="space-y-6">
              {/* Topic */}
              <div className="space-y-4">
                <label className="block text-sm font-medium text-white/90">
                  Article Topic <span className="text-purple-400">*</span>
                </label>
                <input
                  type="text"
                  value={config.topic}
                  onChange={(e) => {
                    setConfig({ ...config, topic: e.target.value });
                    if (e.target.value) setCurrentStep(Math.max(currentStep, 2));
                  }}
                  className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-purple-500/50 transition-all"
                  placeholder="e.g., How to Master Public Speaking"
                  disabled={isGenerating}
                />
              </div>

              {/* Video Links */}
              <div className="space-y-4">
                <label className="block text-sm font-medium text-white/90">
                  YouTube Video Links <span className="text-purple-400">*</span>
                  <span className="text-xs text-gray-400 ml-2">(Up to 5 videos)</span>
                </label>
                
                <div className="flex gap-3 mb-4">
                  <input
                    type="text"
                    value={videoLinkInput}
                    onChange={(e) => setVideoLinkInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addVideoLink())}
                    className="flex-1 px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-purple-500/50 transition-all"
                    placeholder="https://www.youtube.com/watch?v=..."
                    disabled={isGenerating || config.videoLinks.length >= 5}
                  />
                  <motion.button
                    type="button"
                    onClick={addVideoLink}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={cn(
                      "px-6 py-3 rounded-xl font-medium transition-all flex items-center space-x-2",
                      config.videoLinks.length >= 5
                        ? "bg-gray-800 text-gray-400 cursor-not-allowed"
                        : "bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:shadow-lg"
                    )}
                    disabled={isGenerating || config.videoLinks.length >= 5}
                  >
                    <Plus className="w-5 h-5" />
                    <span>Add</span>
                  </motion.button>
                </div>

                {/* Video Links List */}
                <div className="space-y-3">
                  <AnimatePresence>
                    {config.videoLinks.map((link, index) => {
                      const videoId = extractVideoId(link);
                      return (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className="flex items-center space-x-3 p-4 bg-white/5 rounded-xl border border-white/10"
                        >
                          <Youtube className="w-5 h-5 text-red-400" />
                          <div className="flex-1 flex items-center space-x-3">
                            <img
                              src={`https://img.youtube.com/vi/${videoId}/default.jpg`}
                              alt="Video thumbnail"
                              className="w-20 h-14 rounded-lg object-cover"
                            />
                            <div className="flex-1">
                              <p className="text-sm text-white truncate">{link}</p>
                              <p className="text-xs text-gray-400">Video {index + 1}</p>
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeVideoLink(link)}
                            className="text-gray-400 hover:text-white transition-colors"
                            disabled={isGenerating}
                          >
                            <X className="w-5 h-5" />
                          </button>
                        </motion.div>
                      );
                    })}
                  </AnimatePresence>

                  {config.videoLinks.length === 0 && (
                    <div className="text-center py-8 text-gray-400">
                      <Youtube className="w-12 h-12 mx-auto mb-3 opacity-50" />
                      <p>No videos added yet</p>
                      <p className="text-sm mt-1">Add YouTube videos to extract their content</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Step 2: Configure */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8"
          >
            <div className="flex items-center space-x-3 mb-8">
              <div className="p-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg">
                <Type className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white">Configure Output</h3>
            </div>

            <div className="space-y-6">
              {/* Tone Selection */}
              <div className="space-y-4">
                <label className="block text-sm font-medium text-white/90">
                  Writing Tone
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {tones.map((tone) => {
                    const Icon = tone.icon;
                    return (
                      <button
                        key={tone.value}
                        type="button"
                        onClick={() => {
                          setConfig({ ...config, tone: tone.value });
                          setCurrentStep(Math.max(currentStep, 3));
                        }}
                        className={cn(
                          "p-4 rounded-xl border transition-all text-left group",
                          config.tone === tone.value
                            ? "bg-gradient-to-r from-purple-600/20 to-pink-600/20 border-purple-500/50 text-white"
                            : "bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20"
                        )}
                        disabled={isGenerating}
                      >
                        <div className="flex items-center space-x-3 mb-2">
                          <Icon className={cn(
                            "w-5 h-5 transition-colors",
                            config.tone === tone.value ? "text-purple-400" : "text-gray-400 group-hover:text-gray-300"
                          )} />
                          <span className="font-medium">{tone.label}</span>
                        </div>
                        <p className="text-xs opacity-70">{tone.description}</p>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Word Count */}
              <div className="space-y-4">
                <label className="block text-sm font-medium text-white/90">
                  Word Count
                  <span className="text-xs text-gray-400 ml-2">(500 - 10,000 words)</span>
                </label>
                <div className="relative">
                  <input
                    type="range"
                    min="500"
                    max="10000"
                    step="100"
                    value={config.wordCount}
                    onChange={(e) => setConfig({ ...config, wordCount: parseInt(e.target.value) })}
                    className="w-full h-2 bg-white/10 rounded-lg appearance-none cursor-pointer slider"
                    disabled={isGenerating}
                  />
                  <div className="flex justify-between mt-2 text-sm text-gray-400">
                    <span>500</span>
                    <span className="text-white font-medium">{config.wordCount.toLocaleString()} words</span>
                    <span>10,000</span>
                  </div>
                </div>
                <div className="text-xs text-gray-400 mt-2">
                  {config.wordCount <= 1000 && "Quick read - Perfect for social media and brief explanations"}
                  {config.wordCount > 1000 && config.wordCount <= 2000 && "Standard article - Good for blog posts and tutorials"}
                  {config.wordCount > 2000 && config.wordCount <= 5000 && "In-depth article - Comprehensive coverage of topics"}
                  {config.wordCount > 5000 && "Long-form content - Detailed guides and extensive research"}
                </div>
              </div>

              {/* Language Selection */}
              <div className="space-y-4">
                <label className="block text-sm font-medium text-white/90">
                  Output Language
                </label>
                <div className="grid grid-cols-3 gap-3">
                  {languages.map((lang) => (
                    <button
                      key={lang.value}
                      type="button"
                      onClick={() => setConfig({ ...config, language: lang.value as any })}
                      className={cn(
                        "p-4 rounded-xl border transition-all",
                        config.language === lang.value
                          ? "bg-gradient-to-r from-purple-600/20 to-pink-600/20 border-purple-500/50 text-white"
                          : "bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20"
                      )}
                      disabled={isGenerating}
                    >
                      <div className="text-2xl mb-2">{lang.flag}</div>
                      <p className="font-medium">{lang.label}</p>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>

          {/* Step 3: Customize */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8"
          >
            <div className="flex items-center space-x-3 mb-8">
              <div className="p-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg">
                <Wand2 className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white">Customize</h3>
            </div>

            <div className="space-y-4">
              <label className="block text-sm font-medium text-white/90">
                Custom Instructions
                <span className="text-xs text-gray-400 ml-2">(Optional)</span>
              </label>
              <textarea
                value={config.customInstructions}
                onChange={(e) => setConfig({ ...config, customInstructions: e.target.value })}
                className="w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-purple-500/50 transition-all resize-none"
                rows={4}
                placeholder="e.g., Focus on actionable tips, include statistics, use examples from the videos..."
                disabled={isGenerating}
              />
              <p className="text-xs text-gray-400">
                Provide specific instructions for how the article should be written. 
                If left empty, our AI will use optimized defaults.
              </p>
            </div>
          </motion.div>

          {/* Error Display */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 text-red-300"
              >
                <p className="flex items-center space-x-2">
                  <X className="w-5 h-5" />
                  <span>{error}</span>
                </p>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Submit Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex justify-center"
          >
            <motion.button
              type="submit"
              disabled={isGenerating || !config.topic || config.videoLinks.length === 0}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                "px-12 py-6 rounded-2xl font-bold text-lg shadow-2xl transition-all flex items-center space-x-3 group",
                isGenerating || !config.topic || config.videoLinks.length === 0
                  ? "bg-gray-800 text-gray-400 cursor-not-allowed"
                  : "bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:shadow-purple-500/25"
              )}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-6 h-6 animate-spin" />
                  <span>Transforming Videos...</span>
                </>
              ) : (
                <>
                  <Wand2 className="w-6 h-6 group-hover:rotate-12 transition-transform" />
                  <span>Transform to Article</span>
                  <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </>
              )}
            </motion.button>
          </motion.div>

          {/* Animated Story Progress */}
          <AnimatePresence>
            {isGenerating && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="mt-8"
              >
                <VideoAlchemyStory
                  storyData={storyData}
                  progress={progress}
                  message={progressMessage}
                  isActive={isGenerating}
                />
                
                {/* Progress Stats */}
                <div className="mt-6 p-4 bg-gradient-to-r from-purple-900/20 to-pink-900/20 rounded-xl border border-white/10 backdrop-blur-md">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <Wand2 className="w-4 h-4 text-white" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-white">{storyData.title}</h4>
                        <p className="text-xs text-gray-400">{storyData.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-purple-400">{progress}%</div>
                      <div className="text-xs text-gray-400">Complete</div>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </form>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          {[
            {
              icon: Youtube,
              title: 'Caption Extraction',
              description: 'Automatically extract captions from YouTube videos with high accuracy',
              color: 'from-red-600 to-pink-600'
            },
            {
              icon: Globe,
              title: 'SEO Optimized',
              description: 'Articles are optimized for search engines with AEO and GEO considerations',
              color: 'from-blue-600 to-purple-600'
            },
            {
              icon: Languages,
              title: 'Multi-language',
              description: 'Generate articles in English, Hindi, or French based on your preference',
              color: 'from-green-600 to-teal-600'
            }
          ].map((feature, index) => {
            const Icon = feature.icon;
            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
                className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-6 group hover:bg-white/10 transition-all"
              >
                <div className={cn(
                  "w-12 h-12 rounded-lg bg-gradient-to-r flex items-center justify-center mb-4 group-hover:scale-110 transition-transform",
                  feature.color
                )}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">{feature.title}</h4>
                <p className="text-sm text-gray-400">{feature.description}</p>
              </motion.div>
            );
          })}
        </motion.div>
      </main>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 20px;
          height: 20px;
          background: linear-gradient(to right, #9333ea, #ec4899);
          border-radius: 50%;
          cursor: pointer;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .slider::-moz-range-thumb {
          width: 20px;
          height: 20px;
          background: linear-gradient(to right, #9333ea, #ec4899);
          border-radius: 50%;
          cursor: pointer;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border: none;
        }
      `}</style>
    </div>
  );
} 