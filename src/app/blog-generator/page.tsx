'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { FileText, Search, Zap, ArrowLeft, Sparkles, TrendingUp, Target, CheckCircle, AlertCircle, Clock } from 'lucide-react'
import Link from 'next/link'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'

export default function BlogGeneratorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [formData, setFormData] = useState({
    topic: '',
    wordCount: 2000,
    tone: 'professional'
  })

  const [isGenerating, setIsGenerating] = useState(false)
  const [currentStep, setCurrentStep] = useState('')
  const [progress, setProgress] = useState(0)
  const [generatedContent, setGeneratedContent] = useState('')
  const [error, setError] = useState('')
  const [savedToDatabase, setSavedToDatabase] = useState(false)
  const [databaseId, setDatabaseId] = useState<string | undefined>()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.topic.trim()) return

    setIsGenerating(true)
    setError('')
    setProgress(0)
    setCurrentStep('Starting search...')

    try {
      const response = await fetch('/api/blog-generator/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Generation failed')
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (reader) {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6))

                if (data.step) {
                  setCurrentStep(data.step)
                }
                if (data.progress) {
                  setProgress(data.progress)
                }
                if (data.content) {
                  setGeneratedContent(data.content)
                  setSavedToDatabase(data.savedToDatabase || false)
                  setDatabaseId(data.databaseId)

                  // Redirect to display page using short URL
                  if (data.redirectUrl) {
                    router.push(data.redirectUrl)
                  } else {
                    // Fallback to old method for backward compatibility
                    const encodedContent = encodeURIComponent(data.content.substring(0, 1000)) // Limit length
                    const encodedTitle = encodeURIComponent(formData.topic.substring(0, 100))
                    router.push(`/blog-display?content=${encodedContent}&title=${encodedTitle}`)
                  }
                }
                if (data.error) {
                  throw new Error(data.error)
                }
              } catch (parseError) {
                console.error('Error parsing SSE data:', parseError)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Generation error:', error)
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setIsGenerating(false)
    }
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center space-x-4">
            <Link href="/dashboard">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </motion.button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-white flex items-center">
                <FileText className="w-8 h-8 mr-3 text-pink-400" />
                Blog Generator
              </h1>
              <p className="text-gray-400 mt-1">
                Enter a topic → Search & analyze → Generate content
              </p>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
              <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                <Target className="w-5 h-5 mr-2 text-pink-400" />
                Blog Configuration
              </h2>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Topic Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Topic or Title *
                  </label>
                  <textarea
                    name="topic"
                    value={formData.topic}
                    onChange={handleInputChange}
                    placeholder="Enter your blog topic or exact title..."
                    className="w-full px-4 py-3 bg-black/40 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 transition-all resize-none"
                    rows={3}
                    required
                  />
                </div>

                {/* Word Count */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Word Count
                  </label>
                  <select
                    name="wordCount"
                    value={formData.wordCount}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-black/40 border border-white/20 rounded-xl text-white focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 transition-all"
                  >
                    <option value={1000}>1,000 words</option>
                    <option value={1500}>1,500 words</option>
                    <option value={2000}>2,000 words</option>
                    <option value={2500}>2,500 words</option>
                    <option value={3000}>3,000 words</option>
                  </select>
                </div>

                {/* Tone */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Tone
                  </label>
                  <select
                    name="tone"
                    value={formData.tone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 bg-black/40 border border-white/20 rounded-xl text-white focus:border-pink-400 focus:ring-2 focus:ring-pink-400/20 transition-all"
                  >
                    <option value="professional">Professional</option>
                    <option value="casual">Casual</option>
                    <option value="friendly">Friendly</option>
                    <option value="authoritative">Authoritative</option>
                    <option value="conversational">Conversational</option>
                  </select>
                </div>

                {/* Generate Button */}
                <motion.button
                  type="submit"
                  disabled={isGenerating || !formData.topic}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full py-4 bg-gradient-to-r from-pink-600 to-rose-600 text-white font-semibold rounded-xl hover:from-pink-700 hover:to-rose-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      <span>Generating...</span>
                    </>
                  ) : (
                    <>
                      <Zap className="w-5 h-5" />
                      <span>Generate Blog Post</span>
                    </>
                  )}
                </motion.button>
              </form>
            </div>
          </motion.div>

          {/* Progress Panel */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
              <h2 className="text-xl font-semibold text-white mb-6 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-pink-400" />
                Generation Progress
              </h2>

              {!isGenerating && !error && (
                <div className="text-center py-12 text-gray-400">
                  <Sparkles className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p>Ready to generate your blog post</p>
                </div>
              )}

              {isGenerating && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-pink-400"></div>
                    <span className="text-white">{currentStep}</span>
                  </div>

                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-pink-500 to-rose-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>

                  <p className="text-sm text-gray-400">{progress}% complete</p>
                </div>
              )}

              {error && (
                <div className="flex items-center space-x-3 text-red-400">
                  <AlertCircle className="w-5 h-5" />
                  <span>{error}</span>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}