@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom slider styles for content writer */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: #334155;
  height: 8px;
  border-radius: 4px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3b82f6, #06b6d4);
  cursor: pointer;
  border: 2px solid #1e293b;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.slider::-moz-range-track {
  background: #334155;
  height: 8px;
  border-radius: 4px;
  border: none;
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(45deg, #3b82f6, #06b6d4);
  cursor: pointer;
  border: 2px solid #1e293b;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* Prose styles for markdown content */
.prose-invert {
  color: #e2e8f0;
}

.prose-invert h1,
.prose-invert h2,
.prose-invert h3,
.prose-invert h4,
.prose-invert h5,
.prose-invert h6 {
  color: #f1f5f9;
}

.prose-invert strong {
  color: #f1f5f9;
}

.prose-invert code {
  background-color: #334155;
  color: #06b6d4;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.prose-invert pre {
  background-color: #1e293b;
  border: 1px solid #334155;
}

.prose-invert blockquote {
  border-left: 4px solid #3b82f6;
  background-color: #1e293b;
  padding: 1rem;
  margin: 1rem 0;
}

.prose-invert ul li::marker,
.prose-invert ol li::marker {
  color: #3b82f6;
}

/* Custom styles for markdown content */
.markdown-content {
  line-height: 1.7;
}

.markdown-content h1 {
  font-weight: 800;
  line-height: 1.2;
  margin-top: 0;
}

.markdown-content h2 {
  font-weight: 700;
  line-height: 1.3;
  position: relative;
}

.markdown-content h2::before {
  content: '';
  position: absolute;
  left: -24px;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #3b82f6, #6366f1);
  border-radius: 2px;
}

.markdown-content h3 {
  font-weight: 600;
  color: #374151;
}

.markdown-content p {
  margin-bottom: 1.5rem;
  text-align: justify;
}

.markdown-content ul li,
.markdown-content ol li {
  margin-bottom: 0.5rem;
  padding-left: 0.5rem;
}

.markdown-content blockquote {
  position: relative;
  font-style: italic;
  font-size: 1.1rem;
}

.markdown-content blockquote::before {
  content: '"';
  position: absolute;
  left: -0.5rem;
  top: -0.25rem;
  font-size: 3rem;
  color: #3b82f6;
  opacity: 0.3;
  font-family: serif;
}

.markdown-content code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
}

.markdown-content pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  position: relative;
}

.markdown-content pre::before {
  content: attr(data-language);
  position: absolute;
  top: 0.5rem;
  right: 0.75rem;
  font-size: 0.75rem;
  color: #9ca3af;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Custom scrollbar for reading mode */
.markdown-content::-webkit-scrollbar {
  width: 8px;
}

.markdown-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.markdown-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.markdown-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Prose enhancement for better typography */
.prose-enhanced {
  font-family: Georgia, 'Times New Roman', serif;
}

.prose-enhanced h1,
.prose-enhanced h2,
.prose-enhanced h3,
.prose-enhanced h4 {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Smooth transitions for interactive elements */
.transition-all {
  transition: all 0.15s ease-in-out;
}

/* Focus styles for accessibility */
.focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Enhanced button styles */
.btn-primary {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium px-6 py-3 rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply bg-white text-gray-700 font-medium px-6 py-3 rounded-lg border border-gray-300 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200;
}

/* Card styles */
.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
}

.card-header {
  @apply bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out forwards;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent;
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite alternate;
  }

  /* Sky blue aero glass theme utilities for blog display */
  .sky-card {
    @apply bg-white/20 backdrop-blur-xl border border-sky-200/30 rounded-2xl shadow-2xl;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(135, 206, 235, 0.15) 100%);
    box-shadow:
      0 8px 32px 0 rgba(31, 38, 135, 0.37),
      inset 0 1px 0 rgba(255, 255, 255, 0.5),
      inset 0 -1px 0 rgba(255, 255, 255, 0.2);
  }

  .sky-text {
    @apply text-slate-700;
  }

  .sky-heading {
    @apply text-sky-800 font-bold;
  }

  .sky-accent {
    @apply text-sky-600;
  }

  .sky-bg {
    background: linear-gradient(135deg, #87CEEB 0%, #E0F6FF 25%, #B0E0E6 50%, #F0F8FF 75%, #87CEFA 100%);
    min-height: 100vh;
    position: relative;
  }

  .sky-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(135, 206, 235, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }

  .sky-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 60% 10%, rgba(14, 165, 233, 0.1) 0%, transparent 40%),
      radial-gradient(circle at 90% 90%, rgba(6, 182, 212, 0.15) 0%, transparent 40%),
      radial-gradient(circle at 10% 50%, rgba(56, 189, 248, 0.1) 0%, transparent 40%);
    pointer-events: none;
    animation: float 8s ease-in-out infinite;
  }

  /* Enhanced aero glass effects */
  .aero-glass {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px 0 rgba(31, 38, 135, 0.37),
      inset 0 1px 0 rgba(255, 255, 255, 0.5),
      inset 0 -1px 0 rgba(255, 255, 255, 0.2);
  }

  .aero-glass-strong {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
      0 12px 40px 0 rgba(31, 38, 135, 0.5),
      inset 0 2px 0 rgba(255, 255, 255, 0.6),
      inset 0 -2px 0 rgba(255, 255, 255, 0.3);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  100% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.8);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* TipTap Editor Styles */
.ProseMirror {
  @apply max-w-none p-4 focus:outline-none text-gray-200 leading-relaxed;
  line-height: 1.7;
}

.ProseMirror h1 {
  @apply text-white text-3xl font-bold mb-4 mt-6;
}

.ProseMirror h2 {
  @apply text-white text-2xl font-bold mb-3 mt-5;
}

.ProseMirror h3 {
  @apply text-white text-xl font-bold mb-2 mt-4;
}

.ProseMirror p {
  @apply text-gray-200 mb-4;
}

.ProseMirror ul, .ProseMirror ol {
  @apply mb-4 pl-6;
}

.ProseMirror li {
  @apply text-gray-200 mb-1;
}

.ProseMirror blockquote {
  @apply border-l-4 border-blue-500 pl-4 italic text-gray-300 mb-4;
}

.ProseMirror code {
  @apply bg-gray-800 px-2 py-1 rounded text-blue-300;
}

.ProseMirror pre {
  @apply bg-gray-900 p-4 rounded-lg overflow-x-auto mb-4;
}

.ProseMirror strong {
  @apply font-bold text-white;
}

.ProseMirror em {
  @apply italic;
}

/* Loading animations */
.shimmer {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.1) 0%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Glass morphism effect */
.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Enhanced Glass Card with Glow */
.glass-card-glow {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  position: relative;
}

.glass-card-glow::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 0.875rem;
  padding: 2px;
  background: linear-gradient(45deg, #3B82F6, #8B5CF6, #EC4899);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glass-card-glow:hover::before {
  opacity: 0.5;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(to right, #3B82F6, #8B5CF6, #EC4899);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-animation 3s ease infinite;
  background-size: 200% 200%;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Animated gradient background */
.animated-gradient-bg {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Pulse animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Glow animation */
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.8);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Shimmer effect */
.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(
    105deg,
    transparent 40%,
    rgba(255, 255, 255, 0.7) 50%,
    transparent 60%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Neon glow text */
.neon-text {
  text-shadow: 
    0 0 10px #3B82F6,
    0 0 20px #3B82F6,
    0 0 30px #3B82F6,
    0 0 40px #8B5CF6,
    0 0 70px #8B5CF6,
    0 0 80px #8B5CF6,
    0 0 100px #8B5CF6,
    0 0 150px #8B5CF6;
}

/* Hover lift effect */
.hover-lift {
  transition: all 0.15s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Morphing blob */
.blob {
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
  50% {
    border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
  }
}

/* Smooth scroll */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3B82F6, #8B5CF6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563EB, #7C3AED);
}

/* Button hover effects */
.btn-hover-effect {
  position: relative;
  overflow: hidden;
  transition: all 0.15s ease;
}

.btn-hover-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-hover-effect:hover::before {
  width: 300px;
  height: 300px;
}

/* Card hover border animation */
.card-hover-border {
  position: relative;
  overflow: hidden;
}

.card-hover-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.card-hover-border:hover::before {
  left: 100%;
}

/* TipTap Editor Styles - Enhanced */
.ProseMirror {
  @apply max-w-none p-4 focus:outline-none text-gray-200 leading-relaxed;
  line-height: 1.7;
}

.ProseMirror h1 {
  @apply text-white text-3xl font-bold mb-4 mt-6;
}

.ProseMirror h2 {
  @apply text-white text-2xl font-bold mb-3 mt-5;
}

.ProseMirror h3 {
  @apply text-white text-xl font-bold mb-2 mt-4;
}

.ProseMirror p {
  @apply text-gray-200 mb-4;
}

.ProseMirror ul, .ProseMirror ol {
  @apply mb-4 pl-6;
}

.ProseMirror li {
  @apply text-gray-200 mb-1;
}

.ProseMirror blockquote {
  @apply border-l-4 border-blue-500 pl-4 italic text-gray-300 mb-4 bg-white/5 py-2 rounded-r-lg;
}

.ProseMirror code {
  @apply bg-gray-800 text-blue-300 px-1 py-0.5 rounded text-sm font-mono;
}

.ProseMirror pre {
  @apply bg-gray-900 p-4 rounded-lg overflow-x-auto mb-4 border border-gray-700;
}

.ProseMirror pre code {
  @apply bg-transparent p-0 text-gray-300;
}

.ProseMirror strong {
  @apply font-bold text-white;
}

.ProseMirror em {
  @apply italic;
}

.ProseMirror a {
  @apply text-blue-400 underline hover:text-blue-300 transition-colors;
}

.ProseMirror hr {
  @apply border-gray-700 my-6;
}

/* Enhanced Table Styles for Editor */
.ProseMirror table {
  @apply w-full border-collapse mb-6 mt-4 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-white/20 rounded-xl overflow-hidden shadow-xl;
}

.ProseMirror table thead {
  @apply bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20;
}

.ProseMirror table th {
  @apply px-6 py-4 text-left font-semibold text-white border-r border-white/10 last:border-r-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10;
  position: relative;
}

.ProseMirror table th::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
}

.ProseMirror table td {
  @apply px-6 py-4 text-gray-200 border-r border-white/10 last:border-r-0 border-b border-white/10;
  transition: all 0.2s ease;
}

.ProseMirror table tr:last-child td {
  @apply border-b-0;
}

.ProseMirror table tr:hover td {
  @apply bg-white/5;
}

.ProseMirror table tr:nth-child(even) {
  @apply bg-white/5;
}

/* Special table styles for comparison tables */
.ProseMirror table.comparison-table {
  @apply shadow-2xl;
}

.ProseMirror table.comparison-table th {
  @apply text-center font-bold text-sm uppercase tracking-wider;
}

.ProseMirror table.comparison-table td {
  @apply text-center;
}

/* Feature highlight cells */
.ProseMirror table td.feature-highlight {
  @apply bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-300 font-semibold;
}

.ProseMirror table td.feature-missing {
  @apply bg-gradient-to-r from-red-500/20 to-pink-500/20 text-red-300;
}

.ProseMirror table td.feature-neutral {
  @apply bg-gradient-to-r from-gray-500/20 to-slate-500/20 text-gray-300;
}

/* Table responsive design */
@media (max-width: 768px) {
  .ProseMirror table {
    @apply text-sm;
  }
  
  .ProseMirror table th,
  .ProseMirror table td {
    @apply px-3 py-2;
  }
}

/* Selection highlight */
.ProseMirror ::selection {
  @apply bg-blue-500/30 text-white;
}

/* Focus styles */
.ProseMirror:focus {
  @apply outline-none;
}

.ProseMirror.ProseMirror-focused {
  @apply outline-none ring-2 ring-blue-500/20 rounded-lg;
}

/* Placeholder */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9CA3AF;
  pointer-events: none;
  height: 0;
}

/* Chart animations */
.recharts-line-curve {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: draw-line 2s ease-in-out forwards;
}

@keyframes draw-line {
  to {
    stroke-dashoffset: 0;
  }
}

.recharts-bar {
  animation: grow-bar 1s ease-out forwards;
  transform-origin: bottom;
}

@keyframes grow-bar {
  from {
    transform: scaleY(0);
  }
  to {
    transform: scaleY(1);
  }
}

/* Notification badge pulse */
.notification-pulse {
  position: relative;
}

.notification-pulse::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: inherit;
  animation: pulse-ring 1.5s infinite;
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .gradient-text {
    font-size: 90%;
  }
  
  .glass-card {
    padding: 1rem;
  }
}

/* Agent-specific color themes */
:root {

  --superagent-primary: #3B82F6;
  --superagent-secondary: #06B6D4;
  --blog-primary: #10B981;
  --blog-secondary: #34D399;
  --email-primary: #F97316;
  --email-secondary: #EF4444;
  --tweet-primary: #0EA5E9;
  --tweet-secondary: #3B82F6;
  --youtube-primary: #EF4444;
  --youtube-secondary: #EC4899;

  /* Sky blue aero glass color scheme for blog display */
  --sky-primary: #0EA5E9;
  --sky-secondary: #3B82F6;
  --sky-accent: #06B6D4;
  --sky-bg: #E0F6FF;
  --sky-text: #334155;
  --sky-border: #7DD3FC;
}

/* Agent-specific animations */


@keyframes superagent-wave {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes blog-grow {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes email-slide {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes tweet-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  25% {
    transform: translateY(-5px);
  }
  75% {
    transform: translateY(5px);
  }
}

@keyframes youtube-play {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(180deg);
  }
  100% {
    transform: scale(1) rotate(360deg);
  }
}

/* Agent-specific hover effects */


.agent-superagent:hover {
  animation: superagent-wave 2s ease-in-out infinite;
}

.agent-blog:hover {
  animation: blog-grow 2s ease-in-out infinite;
}

.agent-email:hover {
  animation: email-slide 1s ease-out;
}

.agent-tweet:hover {
  animation: tweet-bounce 1s ease-in-out infinite;
}

.agent-youtube:hover {
  animation: youtube-play 3s linear infinite;
}

/* Progress bar styles for each agent */


.progress-superagent {
  background: linear-gradient(to right, var(--superagent-primary), var(--superagent-secondary));
}

.progress-blog {
  background: linear-gradient(to right, var(--blog-primary), var(--blog-secondary));
}

.progress-email {
  background: linear-gradient(to right, var(--email-primary), var(--email-secondary));
}

.progress-tweet {
  background: linear-gradient(to right, var(--tweet-primary), var(--tweet-secondary));
}

.progress-youtube {
  background: linear-gradient(to right, var(--youtube-primary), var(--youtube-secondary));
}

/* Workflow step animations */
@keyframes workflow-step-active {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

.workflow-step-active {
  animation: workflow-step-active 2s ease-in-out infinite;
}

/* Card glow effects for each agent */


.glow-superagent {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

.glow-blog {
  box-shadow: 0 0 30px rgba(16, 185, 129, 0.3);
}

.glow-email {
  box-shadow: 0 0 30px rgba(249, 115, 22, 0.3);
}

.glow-tweet {
  box-shadow: 0 0 30px rgba(14, 165, 233, 0.3);
}

.glow-youtube {
  box-shadow: 0 0 30px rgba(239, 68, 68, 0.3);
}

/* Blog heading styles with image generation */
.heading-with-image-container {
  @apply mb-6;
}

.heading-with-image-container .heading-level-1 {
  @apply text-3xl font-bold text-sky-800 mb-4;
}

.heading-with-image-container .heading-level-2 {
  @apply text-2xl font-bold text-sky-700 mb-3;
}

.heading-with-image-container .heading-level-3 {
  @apply text-xl font-semibold text-sky-700 mb-3;
}

.heading-with-image-container .heading-level-4 {
  @apply text-lg font-semibold text-sky-600 mb-2;
}

/* Image generation button animations */
.heading-with-image-container:hover .group-hover\:opacity-100 {
  opacity: 1;
}

/* Generated image styles */
.heading-with-image-container img {
  @apply transition-all duration-300 hover:scale-105;
}

/* Purple slider styling */
.slider-purple {
  background: linear-gradient(to right, #6b46c1 0%, #6b46c1 var(--value, 0%), #374151 var(--value, 0%), #374151 100%);
}

.slider-purple::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #8b5cf6;
  border: 2px solid #6b46c1;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

.slider-purple::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #8b5cf6;
  border: 2px solid #6b46c1;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

/* Custom scrollbar */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.scrollbar-track-gray-800::-webkit-scrollbar-track {
  background-color: #1f2937;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #1f2937;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Custom sidebar scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Compact Mode Styles */
.compact .p-6 {
  padding: 1rem !important;
}

.compact .p-8 {
  padding: 1.5rem !important;
}

.compact .space-y-4 > * + * {
  margin-top: 0.75rem !important;
}

.compact .space-y-6 > * + * {
  margin-top: 1rem !important;
}

.compact .space-y-8 > * + * {
  margin-top: 1.5rem !important;
}

.compact .gap-6 {
  gap: 1rem !important;
}

.compact .gap-8 {
  gap: 1.5rem !important;
}

.compact .text-2xl {
  font-size: 1.25rem !important;
}

.compact .text-xl {
  font-size: 1.125rem !important;
}

.compact .h-12 {
  height: 2.5rem !important;
}

.compact .w-12 {
  width: 2.5rem !important;
}

.compact .py-4 {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
}

.compact .px-4 {
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
}

.compact .mb-8 {
  margin-bottom: 1.5rem !important;
}

.compact .mb-6 {
  margin-bottom: 1rem !important;
}

.compact .mb-4 {
  margin-bottom: 0.75rem !important;
}
