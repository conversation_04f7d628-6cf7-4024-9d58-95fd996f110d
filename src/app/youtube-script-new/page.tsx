'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  ArrowLeft,
  Video,
  Zap,
  Clock,
  Users,
  Target,
  FileText,
  Download,
  Copy,
  Play,
  BarChart3,
  Sparkles,
  CheckCircle,
  Loader2,
  Youtube,
  Brain,
  Cpu
} from 'lucide-react'
import Link from 'next/link'
import ReactMarkdown from 'react-markdown'

interface GeneratedScript {
  title: string
  hook: string
  introduction: string
  mainContent: string
  conclusion: string
  fullScript: string
  timestamps: Array<{ time: string, section: string, content: string }>
  engagementElements: Array<{ type: string, timing: string, description: string }>
  visualCues: Array<{ timing: string, suggestion: string }>
  metadata: {
    estimatedLength: string
    keyTopics: string[]
    engagementScore: number
    researchVideos: Array<{ title: string, views: number, engagement: number }>
  }
}

export default function YouTubeScriptGeneratorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Form state
  const [topic, setTopic] = useState('')
  const [duration, setDuration] = useState('3-7')
  const [style, setStyle] = useState('educational')
  const [targetAudience, setTargetAudience] = useState('general')
  const [additionalNotes, setAdditionalNotes] = useState('')

  // Generation state
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedScript, setGeneratedScript] = useState<GeneratedScript | null>(null)
  const [activeTab, setActiveTab] = useState('input')
  const [copied, setCopied] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-red-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return null
  }

  const handleGenerate = async () => {
    if (!topic.trim()) return

    setIsGenerating(true)
    setActiveTab('generating')

    try {
      const response = await fetch('/api/generate/youtube-script-new', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topic,
          duration,
          style,
          targetAudience,
          additionalNotes
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setGeneratedScript(data.script)
        setActiveTab('script')
      } else {
        alert('Error: ' + data.error)
      }
    } catch (error) {
      console.error('Generation failed:', error)
      alert('Failed to generate script. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <div className="border-b border-gray-800 bg-black/50 backdrop-blur-lg sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="text-gray-400 hover:text-white transition-colors">
                <ArrowLeft className="w-6 h-6" />
              </Link>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-red-600 rounded-lg">
                  <Youtube className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">YouTube Script Generator</h1>
                  <p className="text-sm text-gray-400">Powered by Kimi-K2 + chutes/fp4</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <Brain className="w-4 h-4" />
              <span>OpenRouter</span>
              <Cpu className="w-4 h-4" />
              <span>Kimi-K2</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-800/50 p-1 rounded-xl mb-8">
          {[
            { id: 'input', label: 'Input', icon: Target },
            { id: 'generating', label: 'Generating', icon: Loader2 },
            { id: 'script', label: 'Script', icon: FileText }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              disabled={tab.id === 'generating' && !isGenerating}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-red-600 text-white shadow-lg'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
              }`}
            >
              <tab.icon className={`w-4 h-4 ${tab.id === 'generating' && isGenerating ? 'animate-spin' : ''}`} />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Input Tab */}
        {activeTab === 'input' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700/50 rounded-2xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Create Your YouTube Script</h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Topic Input */}
                <div className="lg:col-span-2">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Video Topic *
                  </label>
                  <input
                    type="text"
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    placeholder="e.g., How to start a YouTube channel in 2025"
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  />
                </div>

                {/* Duration */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    <Clock className="w-4 h-4 inline mr-1" />
                    Duration
                  </label>
                  <select
                    value={duration}
                    onChange={(e) => setDuration(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    <option value="1-3">1-3 minutes</option>
                    <option value="3-7">3-7 minutes</option>
                    <option value="7-15">7-15 minutes</option>
                    <option value="15+">15+ minutes</option>
                  </select>
                </div>

                {/* Style */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    <Video className="w-4 h-4 inline mr-1" />
                    Style
                  </label>
                  <select
                    value={style}
                    onChange={(e) => setStyle(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    <option value="educational">Educational</option>
                    <option value="entertaining">Entertaining</option>
                    <option value="tutorial">Tutorial</option>
                    <option value="review">Review</option>
                    <option value="vlog">Vlog</option>
                    <option value="news">News/Commentary</option>
                  </select>
                </div>

                {/* Target Audience */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    <Users className="w-4 h-4 inline mr-1" />
                    Target Audience
                  </label>
                  <select
                    value={targetAudience}
                    onChange={(e) => setTargetAudience(e.target.value)}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    <option value="general">General Audience</option>
                    <option value="beginners">Beginners</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                    <option value="professionals">Professionals</option>
                    <option value="students">Students</option>
                  </select>
                </div>

                {/* Additional Notes */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Additional Notes
                  </label>
                  <textarea
                    value={additionalNotes}
                    onChange={(e) => setAdditionalNotes(e.target.value)}
                    placeholder="Any specific requirements, tone, or focus areas..."
                    rows={3}
                    className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 resize-none"
                  />
                </div>
              </div>

              {/* Generate Button */}
              <div className="mt-8 text-center">
                <motion.button
                  onClick={handleGenerate}
                  disabled={!topic.trim() || isGenerating}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="px-8 py-4 bg-gradient-to-r from-red-600 to-orange-600 text-white font-semibold rounded-xl hover:from-red-700 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center space-x-2 mx-auto"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span>Generating Script...</span>
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-5 h-5" />
                      <span>Generate YouTube Script</span>
                    </>
                  )}
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Generating Tab */}
        {activeTab === 'generating' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16"
          >
            <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700/50 rounded-2xl p-12">
              <div className="animate-spin w-16 h-16 border-4 border-red-400 border-t-transparent rounded-full mx-auto mb-6"></div>
              <h3 className="text-2xl font-bold text-white mb-4">Generating Your Script</h3>
              <p className="text-gray-400 mb-6">Using Kimi-K2 model with chutes/fp4 provider for optimal results...</p>
              
              <div className="space-y-3 text-left max-w-md mx-auto">
                <div className="flex items-center space-x-3 text-gray-300">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span>Researching topic and trends</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <Loader2 className="w-5 h-5 animate-spin text-red-400" />
                  <span>Generating optimized script content</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-400">
                  <div className="w-5 h-5 border-2 border-gray-600 rounded-full"></div>
                  <span>Finalizing structure and engagement elements</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Script Tab */}
        {activeTab === 'script' && generatedScript && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            {/* Script Header */}
            <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700/50 rounded-2xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold text-white">{generatedScript.title}</h2>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => copyToClipboard(generatedScript.fullScript)}
                    className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2"
                  >
                    {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    <span>{copied ? 'Copied!' : 'Copy'}</span>
                  </button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
                <div className="bg-gray-700/50 rounded-lg p-3">
                  <div className="text-2xl font-bold text-red-400">{generatedScript.metadata.estimatedLength}</div>
                  <div className="text-sm text-gray-400">Duration</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-3">
                  <div className="text-2xl font-bold text-green-400">{generatedScript.metadata.engagementScore}%</div>
                  <div className="text-sm text-gray-400">Engagement</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-3">
                  <div className="text-2xl font-bold text-blue-400">{generatedScript.metadata.keyTopics.length}</div>
                  <div className="text-sm text-gray-400">Key Topics</div>
                </div>
                <div className="bg-gray-700/50 rounded-lg p-3">
                  <div className="text-2xl font-bold text-purple-400">{generatedScript.timestamps.length}</div>
                  <div className="text-sm text-gray-400">Sections</div>
                </div>
              </div>
            </div>

            {/* Full Script */}
            <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700/50 rounded-2xl p-6">
              <h3 className="text-xl font-bold text-white mb-4">Complete Script</h3>
              <div className="bg-gray-900/50 rounded-lg p-6 max-h-96 overflow-y-auto">
                <ReactMarkdown className="prose prose-invert max-w-none text-gray-300">
                  {generatedScript.fullScript}
                </ReactMarkdown>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  )
}
