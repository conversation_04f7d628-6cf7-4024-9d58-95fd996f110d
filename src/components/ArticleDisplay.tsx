import { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface ArticleDisplayProps {
  content: string;
  title?: string;
  onCopy?: () => void;
  copied?: boolean;
  showWordCount?: boolean;
}

export default function ArticleDisplay({ 
  content, 
  title, 
  onCopy, 
  copied, 
  showWordCount = true 
}: ArticleDisplayProps) {
  const [readingMode, setReadingMode] = useState(false);

  const handleFullscreen = () => {
    setReadingMode(!readingMode);
  };

  // Extract meta description from content
  const extractMetaDescription = (content: string): { cleanContent: string; metaDescription: string | null } => {
    const metaMatch = content.match(/\*\*Meta Description:\*\*\s*(.+)/);
    if (metaMatch) {
      const metaDescription = metaMatch[1];
      const cleanContent = content.replace(/\*\*Meta Description:\*\*\s*(.+)\n?/g, '').replace(/^[\n\s]+/, '');
      return { cleanContent, metaDescription };
    }
    return { cleanContent: content, metaDescription: null };
  };

  const { cleanContent, metaDescription } = extractMetaDescription(content);

  // Extract title from content if not provided
  const getDisplayTitle = () => {
    if (title && title !== 'AI Generated Article') {
      return title;
    }
    
    // Try to extract title from markdown content
    const lines = cleanContent.split('\n');
    for (const line of lines) {
      const trimmed = line.trim();
      if (trimmed.startsWith('# ')) {
        return trimmed.substring(2).trim();
      }
    }
    
    return title || 'AI Generated Article';
  };

  const displayTitle = getDisplayTitle();
  const wordCount = cleanContent.split(/\s+/).filter(word => word.length > 0).length;
  const readingTime = Math.ceil(wordCount / 200);

  return (
    <>
      {/* Regular Display */}
      {!readingMode && (
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">
                {displayTitle}
              </h3>
              {metaDescription && (
                <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-3 rounded-r-lg">
                  <div className="flex items-center">
                    <div className="ml-3">
                      <div className="text-sm font-medium text-blue-800">Meta Description</div>
                      <div className="text-sm text-blue-700">{metaDescription}</div>
                    </div>
                  </div>
                </div>
              )}
              <p className="text-gray-600">Ready to publish and rank #1 on Google</p>
            </div>
            
            <div className="flex items-center space-x-3">
              {showWordCount && (
                <div className="text-right">
                  <div className="text-sm text-gray-500">Article Length</div>
                  <div className="text-lg font-medium text-gray-900">
                    {wordCount.toLocaleString()} words
                  </div>
                  <div className="text-xs text-gray-500">
                    ~{readingTime} min read
                  </div>
                </div>
              )}
              
              <button
                onClick={handleFullscreen}
                className="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors flex items-center space-x-2"
              >
                <span>📖</span>
                <span>Reading Mode</span>
              </button>
              
              {onCopy && (
                <button
                  onClick={onCopy}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <span>{copied ? '✓' : '📋'}</span>
                  <span>{copied ? 'Copied!' : 'Copy'}</span>
                </button>
              )}
            </div>
          </div>

          {/* Article Preview */}
          <div className="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-sm">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-lg">📝</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Article Content</h4>
                    <p className="text-sm text-gray-600">SEO-optimized and ready to publish</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-medium">
                    {Math.round(wordCount / 1000)}k words
                  </span>
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full font-medium">
                    ~{readingTime} min read
                  </span>
                </div>
              </div>
            </div>
            
            <div className="p-6 max-h-96 overflow-y-auto">
              <div className="max-w-none">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  components={{
                    h1: ({ children }) => (
                      <h1 className="text-3xl font-bold text-gray-900 mb-6 leading-tight border-b border-gray-200 pb-4">
                        {children}
                      </h1>
                    ),
                    h2: ({ children }) => (
                      <h2 className="text-2xl font-semibold text-gray-800 mb-4 mt-8 leading-tight">
                        {children}
                      </h2>
                    ),
                    h3: ({ children }) => (
                      <h3 className="text-xl font-semibold text-gray-800 mb-3 mt-6">
                        {children}
                      </h3>
                    ),
                    p: ({ children }) => (
                      <p className="text-gray-700 leading-relaxed mb-4 text-base">
                        {children}
                      </p>
                    ),
                    ul: ({ children }) => (
                      <ul className="space-y-2 mb-6 ml-6 text-gray-700 list-disc list-outside">
                        {children}
                      </ul>
                    ),
                    ol: ({ children }) => (
                      <ol className="space-y-2 mb-6 ml-6 text-gray-700 list-decimal list-outside">
                        {children}
                      </ol>
                    ),
                    li: ({ children }) => (
                      <li className="text-gray-700 leading-relaxed">
                        {children}
                      </li>
                    ),
                    strong: ({ children }) => (
                      <strong className="font-semibold text-gray-900">{children}</strong>
                    ),
                    blockquote: ({ children }) => (
                      <blockquote className="border-l-4 border-blue-500 pl-8 pr-6 italic text-gray-700 my-8 bg-gradient-to-r from-blue-50 to-purple-50 py-6 rounded-r-2xl shadow-lg relative">
                        <div className="text-lg leading-relaxed">
                          <span className="absolute -top-2 -left-2 text-3xl text-blue-400 opacity-50">"</span>
                          {children}
                          <span className="absolute -bottom-4 -right-2 text-3xl text-blue-400 opacity-50">"</span>
                        </div>
                      </blockquote>
                    ),
                    code: ({ children }) => (
                      <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">
                        {children}
                      </code>
                    ),
                  }}
                >
                  {cleanContent}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reading Mode Overlay */}
      {readingMode && (
        <div className="fixed inset-0 bg-white z-50 overflow-hidden">
          {/* Header */}
          <div className="sticky top-0 bg-white border-b border-gray-200 px-8 py-4 flex items-center justify-between shadow-sm">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleFullscreen}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg transition-colors flex items-center space-x-2"
              >
                <span>←</span>
                <span>Exit Reading</span>
              </button>
              <div className="text-sm text-gray-500">
                {readingTime} min read • {wordCount.toLocaleString()} words
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              {onCopy && (
                <button
                  onClick={onCopy}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <span>{copied ? '✓' : '📋'}</span>
                  <span>{copied ? 'Copied!' : 'Copy Article'}</span>
                </button>
              )}
            </div>
          </div>

          {/* Article Content */}
          <div className="h-full overflow-y-auto bg-gradient-to-br from-gray-50 to-white">
            <div className="max-w-4xl mx-auto px-8 py-12">
              <article className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
                {/* Article Header */}
                <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-12 py-8 text-white">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                      <span className="text-2xl">📝</span>
                    </div>
                    <div>
                      <div className="text-blue-100 text-sm font-medium">Generated by AI Agent</div>
                      <div className="text-white text-lg font-semibold">SEO-Optimized Article</div>
                    </div>
                  </div>
                  <h1 className="text-3xl font-bold leading-tight mb-2">{displayTitle}</h1>
                  {metaDescription && (
                    <div className="bg-white bg-opacity-20 rounded-lg p-4 mb-4">
                      <div className="text-blue-100 text-sm font-medium mb-1">Meta Description</div>
                      <div className="text-white text-base">{metaDescription}</div>
                    </div>
                  )}
                  <div className="flex items-center space-x-6 text-blue-100 text-sm">
                    <span>🎯 Ready to rank #1 on Google</span>
                    <span>⚡ Competitor analysis included</span>
                    <span>🔍 Keyword optimized</span>
                  </div>
                </div>

                {/* Article Body */}
                <div className="px-12 py-10">
                  <div className="max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        h1: ({ children }) => (
                          <h1 className="text-4xl font-bold text-gray-900 mb-8 leading-tight border-b-2 border-gray-200 pb-6">
                            {children}
                          </h1>
                        ),
                        h2: ({ children }) => (
                          <h2 className="text-3xl font-bold text-gray-800 mb-6 mt-12 leading-tight pl-4 border-l-4 border-blue-500 relative">
                            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                              {children}
                            </span>
                          </h2>
                        ),
                        h3: ({ children }) => (
                          <h3 className="text-2xl font-semibold text-gray-800 mb-4 mt-8">
                            {children}
                          </h3>
                        ),
                        h4: ({ children }) => (
                          <h4 className="text-xl font-semibold text-gray-800 mb-3 mt-6">
                            {children}
                          </h4>
                        ),
                        p: ({ children }) => (
                          <p className="text-gray-700 leading-relaxed mb-6 text-lg">
                            {children}
                          </p>
                        ),
                        ul: ({ children }) => (
                          <ul className="space-y-3 mb-8 ml-6 list-disc list-outside">
                            {children}
                          </ul>
                        ),
                        ol: ({ children }) => (
                          <ol className="space-y-3 mb-8 ml-6 list-decimal list-outside">
                            {children}
                          </ol>
                        ),
                        li: ({ children }) => (
                          <li className="text-gray-700 text-lg leading-relaxed hover:text-gray-900 transition-colors">
                            {children}
                          </li>
                        ),
                        strong: ({ children }) => (
                          <strong className="font-bold text-gray-900 bg-gradient-to-r from-yellow-200 to-yellow-300 px-2 py-1 rounded-lg shadow-sm">
                            {children}
                          </strong>
                        ),
                        em: ({ children }) => (
                          <em className="italic text-gray-600">{children}</em>
                        ),
                        blockquote: ({ children }) => (
                          <blockquote className="border-l-4 border-blue-500 pl-8 pr-4 italic text-gray-600 my-8 bg-gradient-to-r from-blue-50 to-indigo-50 py-6 rounded-r-xl">
                            <div className="text-xl leading-relaxed">{children}</div>
                          </blockquote>
                        ),
                        code: ({ children }) => (
                          <code className="bg-gray-100 text-gray-800 px-3 py-1 rounded-lg text-base font-mono border">
                            {children}
                          </code>
                        ),
                        pre: ({ children }) => (
                          <pre className="bg-gray-900 text-gray-100 p-6 rounded-xl overflow-x-auto my-6 border">
                            <code className="text-sm">{children}</code>
                          </pre>
                        ),
                        table: ({ children }) => (
                          <div className="my-8 overflow-hidden rounded-2xl shadow-2xl border border-white/20 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm">
                            <table className="w-full border-collapse">
                              {children}
                            </table>
                          </div>
                        ),
                        thead: ({ children }) => (
                          <thead className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20">
                            {children}
                          </thead>
                        ),
                        tbody: ({ children }) => (
                          <tbody className="divide-y divide-white/10">
                            {children}
                          </tbody>
                        ),
                        tr: ({ children }) => (
                          <tr className="hover:bg-white/5 transition-all duration-200">
                            {children}
                          </tr>
                        ),
                        th: ({ children }) => (
                          <th className="px-6 py-4 text-left font-semibold text-white border-r border-white/10 last:border-r-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 relative">
                            <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                            {children}
                          </th>
                        ),
                        td: ({ children }) => (
                          <td className="px-6 py-4 text-gray-200 border-r border-white/10 last:border-r-0 font-medium">
                            {children}
                          </td>
                        ),
                      }}
                    >
                      {cleanContent}
                    </ReactMarkdown>
                  </div>
                </div>

                {/* Article Footer */}
                <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-12 py-8 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      <p className="mb-2">
                        <strong>Generated by:</strong> AI Agent System
                      </p>
                      <p>
                        <strong>Optimization:</strong> SEO keywords, competitor analysis, human-optimized writing patterns
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        {wordCount.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">words</div>
                    </div>
                  </div>
                </div>
              </article>
            </div>
          </div>
        </div>
      )}
    </>
  );
} 