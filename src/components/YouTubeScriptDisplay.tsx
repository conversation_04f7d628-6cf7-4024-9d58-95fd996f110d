'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ArrowLeft, Copy, Download, Clock, FileText, Play, Pause, Camera, Eye, Share, Bookmark } from 'lucide-react'
import Link from 'next/link'

interface YouTubeScriptDisplayProps {
  title: string
  content: string
  wordCount?: number
}

export default function YouTubeScriptDisplay({ title, content, wordCount }: YouTubeScriptDisplayProps) {
  const [copied, setCopied] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState('0:00')
  const [progress, setProgress] = useState(0)

  // Calculate metrics
  const calculatedWordCount = wordCount || content.split(/\s+/).filter(word => word.length > 0).length
  const readingTime = Math.ceil(calculatedWordCount / 200)
  const estimatedVideoLength = Math.ceil(calculatedWordCount / 150) // Slower pace for video

  // Simulate video progress when playing
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isPlaying) {
      interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + (100 / (estimatedVideoLength * 60)) // Progress per second
          if (newProgress >= 100) {
            setIsPlaying(false)
            return 100
          }
          
          // Update time display
          const currentSeconds = Math.floor((newProgress / 100) * estimatedVideoLength * 60)
          const minutes = Math.floor(currentSeconds / 60)
          const seconds = currentSeconds % 60
          setCurrentTime(`${minutes}:${seconds.toString().padStart(2, '0')}`)
          
          return newProgress
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isPlaying, estimatedVideoLength])

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  const downloadScript = () => {
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}-script.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const resetProgress = () => {
    setProgress(0)
    setCurrentTime('0:00')
    setIsPlaying(false)
  }

  // Parse content into sections for better readability
  const parseContentSections = (content: string) => {
    // Split by common section markers or double line breaks
    const sections = content.split(/\n\n+|\[.*?\]/).filter(section => section.trim())
    
    if (sections.length <= 1) {
      return [{ title: 'Script', content: content.trim() }]
    }

    return sections.map((section, index) => {
      const trimmed = section.trim()
      if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
        return { title: trimmed.slice(1, -1), content: '' }
      }
      
      // Auto-detect section types based on content
      const lowerContent = trimmed.toLowerCase()
      let sectionTitle = `Section ${index + 1}`
      
      if (index === 0 || lowerContent.includes('hook') || lowerContent.includes('intro')) {
        sectionTitle = '🎬 Introduction'
      } else if (lowerContent.includes('main') || lowerContent.includes('content') || lowerContent.includes('body')) {
        sectionTitle = '📝 Main Content'
      } else if (lowerContent.includes('conclusion') || lowerContent.includes('outro') || lowerContent.includes('end')) {
        sectionTitle = '🎯 Conclusion'
      } else if (lowerContent.includes('call to action') || lowerContent.includes('subscribe')) {
        sectionTitle = '🔔 Call to Action'
      }
      
      return { title: sectionTitle, content: trimmed }
    }).filter(section => section.content)
  }

  const scriptSections = parseContentSections(content)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Subtle Background Effects */}
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,rgba(59,130,246,0.1),transparent_50%)]" />
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_bottom_right,rgba(239,68,68,0.1),transparent_50%)]" />
      
      {/* Header */}
      <motion.header 
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative z-10 bg-slate-900/50 backdrop-blur-lg border-b border-slate-700/50"
      >
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Link 
                href="/content" 
                className="flex items-center space-x-2 text-slate-400 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
                <span>Back to Content</span>
              </Link>
              
              <div className="h-6 w-px bg-slate-600" />
              
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                  <Camera className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-white">{title}</h1>
                  <p className="text-sm text-slate-400">YouTube Script</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-6 text-sm text-slate-400">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>{readingTime} min read</span>
                </div>
                <div className="flex items-center space-x-2">
                  <FileText className="w-4 h-4" />
                  <span>{calculatedWordCount.toLocaleString()} words</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Eye className="w-4 h-4" />
                  <span>~{estimatedVideoLength} min video</span>
                </div>
              </div>
              
              <div className="h-6 w-px bg-slate-600" />
              
              <div className="flex items-center space-x-2">
                <motion.button
                  onClick={copyToClipboard}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-white bg-slate-700 hover:bg-slate-600 rounded-lg transition-all"
                >
                  <Copy className="w-4 h-4" />
                  <span>{copied ? 'Copied!' : 'Copy'}</span>
                </motion.button>
                
                <motion.button
                  onClick={downloadScript}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-all"
                >
                  <Download className="w-4 h-4" />
                  <span>Download</span>
                </motion.button>
              </div>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Main Content */}
      <div className="relative z-10 max-w-6xl mx-auto p-6">
        {/* Video Player Simulator */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-slate-800/50 backdrop-blur-lg border border-slate-700/50 rounded-2xl p-6 mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-white flex items-center space-x-2">
              <Camera className="w-5 h-5 text-red-500" />
              <span>Video Preview Simulator</span>
            </h2>
            <div className="flex items-center space-x-3">
              <motion.button
                onClick={() => setIsPlaying(!isPlaying)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all"
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                <span>{isPlaying ? 'Pause' : 'Play'}</span>
              </motion.button>
              <button
                onClick={resetProgress}
                className="p-2 text-slate-400 hover:text-white transition-colors"
                title="Reset"
              >
                <ArrowLeft className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mb-4">
            <div className="flex items-center justify-between text-sm text-slate-400 mb-2">
              <span>{currentTime}</span>
              <span>{Math.floor(estimatedVideoLength)}:{String((estimatedVideoLength % 1) * 60).padStart(2, '0')}</span>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-2">
              <motion.div 
                className="bg-red-500 h-2 rounded-full"
                style={{ width: `${progress}%` }}
                transition={{ duration: 0.1 }}
              />
            </div>
          </div>

          {/* Video Stats */}
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="bg-slate-700/50 rounded-lg p-3">
              <div className="text-lg font-bold text-white">{calculatedWordCount.toLocaleString()}</div>
              <div className="text-sm text-slate-400">Words</div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-3">
              <div className="text-lg font-bold text-white">~{estimatedVideoLength}min</div>
              <div className="text-sm text-slate-400">Estimated Length</div>
            </div>
            <div className="bg-slate-700/50 rounded-lg p-3">
              <div className="text-lg font-bold text-white">{scriptSections.length}</div>
              <div className="text-sm text-slate-400">Sections</div>
            </div>
          </div>
        </motion.div>

        {/* Script Content */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-slate-800/50 backdrop-blur-lg border border-slate-700/50 rounded-2xl overflow-hidden"
        >
          <div className="p-6 border-b border-slate-700/50">
            <h2 className="text-xl font-bold text-white mb-2">Script Content</h2>
            <p className="text-slate-400">Your complete YouTube script with clear sections and formatting</p>
          </div>
          
          <div className="p-6">
            {scriptSections.length > 1 ? (
              <div className="space-y-8">
                {scriptSections.map((section, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border-l-4 border-red-500 pl-6"
                  >
                    <h3 className="text-lg font-semibold text-white mb-3">{section.title}</h3>
                    <div className="prose prose-invert max-w-none">
                      <div className="text-slate-300 leading-relaxed whitespace-pre-wrap font-mono text-sm bg-slate-900/50 rounded-lg p-4">
                        {section.content}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="prose prose-invert max-w-none">
                <div className="text-slate-300 leading-relaxed whitespace-pre-wrap font-mono text-sm bg-slate-900/50 rounded-lg p-6">
                  {content}
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-8 flex flex-wrap gap-4 justify-center"
        >
          <motion.button
            onClick={copyToClipboard}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all font-medium"
          >
            <Copy className="w-5 h-5" />
            <span>{copied ? 'Copied to Clipboard!' : 'Copy Script'}</span>
          </motion.button>
          
          <motion.button
            onClick={downloadScript}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center space-x-2 px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all font-medium"
          >
            <Download className="w-5 h-5" />
            <span>Download Script</span>
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all font-medium"
          >
            <Share className="w-5 h-5" />
            <span>Share Script</span>
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center space-x-2 px-6 py-3 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-all font-medium"
          >
            <Bookmark className="w-5 h-5" />
            <span>Save for Later</span>
          </motion.button>
        </motion.div>
      </div>
    </div>
  )
} 