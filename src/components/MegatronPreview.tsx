'use client';

import { motion } from 'framer-motion';
import { Zap, Youtube, Brain } from 'lucide-react';

export default function MegatronPreview() {
  return (
    <div className="relative w-full h-32 overflow-hidden rounded-lg bg-gradient-to-br from-red-900/50 to-orange-900/50">
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="relative">
          {/* Main Megatron Icon */}
          <motion.div
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ duration: 3, repeat: Infinity }}
            className="w-16 h-16 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center relative z-10"
          >
            <Zap className="w-8 h-8 text-white" />
          </motion.div>
          
          {/* Orbiting YouTube Icon */}
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
            className="absolute inset-0 w-16 h-16"
          >
            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
              <motion.div
                animate={{ scale: [0.8, 1, 0.8] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-6 h-6 bg-red-600 rounded-full flex items-center justify-center"
              >
                <Youtube className="w-3 h-3 text-white" />
              </motion.div>
            </div>
          </motion.div>

          {/* Orbiting Brain Icon */}
          <motion.div
            animate={{ rotate: -360 }}
            transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
            className="absolute inset-0 w-16 h-16"
          >
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
              <motion.div
                animate={{ scale: [0.8, 1, 0.8] }}
                transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
                className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center"
              >
                <Brain className="w-3 h-3 text-white" />
              </motion.div>
            </div>
          </motion.div>
          
          {/* Animated particles */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-orange-400 rounded-full"
              style={{
                left: `${20 + (i * 10)}%`,
                top: `${30 + (i % 2) * 40}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.3, 1, 0.3],
                scale: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.3,
              }}
            />
          ))}
        </div>
      </div>
      
      {/* Bottom progress indicator */}
      <div className="absolute bottom-2 left-2 right-2">
        <div className="flex justify-between items-center">
          <motion.div
            animate={{ width: ['20%', '80%', '20%'] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="h-1 bg-red-400 rounded"
          />
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
            className="w-2 h-2 bg-orange-400 rounded-full"
          />
        </div>
      </div>
      
      {/* Glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-orange-500/10 rounded-lg" />
    </div>
  );
}
