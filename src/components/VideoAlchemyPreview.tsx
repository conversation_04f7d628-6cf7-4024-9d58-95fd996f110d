'use client';

import { motion } from 'framer-motion';
import { Video, Wand2, FileText, Sparkles, Youtube, Globe, Languages } from 'lucide-react';

export default function VideoAlchemyPreview() {
  return (
    <div className="relative w-full h-full flex items-center justify-center overflow-hidden bg-gradient-to-br from-purple-950/20 via-black to-pink-950/20">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 left-1/4 w-48 h-48 bg-purple-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute bottom-1/4 right-1/4 w-56 h-56 bg-pink-600/30 rounded-full blur-3xl"
        />
      </div>

      {/* Main Animation */}
      <div className="relative z-10 flex flex-col items-center">
        {/* Video to Article Flow */}
        <div className="relative">
          {/* Video Icon */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="absolute -left-20 top-0"
          >
            <div className="relative">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute inset-0 bg-red-500/20 rounded-xl blur-xl"
              />
              <div className="relative bg-gradient-to-r from-red-600 to-pink-600 p-3 rounded-xl">
                <Youtube className="w-8 h-8 text-white" />
              </div>
            </div>
          </motion.div>

          {/* Magic Wand in Center */}
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.5, duration: 0.8, type: "spring" }}
            className="relative z-20"
          >
            <motion.div
              animate={{ rotate: [0, 15, -15, 0] }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              className="relative"
            >
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full blur-2xl opacity-60"
              />
              <div className="relative bg-gradient-to-r from-purple-600 to-pink-600 p-4 rounded-full shadow-2xl">
                <Wand2 className="w-10 h-10 text-white" />
              </div>
            </motion.div>
          </motion.div>

          {/* Article Icon */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
            className="absolute -right-20 top-0"
          >
            <div className="relative">
              <motion.div
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                className="absolute inset-0 bg-blue-500/20 rounded-xl blur-xl"
              />
              <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-xl">
                <FileText className="w-8 h-8 text-white" />
              </div>
            </div>
          </motion.div>
        </div>

        {/* Sparkles Animation */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="absolute inset-0 pointer-events-none"
        >
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ scale: 0, x: 0, y: 0 }}
              animate={{
                scale: [0, 1, 0],
                x: [0, (i % 2 ? 40 : -40) * (i + 1) / 2],
                y: [0, -20 - (i * 10)],
              }}
              transition={{
                duration: 2,
                delay: 1.2 + (i * 0.2),
                repeat: Infinity,
                repeatDelay: 3
              }}
              className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
            >
              <Sparkles className="w-4 h-4 text-purple-400" />
            </motion.div>
          ))}
        </motion.div>

        {/* Feature Icons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5 }}
          className="flex items-center space-x-4 mt-12"
        >
          <motion.div
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0 }}
            className="bg-white/10 backdrop-blur-sm rounded-lg p-2"
          >
            <Globe className="w-5 h-5 text-blue-400" />
          </motion.div>
          <motion.div
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0.3 }}
            className="bg-white/10 backdrop-blur-sm rounded-lg p-2"
          >
            <Languages className="w-5 h-5 text-purple-400" />
          </motion.div>
          <motion.div
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0.6 }}
            className="bg-white/10 backdrop-blur-sm rounded-lg p-2"
          >
            <Video className="w-5 h-5 text-pink-400" />
          </motion.div>
        </motion.div>

        {/* Text Label */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
          className="mt-6 text-center"
        >
          <p className="text-sm text-gray-400">Transform Videos → SEO Articles</p>
        </motion.div>
      </div>
    </div>
  );
}