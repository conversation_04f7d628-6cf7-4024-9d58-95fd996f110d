'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  FileText, 
  Mail, 
  Video, 
  Crown, 
  Clock, 
  Eye, 
  Trash2, 
  Download,
  Copy,
  ExternalLink,
  Filter,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'

interface ContentItem {
  id: string
  type: string
  title: string
  content: string
  preview: string
  wordCount?: number
  tone?: string
  status: string
  createdAt: string
  updatedAt: string
  metadata?: any
}

interface RecentContentProps {
  limit?: number
  showFilters?: boolean
}

const contentTypeConfig = {
  blog: {
    icon: FileText,
    label: 'Blog Post',
    color: 'from-pink-500 to-rose-500',
    bgColor: 'bg-pink-500/10',
    borderColor: 'border-pink-500/20'
  },
  email: {
    icon: Mail,
    label: 'Email',
    color: 'from-emerald-500 to-teal-500',
    bgColor: 'bg-emerald-500/10',
    borderColor: 'border-emerald-500/20'
  },
  youtube_script: {
    icon: Video,
    label: 'YouTube Script',
    color: 'from-red-500 to-orange-500',
    bgColor: 'bg-red-500/10',
    borderColor: 'border-red-500/20'
  },
  content_team: {
    icon: Crown,
    label: 'KaibanJS Team',
    color: 'from-violet-700 to-indigo-700',
    bgColor: 'bg-violet-700/10',
    borderColor: 'border-violet-700/20'
  }
}

export default function RecentContent({ limit = 6, showFilters = true }: RecentContentProps) {
  const [content, setContent] = useState<ContentItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedType, setSelectedType] = useState<string>('all')
  const [isRefreshing, setIsRefreshing] = useState(false)

  const fetchContent = async (type: string = 'all') => {
    try {
      setIsRefreshing(true)
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: '0'
      })
      
      if (type !== 'all') {
        params.append('type', type)
      }

      const response = await fetch(`/api/content?${params}`)
      if (response.ok) {
        const data = await response.json()
        setContent(data.content || [])
      }
    } catch (error) {
      console.error('Error fetching content:', error)
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }

  useEffect(() => {
    fetchContent(selectedType)
  }, [selectedType, limit])

  const handleTypeFilter = (type: string) => {
    setSelectedType(type)
  }

  const handleDelete = async (contentId: string) => {
    try {
      const response = await fetch(`/api/content?id=${contentId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setContent(prev => prev.filter(item => item.id !== contentId))
      }
    } catch (error) {
      console.error('Error deleting content:', error)
    }
  }

  const handleCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
    } catch (error) {
      console.error('Error copying content:', error)
    }
  }

  // Helper function to ensure articles are stored and get proper URLs
  const getArticleViewUrl = async (item: ContentItem): Promise<string> => {
    try {
      // First check if this article already has a stored URL by using its ID
      if (item.id) {
        // Check if the article exists in the clean URL system
        const checkResponse = await fetch(`/api/articles/${item.id}`)
        if (checkResponse.ok) {
          return `/article-view/${item.id}`
        }
      }

      // If not found, store the article using the proper API
      const response = await fetch('/api/articles/store', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: item.title,
          content: item.content,
          type: item.type,
          metadata: item.metadata,
        }),
      })

      const data = await response.json()
      if (response.ok && data.success && data.url) {
        return data.url
      }

      // Fallback to legacy URL if storing fails
      console.warn('Failed to store article, using fallback URL')
      return `/article-view?article=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}`
    } catch (error) {
      console.error('Error generating article URL:', error)
      // Fallback to legacy URL on error
      return `/article-view?article=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}`
    }
  }

  const handleViewClick = async (item: ContentItem, event: React.MouseEvent) => {
    event.preventDefault()
    
    try {
      const url = await getArticleViewUrl(item)
      window.location.href = url
    } catch (error) {
      console.error('Error handling view click:', error)
      // Fallback to direct navigation with legacy URL
      window.location.href = `/article-view?article=${encodeURIComponent(item.content)}&title=${encodeURIComponent(item.title)}`
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getContentConfig = (type: string) => {
    return contentTypeConfig[type as keyof typeof contentTypeConfig] || contentTypeConfig.blog
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white">Recent Content</h3>
          <div className="animate-spin w-5 h-5 border-2 border-violet-400 border-t-transparent rounded-full"></div>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-24 bg-white/5 rounded-xl"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-white">Recent Content</h3>
        <div className="flex items-center space-x-2">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => fetchContent(selectedType)}
            disabled={isRefreshing}
            className="p-2 bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 text-gray-400 ${isRefreshing ? 'animate-spin' : ''}`} />
          </motion.button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => handleTypeFilter('all')}
            className={`px-3 py-1.5 text-sm rounded-lg transition-colors ${
              selectedType === 'all'
                ? 'bg-violet-600 text-white'
                : 'bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white'
            }`}
          >
            All
          </button>
          {Object.entries(contentTypeConfig).map(([type, config]) => (
            <button
              key={type}
              onClick={() => handleTypeFilter(type)}
              className={`px-3 py-1.5 text-sm rounded-lg transition-colors flex items-center space-x-1 ${
                selectedType === type
                  ? 'bg-violet-600 text-white'
                  : 'bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white'
              }`}
            >
              <config.icon className="w-3 h-3" />
              <span>{config.label}</span>
            </button>
          ))}
        </div>
      )}

      {/* Content Grid */}
      <div className="space-y-4">
        <AnimatePresence>
          {content.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-12"
            >
              <FileText className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400">No content found</p>
              <p className="text-sm text-gray-500 mt-1">
                Start creating content to see it here
              </p>
            </motion.div>
          ) : (
            content.map((item, index) => {
              const config = getContentConfig(item.type)
              const IconComponent = config.icon

              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                  className={`group relative backdrop-blur-xl border rounded-xl p-4 transition-all duration-300 ${
                    item.type === 'content_team'
                      ? 'bg-white/10 border-white/20 hover:bg-white/15 hover:border-white/30 shadow-xl'
                      : `bg-white/5 ${config.borderColor} hover:bg-white/10`
                  }`}
                >
                  {/* Glass reflection effect for KaibanJS content */}
                  {item.type === 'content_team' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-xl" />
                  )}
                  
                  <div className="relative flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      {/* Icon */}
                      <div className={`p-2 rounded-lg border ${
                        item.type === 'content_team'
                          ? 'bg-violet-700/20 border-violet-700/30 backdrop-blur-sm'
                          : `${config.bgColor} ${config.borderColor}`
                      }`}>
                        <IconComponent className="w-4 h-4 text-white" />
                      </div>

                      {/* Content Info */}
                      <div className="flex-1 min-w-0">
                        <h4 className="text-white font-medium truncate">{item.title}</h4>
                        <p className="text-gray-400 text-sm mt-1 line-clamp-2">{item.preview}</p>
                        
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>{formatDate(item.createdAt)}</span>
                          </div>
                          {item.wordCount && (
                            <span>{item.wordCount.toLocaleString()} words</span>
                          )}
                          {item.tone && (
                            <span className="capitalize">{item.tone}</span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => handleCopy(item.content)}
                        className={`p-1.5 text-gray-400 hover:text-white rounded-lg transition-colors ${
                          item.type === 'content_team'
                            ? 'hover:bg-white/15 backdrop-blur-sm'
                            : 'hover:bg-white/10'
                        }`}
                        title="Copy content"
                      >
                        <Copy className="w-4 h-4" />
                      </motion.button>
                      
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={(e) => handleViewClick(item, e)}
                        className="p-1.5 text-gray-400 hover:text-white rounded-lg transition-colors hover:bg-white/10"
                        title="View content"
                      >
                        <Eye className="w-4 h-4" />
                      </motion.button>
                      
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => handleDelete(item.id)}
                        className="p-1.5 text-gray-400 hover:text-red-400 rounded-lg transition-colors hover:bg-red-500/10"
                        title="Delete content"
                      >
                        <Trash2 className="w-4 h-4" />
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              )
            })
          )}
        </AnimatePresence>
      </div>

      {/* View All Link */}
      {content.length >= limit && (
        <div className="text-center">
          <Link href="/content" className="text-violet-400 hover:text-violet-300 text-sm font-medium">
            View all content →
          </Link>
        </div>
      )}
    </div>
  )
} 