'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface QuotaData {
  quotaType: string
  used: number
  limit: number
  resetDate: string
  percentage: number
}

interface QuotaCardProps {
  quotaType: 'blog_posts' | 'emails' | 'social_media' | 'youtube_scripts'
  title: string
  icon: React.ReactNode
  color: string
}

export default function QuotaCard({ quotaType, title, icon, color }: QuotaCardProps) {
  const { data: session } = useSession()
  const [quota, setQuota] = useState<QuotaData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (session?.user?.id) {
      fetchQuota()
    }
  }, [session])

  const fetchQuota = async () => {
    try {
      const response = await fetch(`/api/quota?type=${quotaType}`)
      if (response.ok) {
        const data = await response.json()
        setQuota({
          quotaType,
          used: data.used,
          limit: data.limit,
          resetDate: data.resetDate,
          percentage: data.limit === -1 ? 0 : (data.used / data.limit) * 100
        })
      }
    } catch (error) {
      console.error('Error fetching quota:', error)
    } finally {
      setLoading(false)
    }
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-400'
    if (percentage >= 75) return 'text-yellow-400'
    return 'text-green-400'
  }

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  if (loading) {
    return (
      <div className="bg-gray-900/50 backdrop-blur-xl border border-gray-800 rounded-2xl p-6 animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gray-700 rounded-lg"></div>
            <div className="w-24 h-4 bg-gray-700 rounded"></div>
          </div>
        </div>
        <div className="w-full h-2 bg-gray-700 rounded-full mb-2"></div>
        <div className="w-16 h-3 bg-gray-700 rounded"></div>
      </div>
    )
  }

  if (!quota) {
    return (
      <div className="bg-gray-900/50 backdrop-blur-xl border border-gray-800 rounded-2xl p-6">
        <div className="flex items-center gap-3 mb-4">
          <div className={`p-2 rounded-lg ${color}`}>
            {icon}
          </div>
          <h3 className="text-white font-semibold">{title}</h3>
        </div>
        <p className="text-gray-400 text-sm">Unable to load quota data</p>
      </div>
    )
  }

  const isUnlimited = quota.limit === -1
  const resetDate = new Date(quota.resetDate).toLocaleDateString()

  return (
    <div className="bg-gray-900/50 backdrop-blur-xl border border-gray-800 rounded-2xl p-6 hover:border-gray-700 transition-all duration-200">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${color}`}>
            {icon}
          </div>
          <h3 className="text-white font-semibold">{title}</h3>
        </div>
        {!isUnlimited && (
          <span className={`text-sm font-medium ${getUsageColor(quota.percentage)}`}>
            {quota.used}/{quota.limit}
          </span>
        )}
      </div>

      {isUnlimited ? (
        <div className="text-center py-4">
          <div className="text-2xl font-bold text-green-400 mb-1">∞</div>
          <div className="text-sm text-gray-400">Unlimited</div>
        </div>
      ) : (
        <>
          {/* Progress Bar */}
          <div className="w-full bg-gray-700 rounded-full h-2 mb-3">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(quota.percentage)}`}
              style={{ width: `${Math.min(quota.percentage, 100)}%` }}
            ></div>
          </div>

          {/* Usage Stats */}
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-400">
              {quota.limit - quota.used} remaining
            </span>
            <span className="text-gray-500">
              Resets {resetDate}
            </span>
          </div>

          {/* Warning Messages */}
          {quota.percentage >= 90 && (
            <div className="mt-3 p-2 bg-red-900/30 border border-red-500/30 rounded-lg">
              <p className="text-red-400 text-xs">
                ⚠️ Quota almost exhausted! Consider upgrading your plan.
              </p>
            </div>
          )}
        </>
      )}
    </div>
  )
} 