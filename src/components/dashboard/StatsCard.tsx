import { motion } from 'framer-motion'
import { ArrowUpRight, ArrowDownRight } from 'lucide-react'

interface StatsCardProps {
  icon: any
  label: string
  value: string
  change: string
  trend: 'up' | 'down' | 'neutral'
  gradient?: string
}

export default function StatsCard({ 
  icon: Icon, 
  label, 
  value, 
  change, 
  trend,
  gradient = "from-blue-500/20 to-purple-600/20"
}: StatsCardProps) {
  return (
    <motion.div
      whileHover={{ y: -4, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className="glass-card p-6 group hover:shadow-2xl transition-all duration-300 relative overflow-hidden"
    >
      {/* Background gradient effect */}
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300`} />
      
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className={`w-14 h-14 rounded-xl bg-gradient-to-br ${
            trend === 'up' ? 'from-green-500/20 to-emerald-600/20' : 
            trend === 'down' ? 'from-red-500/20 to-rose-600/20' :
            'from-gray-500/20 to-gray-600/20'
          } flex items-center justify-center group-hover:scale-110 transition-transform`}>
            <Icon className={`w-7 h-7 ${
              trend === 'up' ? 'text-green-400' : 
              trend === 'down' ? 'text-red-400' :
              'text-gray-400'
            }`} />
          </div>
          
          {change && (
            <div className={`flex items-center text-sm font-medium ${
              trend === 'up' ? 'text-green-400' : 
              trend === 'down' ? 'text-red-400' :
              'text-gray-400'
            }`}>
              {trend === 'up' && <ArrowUpRight className="w-4 h-4" />}
              {trend === 'down' && <ArrowDownRight className="w-4 h-4" />}
              {change}
            </div>
          )}
        </div>
        
        <h3 className="text-3xl font-bold text-white mb-1 group-hover:scale-105 transition-transform">
          {value}
        </h3>
        <p className="text-gray-400 text-sm font-medium">{label}</p>
        
        {/* Animated line */}
        <div className="mt-4 h-1 bg-white/10 rounded-full overflow-hidden">
          <motion.div
            className={`h-full bg-gradient-to-r ${
              trend === 'up' ? 'from-green-400 to-emerald-400' : 
              trend === 'down' ? 'from-red-400 to-rose-400' :
              'from-gray-400 to-gray-400'
            }`}
            initial={{ width: '0%' }}
            animate={{ width: '65%' }}
            transition={{ duration: 1.5, ease: "easeOut" }}
          />
        </div>
      </div>
    </motion.div>
  )
} 