'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Highlight from '@tiptap/extension-highlight'
import Link from '@tiptap/extension-link'
import Placeholder from '@tiptap/extension-placeholder'
import CharacterCount from '@tiptap/extension-character-count'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import {
  Bold,
  Italic,
  Strikethrough,
  Code,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Link as LinkIcon,
  Highlighter as HighlightIcon,
  Table as TableIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  ExternalLink,
  X,
  Check,
  Globe,
  Sparkles
} from 'lucide-react'
import { useCallback, useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface RichTextEditorProps {
  content: string
  onChange: (content: string) => void
  placeholder?: string
  className?: string
}

interface LinkModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (url: string, title?: string) => void
  initialUrl?: string
  initialTitle?: string
}

const LinkModal = ({ isOpen, onClose, onSubmit, initialUrl = '', initialTitle = '' }: LinkModalProps) => {
  const [url, setUrl] = useState(initialUrl)
  const [title, setTitle] = useState(initialTitle)
  const [isLoading, setIsLoading] = useState(false)
  const [linkPreview, setLinkPreview] = useState<{ title: string; description: string; favicon: string } | null>(null)

  useEffect(() => {
    if (isOpen) {
      setUrl(initialUrl)
      setTitle(initialTitle)
      setLinkPreview(null)
    }
  }, [isOpen, initialUrl, initialTitle])

  const fetchLinkPreview = async (targetUrl: string) => {
    if (!targetUrl.startsWith('http')) return
    
    setIsLoading(true)
    try {
      // Simulate fetching link preview (in real app, you'd call an API)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock preview data
      const domain = new URL(targetUrl).hostname
      setLinkPreview({
        title: title || `Page from ${domain}`,
        description: `Content from ${domain}`,
        favicon: `https://www.google.com/s2/favicons?domain=${domain}&sz=32`
      })
    } catch (error) {
      console.error('Failed to fetch link preview:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (url) {
      onSubmit(url, title)
      onClose()
    }
  }

  const handleUrlChange = (newUrl: string) => {
    setUrl(newUrl)
    if (newUrl && newUrl.startsWith('http')) {
      fetchLinkPreview(newUrl)
    } else {
      setLinkPreview(null)
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            onClick={onClose}
          />
          
          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4"
          >
            <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border border-white/20 rounded-2xl shadow-2xl w-full max-w-md">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-white/10">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                    <LinkIcon className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">Add Link</h3>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    URL
                  </label>
                  <div className="relative">
                    <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="url"
                      value={url}
                      onChange={(e) => handleUrlChange(e.target.value)}
                      placeholder="https://example.com"
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                      autoFocus
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Title (optional)
                  </label>
                  <input
                    type="text"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Link title"
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  />
                </div>

                {/* Link Preview */}
                {isLoading && (
                  <div className="p-4 bg-white/5 rounded-lg border border-white/10">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded animate-pulse"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-white/20 rounded animate-pulse"></div>
                        <div className="h-3 bg-white/10 rounded w-2/3 animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                )}

                {linkPreview && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="p-4 bg-gradient-to-r from-blue-500/10 to-purple-600/10 rounded-lg border border-blue-500/20"
                  >
                    <div className="flex items-start space-x-3">
                      <img
                        src={linkPreview.favicon}
                        alt="Favicon"
                        className="w-8 h-8 rounded"
                        onError={(e) => {
                          e.currentTarget.src = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><rect width="32" height="32" fill="%23374151"/><text x="16" y="20" text-anchor="middle" fill="white" font-size="14">🌐</text></svg>'
                        }}
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="text-white font-medium truncate">{linkPreview.title}</h4>
                        <p className="text-gray-400 text-sm truncate">{linkPreview.description}</p>
                      </div>
                    </div>
                  </motion.div>
                )}

                {/* Actions */}
                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={onClose}
                    className="flex-1 px-4 py-3 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <motion.button
                    type="submit"
                    disabled={!url}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    className="flex-1 px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:from-blue-700 hover:to-purple-700 transition-all flex items-center justify-center space-x-2"
                  >
                    <Check className="w-4 h-4" />
                    <span>Add Link</span>
                  </motion.button>
                </div>
              </form>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

const MenuButton = ({ 
  onClick, 
  isActive, 
  disabled, 
  children, 
  title,
  gradient = false
}: {
  onClick: () => void
  isActive?: boolean
  disabled?: boolean
  children: React.ReactNode
  title: string
  gradient?: boolean
}) => (
  <motion.button
    onClick={onClick}
    disabled={disabled}
    title={title}
    whileHover={{ scale: 1.05 }}
    whileTap={{ scale: 0.95 }}
    className={`p-2.5 rounded-lg transition-all duration-200 relative overflow-hidden ${
      isActive 
        ? gradient 
          ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25' 
          : 'bg-blue-600 text-white shadow-lg' 
        : 'text-gray-300 hover:text-white hover:bg-white/10 hover:scale-105'
    } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
  >
    {children}
    {isActive && (
      <motion.div
        layoutId="activeIndicator"
        className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 -z-10 rounded-lg"
        initial={false}
      />
    )}
  </motion.button>
)

export default function RichTextEditor({ 
  content, 
  onChange, 
  placeholder = "Start writing...",
  className = ""
}: RichTextEditorProps) {
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false)
  const [wordCount, setWordCount] = useState(0)

  const editor = useEditor({
    extensions: [
      StarterKit,
      Highlight.configure({ 
        multicolor: true,
        HTMLAttributes: {
          class: 'bg-gradient-to-r from-yellow-400/30 to-orange-500/30 px-1 rounded',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500 hover:from-blue-300 hover:to-purple-400 underline decoration-blue-400/50 hover:decoration-blue-300/70 transition-all duration-200 cursor-pointer',
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      CharacterCount.configure({
        limit: 10000,
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML())
      // Update word count
      const text = editor.getText()
      setWordCount(text.split(/\s+/).filter(word => word.length > 0).length)
    },
    editorProps: {
      attributes: {
        class: 'prose prose-invert max-w-none p-6 focus:outline-none min-h-[400px] text-gray-100',
      },
    },
  })

  const setLink = useCallback(() => {
    const previousUrl = editor?.getAttributes('link').href
    
    setIsLinkModalOpen(true)
  }, [editor])

  const handleLinkSubmit = useCallback((url: string, title?: string) => {
    if (url === '') {
      editor?.chain().focus().extendMarkRange('link').unsetLink().run()
      return
    }

    editor?.chain().focus().extendMarkRange('link').setLink({ 
      href: url
    }).run()
  }, [editor])

  const addTable = useCallback(() => {
    editor?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()
  }, [editor])

  if (!editor) {
    return (
      <div className="glass-card p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded w-1/4 mb-4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-white/20 rounded"></div>
            <div className="h-4 bg-white/20 rounded w-5/6"></div>
            <div className="h-4 bg-white/20 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className={`glass-card ${className} bg-gradient-to-br from-slate-900/90 via-slate-800/90 to-slate-900/90 backdrop-blur-xl border border-white/20 shadow-2xl`}>
        {/* Enhanced Toolbar */}
        <div className="border-b border-white/10 p-4 bg-gradient-to-r from-slate-800/50 to-slate-900/50">
          <div className="flex flex-wrap items-center gap-2">
            {/* Text Formatting */}
            <div className="flex items-center gap-1 border-r border-white/10 pr-3 mr-3">
              <MenuButton
                onClick={() => editor.chain().focus().toggleBold().run()}
                isActive={editor.isActive('bold')}
                title="Bold (Ctrl+B)"
                gradient
              >
                <Bold className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={() => editor.chain().focus().toggleItalic().run()}
                isActive={editor.isActive('italic')}
                title="Italic (Ctrl+I)"
              >
                <Italic className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={() => editor.chain().focus().toggleStrike().run()}
                isActive={editor.isActive('strike')}
                title="Strikethrough"
              >
                <Strikethrough className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={() => editor.chain().focus().toggleCode().run()}
                isActive={editor.isActive('code')}
                title="Inline Code"
              >
                <Code className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={() => editor.chain().focus().toggleHighlight().run()}
                isActive={editor.isActive('highlight')}
                title="Highlight"
                gradient
              >
                <HighlightIcon className="w-4 h-4" />
              </MenuButton>
            </div>

            {/* Headings */}
            <div className="flex items-center gap-1 border-r border-white/10 pr-3 mr-3">
              <MenuButton
                onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                isActive={editor.isActive('heading', { level: 1 })}
                title="Heading 1"
                gradient
              >
                <Heading1 className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                isActive={editor.isActive('heading', { level: 2 })}
                title="Heading 2"
                gradient
              >
                <Heading2 className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                isActive={editor.isActive('heading', { level: 3 })}
                title="Heading 3"
                gradient
              >
                <Heading3 className="w-4 h-4" />
              </MenuButton>
            </div>

            {/* Lists */}
            <div className="flex items-center gap-1 border-r border-white/10 pr-3 mr-3">
              <MenuButton
                onClick={() => editor.chain().focus().toggleBulletList().run()}
                isActive={editor.isActive('bulletList')}
                title="Bullet List"
              >
                <List className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={() => editor.chain().focus().toggleOrderedList().run()}
                isActive={editor.isActive('orderedList')}
                title="Numbered List"
              >
                <ListOrdered className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={() => editor.chain().focus().toggleBlockquote().run()}
                isActive={editor.isActive('blockquote')}
                title="Quote"
              >
                <Quote className="w-4 h-4" />
              </MenuButton>
            </div>

            {/* Insert */}
            <div className="flex items-center gap-1 border-r border-white/10 pr-3 mr-3">
              <MenuButton
                onClick={setLink}
                isActive={editor.isActive('link')}
                title="Add Link"
                gradient
              >
                <LinkIcon className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={addTable}
                title="Insert Table"
              >
                <TableIcon className="w-4 h-4" />
              </MenuButton>
            </div>

            {/* History */}
            <div className="flex items-center gap-1">
              <MenuButton
                onClick={() => editor.chain().focus().undo().run()}
                disabled={!editor.can().chain().focus().undo().run()}
                title="Undo (Ctrl+Z)"
              >
                <Undo className="w-4 h-4" />
              </MenuButton>
              
              <MenuButton
                onClick={() => editor.chain().focus().redo().run()}
                disabled={!editor.can().chain().focus().redo().run()}
                title="Redo (Ctrl+Y)"
              >
                <Redo className="w-4 h-4" />
              </MenuButton>
            </div>

            {/* AI Assistant Button */}
            <div className="ml-auto">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg font-medium shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-200 flex items-center space-x-2"
                title="AI Writing Assistant"
              >
                <Sparkles className="w-4 h-4" />
                <span className="hidden sm:block">AI Assist</span>
              </motion.button>
            </div>
          </div>
        </div>

        {/* Editor */}
        <div className="relative">
          <EditorContent editor={editor} />
          
          {/* Enhanced Status Bar */}
          <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-r from-slate-900/80 to-slate-800/80 backdrop-blur-sm border-t border-white/10">
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center space-x-4 text-gray-400">
                <span className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>{wordCount} words</span>
                </span>
                <span>{editor.storage.characterCount.characters()}/10,000 characters</span>
                {editor.isActive('link') && (
                  <motion.span
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex items-center space-x-1 text-blue-400"
                  >
                    <ExternalLink className="w-3 h-3" />
                    <span>Link selected</span>
                  </motion.span>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="w-px h-4 bg-white/20"></div>
                <span className="text-gray-400">Ready</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Link Modal */}
      <LinkModal
        isOpen={isLinkModalOpen}
        onClose={() => setIsLinkModalOpen(false)}
        onSubmit={handleLinkSubmit}
        initialUrl={editor?.getAttributes('link').href || ''}
      />
    </>
  )
} 