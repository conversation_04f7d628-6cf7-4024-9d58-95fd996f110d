'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'

type Theme = 'dark' | 'light' | 'auto'

interface ThemeContextType {
  theme: Theme
  setTheme: (theme: Theme) => void
  resolvedTheme: 'dark' | 'light'
  compactMode: boolean
  setCompactMode: (compact: boolean) => void
  accentColor: string
  setAccentColor: (color: string) => void
  animationsEnabled: boolean
  setAnimationsEnabled: (enabled: boolean) => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

interface ThemeProviderProps {
  children: ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>('dark')
  const [resolvedTheme, setResolvedTheme] = useState<'dark' | 'light'>('dark')
  const [compactMode, setCompactMode] = useState(false)
  const [accentColor, setAccentColor] = useState('blue')
  const [animationsEnabled, setAnimationsEnabled] = useState(true)

  useEffect(() => {
    // Load all settings from localStorage
    const savedTheme = localStorage.getItem('theme') as Theme
    const savedCompactMode = localStorage.getItem('compactMode') === 'true'
    const savedAccentColor = localStorage.getItem('accentColor') || 'blue'
    const savedAnimationsEnabled = localStorage.getItem('animationsEnabled') !== 'false'
    
    if (savedTheme && ['dark', 'light', 'auto'].includes(savedTheme)) {
      setTheme(savedTheme)
    }
    setCompactMode(savedCompactMode)
    setAccentColor(savedAccentColor)
    setAnimationsEnabled(savedAnimationsEnabled)
  }, [])

  useEffect(() => {
    const updateResolvedTheme = () => {
      if (theme === 'auto') {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        setResolvedTheme(mediaQuery.matches ? 'dark' : 'light')
      } else {
        setResolvedTheme(theme)
      }
    }

    updateResolvedTheme()

    if (theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleChange = () => updateResolvedTheme()
      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
  }, [theme])

  useEffect(() => {
    // Apply theme and other settings to document
    const root = document.documentElement
    if (resolvedTheme === 'dark') {
      root.classList.add('dark')
      root.classList.remove('light')
    } else {
      root.classList.add('light')
      root.classList.remove('dark')
    }
    
    // Apply compact mode
    if (compactMode) {
      root.classList.add('compact')
    } else {
      root.classList.remove('compact')
    }
    
    // Save all settings to localStorage
    localStorage.setItem('theme', theme)
    localStorage.setItem('compactMode', compactMode.toString())
    localStorage.setItem('accentColor', accentColor)
    localStorage.setItem('animationsEnabled', animationsEnabled.toString())
  }, [theme, resolvedTheme, compactMode, accentColor, animationsEnabled])

  return (
    <ThemeContext.Provider value={{ 
      theme, 
      setTheme, 
      resolvedTheme, 
      compactMode, 
      setCompactMode, 
      accentColor, 
      setAccentColor, 
      animationsEnabled, 
      setAnimationsEnabled 
    }}>
      {children}
    </ThemeContext.Provider>
  )
}