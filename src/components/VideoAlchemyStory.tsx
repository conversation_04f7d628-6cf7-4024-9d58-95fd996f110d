'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Zap, 
  Eye, 
  Search, 
  PenTool, 
  CheckCircle, 
  Trophy,
  Video,
  Brain,
  Globe,
  FileText,
  Wand2
} from 'lucide-react';

interface StoryData {
  stage: string;
  title: string;
  description: string;
  animation: string;
  duration: number;
}

interface VideoAlchemyStoryProps {
  storyData: StoryData;
  progress: number;
  message: string;
  isActive: boolean;
}

const GeometricShape = ({ type, className, delay = 0 }: { type: string; className: string; delay?: number }) => {
  const shapes = {
    circle: (
      <motion.div
        initial={{ scale: 0, rotate: 0 }}
        animate={{ scale: 1, rotate: 360 }}
        transition={{ 
          duration: 2, 
          delay,
          repeat: Infinity,
          repeatType: 'loop',
          ease: 'easeInOut'
        }}
        className={`rounded-full ${className}`}
      />
    ),
    square: (
      <motion.div
        initial={{ scale: 0, rotate: 0 }}
        animate={{ scale: 1, rotate: 45 }}
        transition={{ 
          duration: 2, 
          delay,
          repeat: Infinity,
          repeatType: 'reverse',
          ease: 'easeInOut'
        }}
        className={`${className}`}
      />
    ),
    triangle: (
      <motion.div
        initial={{ scale: 0, rotate: 0 }}
        animate={{ scale: 1, rotate: 120 }}
        transition={{ 
          duration: 3, 
          delay,
          repeat: Infinity,
          repeatType: 'loop',
          ease: 'easeInOut'
        }}
        className={`${className}`}
        style={{
          width: 0,
          height: 0,
          borderLeft: '20px solid transparent',
          borderRight: '20px solid transparent',
          borderBottom: '35px solid currentColor'
        }}
      />
    ),
    hexagon: (
      <motion.div
        initial={{ scale: 0, rotate: 0 }}
        animate={{ scale: 1, rotate: 180 }}
        transition={{ 
          duration: 4, 
          delay,
          repeat: Infinity,
          repeatType: 'loop',
          ease: 'easeInOut'
        }}
        className={`${className}`}
        style={{
          width: '40px',
          height: '40px',
          clipPath: 'polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%)'
        }}
      />
    )
  };

  return shapes[type as keyof typeof shapes] || shapes.circle;
};

const LaboratorySetup = () => (
  <div className="relative w-full h-full flex items-center justify-center">
    {/* Background particles */}
    <div className="absolute inset-0 overflow-hidden">
      {[...Array(20)].map((_, i) => (
        <GeometricShape
          key={i}
          type={['circle', 'square', 'triangle'][i % 3]}
          className={`absolute w-2 h-2 bg-purple-400/20 ${
            i % 4 === 0 ? 'top-1/4 left-1/4' :
            i % 4 === 1 ? 'top-1/3 right-1/4' :
            i % 4 === 2 ? 'bottom-1/4 left-1/3' :
            'bottom-1/3 right-1/3'
          }`}
          delay={i * 0.2}
        />
      ))}
    </div>

    {/* Main animation */}
    <div className="relative z-10 text-center">
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 1, ease: 'easeOut' }}
        className="mb-8"
      >
        <div className="relative">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
            className="w-32 h-32 border-4 border-purple-400/30 rounded-full mx-auto mb-4"
          >
            <div className="w-full h-full border-2 border-pink-400/30 rounded-full animate-pulse" />
          </motion.div>
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="absolute inset-0 flex items-center justify-center"
          >
            <Sparkles className="w-12 h-12 text-purple-400" />
          </motion.div>
        </div>
      </motion.div>

      <motion.h3
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.8 }}
        className="text-2xl font-bold text-white mb-2"
      >
        The Journey Begins
      </motion.h3>
      <motion.p
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7, duration: 0.8 }}
        className="text-gray-300"
      >
        Our magical Video Alchemy laboratory is warming up...
      </motion.p>
    </div>
  </div>
);

const VideoExtraction = () => (
  <div className="relative w-full h-full flex items-center justify-center">
    {/* Video symbols floating */}
    <div className="absolute inset-0">
      {[...Array(12)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ x: Math.random() * 400, y: Math.random() * 300, opacity: 0 }}
          animate={{
            x: Math.random() * 400,
            y: Math.random() * 300,
            opacity: [0, 1, 0],
          }}
          transition={{
            duration: 3,
            delay: i * 0.3,
            repeat: Infinity,
            repeatType: 'loop'
          }}
          className="absolute"
        >
          <Video className="w-6 h-6 text-red-400/60" />
        </motion.div>
      ))}
    </div>

    {/* Main extraction animation */}
    <div className="relative z-10 text-center">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 1 }}
        className="mb-8"
      >
        <div className="relative">
          {/* Extraction beams */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 120, opacity: [0, 1, 0] }}
              transition={{
                duration: 2,
                delay: i * 0.3,
                repeat: Infinity,
                repeatType: 'loop'
              }}
              className="absolute bg-gradient-to-t from-purple-400/50 to-transparent w-1 left-1/2 transform -translate-x-1/2"
              style={{
                transform: `translateX(-50%) rotate(${i * 60}deg)`,
                transformOrigin: 'bottom'
              }}
            />
          ))}
          
          <motion.div
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-24 h-24 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg mx-auto flex items-center justify-center"
          >
            <Video className="w-12 h-12 text-white" />
          </motion.div>
        </div>
      </motion.div>

      <motion.h3
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.8 }}
        className="text-2xl font-bold text-white mb-2"
      >
        Extracting Video Essence
      </motion.h3>
      <motion.p
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7, duration: 0.8 }}
        className="text-gray-300"
      >
        Magical extractors are capturing the essence from your videos...
      </motion.p>
    </div>
  </div>
);

const AIAnalysis = () => (
  <div className="relative w-full h-full flex items-center justify-center">
    {/* Neural network visualization */}
    <div className="absolute inset-0">
      <svg className="w-full h-full opacity-20" viewBox="0 0 400 300">
        {/* Neural network connections */}
        {[...Array(15)].map((_, i) => (
          <motion.line
            key={i}
            x1={Math.random() * 400}
            y1={Math.random() * 300}
            x2={Math.random() * 400}
            y2={Math.random() * 300}
            stroke="url(#gradient)"
            strokeWidth="2"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: 2,
              delay: i * 0.1,
              repeat: Infinity,
              repeatType: 'loop'
            }}
          />
        ))}
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#8B5CF6" />
            <stop offset="100%" stopColor="#EC4899" />
          </linearGradient>
        </defs>
      </svg>
    </div>

    {/* Main AI brain */}
    <div className="relative z-10 text-center">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 1 }}
        className="mb-8"
      >
        <div className="relative">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 10, repeat: Infinity, ease: 'linear' }}
            className="w-32 h-32 border-4 border-blue-400/30 rounded-full mx-auto"
          >
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-full h-full border-2 border-purple-400/50 rounded-full flex items-center justify-center"
            >
              <Brain className="w-16 h-16 text-blue-400" />
            </motion.div>
          </motion.div>
          
          {/* Thinking pulses */}
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ scale: 0, opacity: 1 }}
              animate={{ scale: 2, opacity: 0 }}
              transition={{
                duration: 2,
                delay: i * 0.5,
                repeat: Infinity,
                repeatType: 'loop'
              }}
              className="absolute inset-0 border-2 border-purple-400/30 rounded-full"
            />
          ))}
        </div>
      </motion.div>

      <motion.h3
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.8 }}
        className="text-2xl font-bold text-white mb-2"
      >
        The AI Oracle Awakens
      </motion.h3>
      <motion.p
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7, duration: 0.8 }}
        className="text-gray-300"
      >
        Our AI oracle is analyzing your content and discovering hidden knowledge gaps...
      </motion.p>
    </div>
  </div>
);

const ResearchHunt = () => (
  <div className="relative w-full h-full flex items-center justify-center">
    {/* Research elements flying around */}
    <div className="absolute inset-0">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ x: 200, y: 150, scale: 0 }}
          animate={{
            x: [200, Math.random() * 400, 200],
            y: [150, Math.random() * 300, 150],
            scale: [0, 1, 0]
          }}
          transition={{
            duration: 4,
            delay: i * 0.5,
            repeat: Infinity,
            repeatType: 'loop'
          }}
          className="absolute"
        >
          <Search className="w-8 h-8 text-green-400/70" />
        </motion.div>
      ))}
    </div>

    {/* Main research hub */}
    <div className="relative z-10 text-center">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 1 }}
        className="mb-8"
      >
        <div className="relative">
          <motion.div
            animate={{ rotate: -360 }}
            transition={{ duration: 15, repeat: Infinity, ease: 'linear' }}
            className="w-32 h-32 border-4 border-green-400/30 rounded-full mx-auto"
          >
            <motion.div
              animate={{ scale: [1, 1.3, 1] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="w-full h-full border-2 border-emerald-400/50 rounded-full flex items-center justify-center"
            >
              <Globe className="w-16 h-16 text-green-400" />
            </motion.div>
          </motion.div>
          
          {/* Search radar */}
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 3, repeat: Infinity, ease: 'linear' }}
            className="absolute inset-0 flex items-center justify-center"
          >
            <div className="w-24 h-1 bg-gradient-to-r from-transparent via-green-400 to-transparent" />
          </motion.div>
        </div>
      </motion.div>

      <motion.h3
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.8 }}
        className="text-2xl font-bold text-white mb-2"
      >
        Knowledge Hunters Dispatched
      </motion.h3>
      <motion.p
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7, duration: 0.8 }}
        className="text-gray-300"
      >
        Digital knowledge hunters are scouring the web for missing information...
      </motion.p>
    </div>
  </div>
);

const ArticleCreation = () => (
  <div className="relative w-full h-full flex items-center justify-center">
    {/* Golden threads weaving */}
    <div className="absolute inset-0">
      <svg className="w-full h-full opacity-30" viewBox="0 0 400 300">
        {[...Array(10)].map((_, i) => (
          <motion.path
            key={i}
            d={`M ${Math.random() * 400} ${Math.random() * 300} Q ${Math.random() * 400} ${Math.random() * 300} ${Math.random() * 400} ${Math.random() * 300}`}
            stroke="url(#goldGradient)"
            strokeWidth="3"
            fill="none"
            initial={{ pathLength: 0 }}
            animate={{ pathLength: 1 }}
            transition={{
              duration: 3,
              delay: i * 0.3,
              repeat: Infinity,
              repeatType: 'loop'
            }}
          />
        ))}
        <defs>
          <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#F59E0B" />
            <stop offset="100%" stopColor="#EAB308" />
          </linearGradient>
        </defs>
      </svg>
    </div>

    {/* Main creation animation */}
    <div className="relative z-10 text-center">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 1 }}
        className="mb-8"
      >
        <div className="relative">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
            className="w-32 h-32 border-4 border-yellow-400/30 rounded-full mx-auto"
          >
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-full h-full border-2 border-orange-400/50 rounded-full flex items-center justify-center"
            >
              <PenTool className="w-16 h-16 text-yellow-400" />
            </motion.div>
          </motion.div>
          
          {/* Writing sparkles */}
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ scale: 0, opacity: 0 }}
              animate={{
                scale: [0, 1, 0],
                opacity: [0, 1, 0],
                x: Math.sin(i * 60 * Math.PI / 180) * 60,
                y: Math.cos(i * 60 * Math.PI / 180) * 60
              }}
              transition={{
                duration: 2,
                delay: i * 0.3,
                repeat: Infinity,
                repeatType: 'loop'
              }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            >
              <Sparkles className="w-4 h-4 text-yellow-400" />
            </motion.div>
          ))}
        </div>
      </motion.div>

      <motion.h3
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.8 }}
        className="text-2xl font-bold text-white mb-2"
      >
        The Grand Synthesis
      </motion.h3>
      <motion.p
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7, duration: 0.8 }}
        className="text-gray-300"
      >
        Master writers are weaving your perfect article with golden threads of wisdom...
      </motion.p>
    </div>
  </div>
);

const FinalPolish = () => (
  <div className="relative w-full h-full flex items-center justify-center">
    {/* Polishing effects */}
    <div className="absolute inset-0">
      {[...Array(12)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: [0, 1, 0],
            opacity: [0, 1, 0],
            rotate: [0, 360]
          }}
          transition={{
            duration: 1.5,
            delay: i * 0.1,
            repeat: Infinity,
            repeatType: 'loop'
          }}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`
          }}
        >
          <div className="w-2 h-2 bg-cyan-400 rounded-full" />
        </motion.div>
      ))}
    </div>

    {/* Main polish animation */}
    <div className="relative z-10 text-center">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 1 }}
        className="mb-8"
      >
        <div className="relative">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 5, repeat: Infinity, ease: 'linear' }}
            className="w-32 h-32 border-4 border-cyan-400/30 rounded-full mx-auto"
          >
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
              className="w-full h-full border-2 border-teal-400/50 rounded-full flex items-center justify-center"
            >
              <CheckCircle className="w-16 h-16 text-cyan-400" />
            </motion.div>
          </motion.div>
          
          {/* Polish shine */}
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            className="absolute inset-0 flex items-center justify-center"
          >
            <div className="w-20 h-1 bg-gradient-to-r from-transparent via-cyan-400 to-transparent" />
          </motion.div>
        </div>
      </motion.div>

      <motion.h3
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.8 }}
        className="text-2xl font-bold text-white mb-2"
      >
        The Final Polish
      </motion.h3>
      <motion.p
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7, duration: 0.8 }}
        className="text-gray-300"
      >
        Quality guardians are adding the final touches to your masterpiece...
      </motion.p>
    </div>
  </div>
);

const CompletionCelebration = () => (
  <div className="relative w-full h-full flex items-center justify-center">
    {/* Celebration confetti */}
    <div className="absolute inset-0">
      {[...Array(20)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ y: -50, opacity: 0 }}
          animate={{
            y: 350,
            opacity: [0, 1, 0],
            rotate: [0, 360]
          }}
          transition={{
            duration: 3,
            delay: i * 0.1,
            repeat: Infinity,
            repeatType: 'loop'
          }}
          className="absolute"
          style={{
            left: `${Math.random() * 100}%`,
          }}
        >
          <GeometricShape
            type={['circle', 'square', 'triangle', 'hexagon'][i % 4]}
            className="w-3 h-3 text-pink-400"
          />
        </motion.div>
      ))}
    </div>

    {/* Main celebration */}
    <div className="relative z-10 text-center">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 1, type: 'spring' }}
        className="mb-8"
      >
        <div className="relative">
          <motion.div
            animate={{ scale: [1, 1.3, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-32 h-32 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full mx-auto flex items-center justify-center"
          >
            <Trophy className="w-16 h-16 text-white" />
          </motion.div>
          
          {/* Victory rings */}
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ scale: 0, opacity: 1 }}
              animate={{ scale: 3, opacity: 0 }}
              transition={{
                duration: 2,
                delay: i * 0.7,
                repeat: Infinity,
                repeatType: 'loop'
              }}
              className="absolute inset-0 border-4 border-pink-400/30 rounded-full"
            />
          ))}
        </div>
      </motion.div>

      <motion.h3
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.8 }}
        className="text-2xl font-bold text-white mb-2"
      >
        The Magic is Complete!
      </motion.h3>
      <motion.p
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.7, duration: 0.8 }}
        className="text-gray-300"
      >
        Your superior article is ready to captivate readers worldwide!
      </motion.p>
    </div>
  </div>
);

const StoryAnimations = {
  'laboratory-setup': LaboratorySetup,
  'video-extraction': VideoExtraction,
  'ai-analysis': AIAnalysis,
  'research-hunt': ResearchHunt,
  'article-creation': ArticleCreation,
  'final-polish': FinalPolish,
  'completion-celebration': CompletionCelebration,
};

export default function VideoAlchemyStory({ storyData, progress, message, isActive }: VideoAlchemyStoryProps) {
  const [currentAnimation, setCurrentAnimation] = useState<string>('laboratory-setup');

  useEffect(() => {
    if (storyData?.animation) {
      setCurrentAnimation(storyData.animation);
    }
  }, [storyData]);

  const AnimationComponent = StoryAnimations[currentAnimation as keyof typeof StoryAnimations] || LaboratorySetup;

  return (
    <div className="relative w-full h-96 bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900 rounded-2xl overflow-hidden border border-white/10">
      {/* Animated background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-900/10 via-transparent to-pink-900/10" />
      
      {/* Main animation area */}
      <div className="relative z-10 w-full h-full p-8">
        <AnimatePresence mode="wait">
          {isActive && (
            <motion.div
              key={currentAnimation}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.1 }}
              transition={{ duration: 0.5 }}
              className="w-full h-full"
            >
              <AnimationComponent />
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Progress bar */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gray-800">
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5 }}
          className="h-full bg-gradient-to-r from-purple-500 to-pink-500"
        />
      </div>

      {/* Status message */}
      <div className="absolute bottom-4 left-4 right-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-black/50 backdrop-blur-sm rounded-lg p-3 border border-white/10"
        >
          <p className="text-sm text-gray-300 text-center">{message}</p>
        </motion.div>
      </div>
    </div>
  );
} 