'use client';

import { motion } from 'framer-motion';
import { 
  FileText, 
  Video, 
  Clock, 
  Youtube, 
  Sparkles, 
  ArrowRight,
  RefreshC<PERSON>,
  Play,
  Edit3
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface VideoAnalysis {
  videoTitle: string;
  videoUrl: string;
  analysisType: 'single' | 'multiple';
  topics: {
    id: string;
    title: string;
    summary: string[];
    timestamp?: string;
  }[];
}

interface MegatronAnalysisCardsProps {
  analysis: VideoAnalysis;
  onGenerateArticle: (topic: VideoAnalysis['topics'][0]) => void;
  onGenerateScript: (topic: VideoAnalysis['topics'][0]) => void;
  onReset: () => void;
}

export default function MegatronAnalysisCards({
  analysis,
  onGenerateArticle,
  onGenerateScript,
  onReset
}: MegatronAnalysisCardsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-8"
    >
      {/* Video Info Header */}
      <div className="bg-white/5 backdrop-blur-md border border-white/20 rounded-2xl p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
              <Youtube className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-bold text-white mb-2">{analysis.videoTitle}</h2>
              <div className="flex items-center space-x-4 text-sm text-gray-400">
                <div className="flex items-center space-x-1">
                  <Play className="w-4 h-4" />
                  <span>YouTube Video</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Sparkles className="w-4 h-4" />
                  <span>{analysis.analysisType === 'single' ? 'Single Topic' : 'Multiple Topics'}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <FileText className="w-4 h-4" />
                  <span>{analysis.topics.length} Topic{analysis.topics.length > 1 ? 's' : ''}</span>
                </div>
              </div>
            </div>
          </div>
          <Button
            onClick={onReset}
            variant="outline"
            size="sm"
            className="border-white/20 text-gray-300 hover:text-white hover:bg-white/10"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            New Analysis
          </Button>
        </div>
      </div>

      {/* Topic Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {analysis.topics.map((topic, index) => (
          <motion.div
            key={topic.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white/5 backdrop-blur-md border border-white/20 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 group"
          >
            {/* Topic Header */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-3">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-white text-sm font-bold">
                  {index + 1}
                </div>
                {topic.timestamp && (
                  <div className="flex items-center space-x-1 text-xs text-gray-400">
                    <Clock className="w-3 h-3" />
                    <span>{topic.timestamp}</span>
                  </div>
                )}
              </div>
              <h3 className="text-lg font-bold text-white mb-2 group-hover:text-orange-300 transition-colors">
                {topic.title}
              </h3>
            </div>

            {/* Topic Summary */}
            <div className="mb-6">
              <h4 className="text-sm font-semibold text-gray-300 mb-3">Key Points Covered:</h4>
              <ul className="space-y-2">
                {topic.summary.map((point, pointIndex) => (
                  <li key={pointIndex} className="flex items-start space-x-2 text-sm text-gray-400">
                    <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0" />
                    <span>{point}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                onClick={() => onGenerateArticle(topic)}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-2.5 px-4 rounded-lg transition-all duration-200 group/btn"
              >
                <div className="flex items-center justify-center space-x-2">
                  <FileText className="w-4 h-4" />
                  <span>Generate Article</span>
                  <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                </div>
              </Button>
              
              <Button
                onClick={() => onGenerateScript(topic)}
                variant="outline"
                className="w-full border-white/20 text-gray-300 hover:text-white hover:bg-white/10 font-semibold py-2.5 px-4 rounded-lg transition-all duration-200 group/btn"
              >
                <div className="flex items-center justify-center space-x-2">
                  <Edit3 className="w-4 h-4" />
                  <span>Generate Script</span>
                  <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                </div>
              </Button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Analysis Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-gradient-to-r from-orange-500/10 to-red-500/10 backdrop-blur-md border border-orange-500/20 rounded-2xl p-6"
      >
        <div className="text-center">
          <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-6 h-6 text-white" />
          </div>
          <h3 className="text-lg font-bold text-white mb-2">Analysis Complete</h3>
          <p className="text-gray-300 text-sm">
            {analysis.analysisType === 'single' 
              ? `This video focuses on a single topic: "${analysis.topics[0]?.title}". Perfect for creating focused, in-depth content.`
              : `This video covers ${analysis.topics.length} distinct topics. Each can be expanded into comprehensive articles or scripts.`
            }
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
}
