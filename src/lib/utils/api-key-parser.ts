/**
 * Utility functions for parsing API keys from environment variables
 */

export interface ApiKeyConfig {
  keys: string[];
  isConfigured: boolean;
  count: number;
}

/**
 * Parse comma-separated API keys from environment variables
 * @param envVarName - Primary environment variable name
 * @param fallbackEnvVarName - Fallback environment variable name
 * @returns Parsed API key configuration
 */
export function parseApiKeys(
  envVarName: string, 
  fallbackEnvVarName?: string
): ApiKeyConfig {
  const keysString = process.env[envVarName] || 
                    (fallbackEnvVarName ? process.env[fallbackEnvVarName] : '') || 
                    '';
  
  const keys = keysString
    .split(',')
    .map(key => key.trim())
    .filter(key => key.length > 0 && !key.includes('your_') && key !== 'undefined');
  
  const isConfigured = keys.length > 0;
  
  return {
    keys,
    isConfigured,
    count: keys.length
  };
}

/**
 * Parse Tavily API keys specifically
 * @returns Tavily API key configuration
 */
export function parseTavilyApiKeys(): ApiKeyConfig {
  // First try environment variables
  const envConfig = parseApiKeys('TAVILY_API_KEYS', 'NEXT_PUBLIC_TAVILY_API_KEYS');
  
  // Hardcoded fallback Tavily API keys for rotation system
  const fallbackKeys = [
    '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',  // Active
    '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',  // Active  
    '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',  // Backup
    '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',  // Backup
    '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',  // Backup
  ];
  
  // If environment keys exist, use them + fallbacks; otherwise just use fallbacks
  const allKeys = envConfig.isConfigured ? [...envConfig.keys, ...fallbackKeys] : fallbackKeys;
  
  const config = {
    keys: allKeys,
    isConfigured: allKeys.length > 0,
    count: allKeys.length
  };
  
  if (!config.isConfigured) {
    console.warn('⚠️ No Tavily API keys found in environment variables or fallbacks');
    console.log('💡 Expected format: TAVILY_API_KEYS=key1,key2,key3,key4');
  } else {
    console.log(`🔑 Found ${config.count} Tavily API keys (${envConfig.count} from env + ${fallbackKeys.length} fallbacks):`);
    config.keys.forEach((key, index) => {
      const source = index < envConfig.count ? 'env' : 'fallback';
      console.log(`   ${index + 1}. ...${key.slice(-6)} (${source})`);
    });
  }
  
  return config;
}

/**
 * Create a key rotator class for any set of API keys
 */
export class ApiKeyRotator {
  private keys: string[];
  private currentIndex: number = 0;
  private errorCounts: Map<string, number> = new Map();
  private name: string;

  constructor(keys: string[], name: string = 'API') {
    this.keys = keys;
    this.name = name;
    
    // Initialize error counts
    keys.forEach(key => this.errorCounts.set(key, 0));
  }

  /**
   * Get current API key
   */
  getCurrentKey(): string | null {
    if (this.keys.length === 0) return null;
    return this.keys[this.currentIndex];
  }

  /**
   * Rotate to next key
   */
  rotate(): string | null {
    if (this.keys.length <= 1) return this.getCurrentKey();
    
    this.currentIndex = (this.currentIndex + 1) % this.keys.length;
    const newKey = this.getCurrentKey();
    
    console.log(`🔄 ${this.name} key rotated to ${this.currentIndex + 1}/${this.keys.length} (...${newKey?.slice(-4)})`);
    
    return newKey;
  }

  /**
   * Mark current key as having an error
   */
  markError(errorType: string = 'generic'): void {
    const currentKey = this.getCurrentKey();
    if (!currentKey) return;
    
    const currentErrors = this.errorCounts.get(currentKey) || 0;
    this.errorCounts.set(currentKey, currentErrors + 1);
    
    console.warn(`⚠️ ${this.name} key error: ${errorType} (${currentErrors + 1} errors for ...${currentKey.slice(-4)})`);
  }

  /**
   * Get statistics
   */
  getStats() {
    return {
      totalKeys: this.keys.length,
      currentIndex: this.currentIndex,
      currentKey: this.getCurrentKey()?.slice(-6),
      errorCounts: Array.from(this.errorCounts.entries()).map(([key, errors]) => ({
        key: `...${key.slice(-6)}`,
        errors
      }))
    };
  }
}

/**
 * Create a Tavily-specific key rotator
 */
export function createTavilyKeyRotator(): ApiKeyRotator | null {
  const config = parseTavilyApiKeys();
  
  if (!config.isConfigured) {
    return null;
  }
  
  return new ApiKeyRotator(config.keys, 'Tavily');
}