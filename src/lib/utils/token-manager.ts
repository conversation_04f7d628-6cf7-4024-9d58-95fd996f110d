/**
 * Token Management Utility
 * Prevents OpenRouter API token limit errors
 */

export interface TokenLimitConfig {
  maxModelTokens?: number;
  outputTokens?: number;
  bufferSize?: number;
}

export class TokenManager {
  private static readonly MODEL_LIMITS: Record<string, number> = {
    'moonshotai/kimi-k2': 131072,
    'openai/gpt-4': 8192,
    'openai/gpt-4-32k': 32768,
    'openai/gpt-4-turbo': 128000,
    'openai/gpt-3.5-turbo': 4096,
    'openai/gpt-3.5-turbo-16k': 16384,
    'anthropic/claude-3-opus': 200000,
    'anthropic/claude-3-sonnet': 200000,
    'anthropic/claude-3-haiku': 200000,
    'google/gemini-pro': 32768,
    'google/gemini-pro-1.5': 1000000,
  };

  /**
   * Get token limit for a specific model
   */
  static getModelTokenLimit(model: string): number {
    return this.MODEL_LIMITS[model] || 8192; // Default safe limit
  }

  /**
   * Estimate token count from text (rough approximation)
   */
  static estimateTokens(text: string): number {
    return Math.ceil(text.length / 4); // ~4 characters per token
  }

  /**
   * Check if prompt + output would exceed model limits
   */
  static checkTokenLimits(
    prompt: string,
    outputTokens: number,
    model: string,
    bufferSize: number = 1000
  ): { safe: boolean; estimatedInput: number; maxAllowed: number; recommended: number } {
    const modelLimit = this.getModelTokenLimit(model);
    const estimatedInput = this.estimateTokens(prompt);
    const maxAllowed = modelLimit - bufferSize;
    const recommended = Math.max(1000, maxAllowed - estimatedInput);

    return {
      safe: estimatedInput + outputTokens <= maxAllowed,
      estimatedInput,
      maxAllowed,
      recommended: Math.min(recommended, outputTokens)
    };
  }

  /**
   * Adjust output tokens to fit within model limits
   */
  static adjustOutputTokens(
    prompt: string,
    requestedOutputs: number,
    model: string,
    bufferSize: number = 1000
  ): number {
    const check = this.checkTokenLimits(prompt, requestedOutputs, model, bufferSize);
    
    if (check.safe) {
      return requestedOutputs;
    }

    console.warn(`⚠️ Token limit exceeded, adjusting from ${requestedOutputs} to ${check.recommended} tokens`);
    return check.recommended;
  }

  /**
   * Compress prompt if it's too large
   */
  static compressPrompt(
    prompt: string,
    maxTokens: number,
    preserveSystemInstructions: boolean = true
  ): string {
    const currentTokens = this.estimateTokens(prompt);
    
    if (currentTokens <= maxTokens) {
      return prompt;
    }

    console.warn(`⚠️ Compressing prompt from ${currentTokens} to ${maxTokens} tokens`);

    const compressionRatio = maxTokens / currentTokens;
    const targetLength = Math.floor(prompt.length * compressionRatio);

    if (preserveSystemInstructions) {
      // Try to preserve the beginning (system instructions) and compress the middle
      const sections = prompt.split('\n\n');
      const systemInstructions = sections.slice(0, 3).join('\n\n'); // First 3 sections
      const remainingSections = sections.slice(3);
      
      const systemLength = systemInstructions.length;
      const remainingTargetLength = targetLength - systemLength - 4; // Account for separators
      
      if (remainingTargetLength > 100) {
        const compressedRemaining = this.compressSections(remainingSections, remainingTargetLength);
        return systemInstructions + '\n\n' + compressedRemaining;
      }
    }

    // Simple compression: take first N characters
    return prompt.substring(0, targetLength) + '\n\n[Content truncated due to token limits]';
  }

  /**
   * Compress array of sections to fit target length
   */
  private static compressSections(sections: string[], targetLength: number): string {
    let compressedContent = '';
    let currentLength = 0;

    for (const section of sections) {
      if (currentLength + section.length <= targetLength) {
        compressedContent += section + '\n\n';
        currentLength += section.length + 2;
      } else {
        // Compress this section by taking first sentences
        const remainingSpace = targetLength - currentLength;
        if (remainingSpace > 50) {
          const sentences = section.split('. ');
          const ratio = remainingSpace / section.length;
          const keepCount = Math.max(1, Math.floor(sentences.length * ratio));
          compressedContent += sentences.slice(0, keepCount).join('. ') + '\n\n';
        }
        break;
      }
    }

    return compressedContent;
  }

  /**
   * Smart token management for content generation
   */
  static manageContentGeneration(
    prompt: string,
    targetWordCount: number,
    model: string
  ): { compressedPrompt: string; adjustedOutputTokens: number } {
    const baseOutputTokens = Math.min(64000, targetWordCount * 4);
    const adjustedOutputTokens = this.adjustOutputTokens(prompt, baseOutputTokens, model);
    
    const modelLimit = this.getModelTokenLimit(model);
    const maxInputTokens = modelLimit - adjustedOutputTokens - 1000;
    const compressedPrompt = this.compressPrompt(prompt, maxInputTokens);

    return {
      compressedPrompt,
      adjustedOutputTokens
    };
  }
} 