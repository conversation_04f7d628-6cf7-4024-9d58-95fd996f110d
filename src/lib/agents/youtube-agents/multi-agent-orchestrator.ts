import { HookPsychologyAgent } from './hook-psychology-agent'
import { RetentionEngineerAgent } from './retention-engineer-agent'
import { EngagementArchitectAgent } from './engagement-architect-agent'
import { ScriptSynthesizerAgent } from './script-synthesizer-agent'

interface VideoData {
  title: string
  captions: Array<{ text: string; start: number; duration: number }>
  duration: number
  viewCount: number
}

interface OrchestrationProgress {
  phase: 'initialization' | 'hook_analysis' | 'retention_analysis' | 'engagement_analysis' | 'synthesis' | 'validation' | 'completed'
  currentAgent: string
  progress: number
  status: string
  startTime: number
  estimatedCompletion?: number
}

interface MultiAgentResults {
  optimizedScript: {
    title: string
    hook: {
      content: string
      duration: string
      psychologyBasis: string
      expectedImpact: number
    }
    structure: Array<{
      section: string
      timeRange: string
      content: string
      purpose: string
      retentionTechniques: string[]
      engagementElements: string[]
      transitions: string
    }>
    engagementPlan: Array<{
      timing: string
      type: 'question' | 'cta' | 'interaction' | 'story' | 'pattern_interrupt'
      content: string
      implementation: string
      expectedOutcome: string
    }>
    fullScript: string
    metadata: {
      estimatedRetention: number
      engagementPotential: number
      algorithmicOptimization: number
      overallScore: number
      keyOptimizations: string[]
    }
  }
  agentInsights: {
    hookAnalysis: any
    retentionAnalysis: any
    engagementAnalysis: any
  }
  performance: {
    totalProcessingTime: number
    agentPerformance: Array<{
      agent: string
      processingTime: number
      successRate: number
    }>
    optimizationGains: {
      retentionImprovement: number
      engagementBoost: number
      algorithmicBoost: number
    }
  }
  validation: {
    hookEffectiveness: number
    retentionOptimization: number
    engagementIntegration: number
    overallQuality: number
    refinementSuggestions: string[]
  }
}

export class MultiAgentOrchestrator {
  private hookAgent: HookPsychologyAgent
  private retentionAgent: RetentionEngineerAgent
  private engagementAgent: EngagementArchitectAgent
  private synthesizerAgent: ScriptSynthesizerAgent
  
  private progressCallback?: (progress: OrchestrationProgress) => void
  
  constructor(progressCallback?: (progress: OrchestrationProgress) => void) {
    this.hookAgent = new HookPsychologyAgent()
    this.retentionAgent = new RetentionEngineerAgent()
    this.engagementAgent = new EngagementArchitectAgent()
    this.synthesizerAgent = new ScriptSynthesizerAgent()
    
    this.progressCallback = progressCallback
  }

  /**
   * Orchestrates the complete multi-agent YouTube script optimization workflow
   */
  async generateOptimizedScript(
    videos: VideoData[],
    topic: string,
    style: string,
    duration: string,
    targetAudience: string,
    additionalNotes?: string
  ): Promise<MultiAgentResults> {
    const startTime = Date.now()
    console.log('🚀 Multi-Agent Orchestrator: Starting comprehensive YouTube script optimization')
    console.log(`📊 Coordinating 4 specialized agents for "${topic}" optimization`)

    try {
      // Phase 1: Initialization
      this.updateProgress({
        phase: 'initialization',
        currentAgent: 'System',
        progress: 5,
        status: 'Initializing multi-agent system...',
        startTime
      })

      await this.delay(500) // Brief initialization delay

      // Phase 2: Hook Psychology Analysis
      this.updateProgress({
        phase: 'hook_analysis',
        currentAgent: 'Hook Psychology Specialist',
        progress: 15,
        status: 'Analyzing psychological hook patterns and 8-15 second optimization...',
        startTime,
        estimatedCompletion: startTime + 180000 // 3 minutes estimate
      })

      const hookAnalysisStart = Date.now()
      const hookAnalysis = await this.hookAgent.analyzeHookPatterns(videos, topic, targetAudience)
      const hookGeneration = await this.hookAgent.generateOptimizedHook(
        hookAnalysis, topic, style, targetAudience, duration
      )
      const hookProcessingTime = Date.now() - hookAnalysisStart

      console.log(`✅ Hook Psychology Specialist completed in ${hookProcessingTime}ms`)

      // Phase 3: Retention Engineering Analysis
      this.updateProgress({
        phase: 'retention_analysis',
        currentAgent: 'Retention Engineer',
        progress: 35,
        status: 'Analyzing drop-off patterns and retention optimization strategies...',
        startTime,
        estimatedCompletion: startTime + 150000
      })

      const retentionAnalysisStart = Date.now()
      const retentionAnalysis = await this.retentionAgent.analyzeRetentionPatterns(videos, topic, duration)
      const retentionOptimizations = await this.retentionAgent.generateRetentionOptimizations(
        retentionAnalysis, topic, style, duration
      )
      const retentionProcessingTime = Date.now() - retentionAnalysisStart

      console.log(`✅ Retention Engineer completed in ${retentionProcessingTime}ms`)

      // Phase 4: Engagement Architecture Analysis
      this.updateProgress({
        phase: 'engagement_analysis',
        currentAgent: 'Engagement Architect',
        progress: 55,
        status: 'Optimizing viewer interaction and engagement strategies...',
        startTime,
        estimatedCompletion: startTime + 120000
      })

      const engagementAnalysisStart = Date.now()
      const engagementAnalysis = await this.engagementAgent.analyzeEngagementPatterns(
        videos, topic, targetAudience
      )
      const engagementStrategy = await this.engagementAgent.generateEngagementStrategy(
        engagementAnalysis, topic, style, duration, targetAudience
      )
      const engagementProcessingTime = Date.now() - engagementAnalysisStart

      console.log(`✅ Engagement Architect completed in ${engagementProcessingTime}ms`)

      // Phase 5: Script Synthesis
      this.updateProgress({
        phase: 'synthesis',
        currentAgent: 'Script Synthesizer',
        progress: 75,
        status: 'Synthesizing all agent insights into optimized script...',
        startTime,
        estimatedCompletion: startTime + 90000
      })

      const synthesisStart = Date.now()
      const optimizedScript = await this.synthesizerAgent.synthesizeOptimizedScript(
        hookAnalysis,
        retentionAnalysis,
        engagementAnalysis,
        topic,
        style,
        duration,
        targetAudience,
        additionalNotes
      )
      const synthesisProcessingTime = Date.now() - synthesisStart

      console.log(`✅ Script Synthesizer completed in ${synthesisProcessingTime}ms`)

      // Phase 6: Validation and Refinement
      this.updateProgress({
        phase: 'validation',
        currentAgent: 'Script Synthesizer',
        progress: 90,
        status: 'Validating and refining optimized script...',
        startTime,
        estimatedCompletion: startTime + 30000
      })

      const validationStart = Date.now()
      const validation = await this.synthesizerAgent.validateAndRefineScript(
        optimizedScript,
        hookAnalysis,
        retentionAnalysis,
        engagementAnalysis
      )
      const validationProcessingTime = Date.now() - validationStart

      const totalProcessingTime = Date.now() - startTime

      // Phase 7: Completion
      this.updateProgress({
        phase: 'completed',
        currentAgent: 'System',
        progress: 100,
        status: 'Multi-agent optimization completed successfully!',
        startTime
      })

      // Calculate optimization gains
      const baselineRetention = 65 // Industry average
      const baselineEngagement = 3.5 // Industry average engagement rate
      const baselineAlgorithmic = 70 // Base algorithmic performance

      const optimizationGains = {
        retentionImprovement: Math.max(0, optimizedScript.metadata.estimatedRetention - baselineRetention),
        engagementBoost: Math.max(0, optimizedScript.metadata.engagementPotential - baselineEngagement),
        algorithmicBoost: Math.max(0, optimizedScript.metadata.algorithmicOptimization - baselineAlgorithmic)
      }

      const results: MultiAgentResults = {
        optimizedScript: {
          ...optimizedScript,
          fullScript: (validation.finalScript && validation.finalScript !== 'Refined script with all optimizations applied') 
            ? validation.finalScript 
            : optimizedScript.fullScript
        },
        agentInsights: {
          hookAnalysis: {
            ...hookAnalysis,
            generatedHooks: hookGeneration
          },
          retentionAnalysis: {
            ...retentionAnalysis,
            optimizations: retentionOptimizations
          },
          engagementAnalysis: {
            ...engagementAnalysis,
            strategy: engagementStrategy
          }
        },
        performance: {
          totalProcessingTime,
          agentPerformance: [
            {
              agent: 'Hook Psychology Specialist',
              processingTime: hookProcessingTime,
              successRate: hookAnalysis.expectedRetention > 80 ? 100 : 85
            },
            {
              agent: 'Retention Engineer',
              processingTime: retentionProcessingTime,
              successRate: retentionAnalysis.overallRetentionScore > 75 ? 100 : 88
            },
            {
              agent: 'Engagement Architect',
              processingTime: engagementProcessingTime,
              successRate: engagementAnalysis.overallEngagementScore > 80 ? 100 : 90
            },
            {
              agent: 'Script Synthesizer',
              processingTime: synthesisProcessingTime + validationProcessingTime,
              successRate: optimizedScript.metadata.overallScore > 85 ? 100 : 92
            }
          ],
          optimizationGains
        },
        validation: validation.validationResults
      }

      console.log('🎉 Multi-Agent Orchestrator: Optimization completed successfully!')
      console.log(`⏱️ Total processing time: ${totalProcessingTime}ms`)
      console.log(`📈 Retention improvement: +${optimizationGains.retentionImprovement}%`)
      console.log(`🎯 Engagement boost: +${optimizationGains.engagementBoost}%`)
      console.log(`🤖 Algorithmic boost: +${optimizationGains.algorithmicBoost}%`)
      console.log(`⭐ Overall optimization score: ${optimizedScript.metadata.overallScore}%`)

      return results

    } catch (error) {
      console.error('❌ Multi-Agent Orchestrator failed:', error)
      throw new Error(`Multi-agent optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Runs a parallel analysis mode for faster processing (experimental)
   */
  async generateOptimizedScriptParallel(
    videos: VideoData[],
    topic: string,
    style: string,
    duration: string,
    targetAudience: string,
    additionalNotes?: string
  ): Promise<MultiAgentResults> {
    const startTime = Date.now()
    console.log('⚡ Multi-Agent Orchestrator: Starting PARALLEL optimization mode')

    try {
      this.updateProgress({
        phase: 'initialization',
        currentAgent: 'Parallel System',
        progress: 10,
        status: 'Starting parallel agent analysis...',
        startTime
      })

      // Run Hook, Retention, and Engagement agents in parallel
      const [hookResults, retentionResults, engagementResults] = await Promise.all([
        Promise.all([
          this.hookAgent.analyzeHookPatterns(videos, topic, targetAudience),
          this.hookAgent.generateOptimizedHook(
            await this.hookAgent.analyzeHookPatterns(videos, topic, targetAudience),
            topic, style, targetAudience, duration
          )
        ]),
        Promise.all([
          this.retentionAgent.analyzeRetentionPatterns(videos, topic, duration),
          this.retentionAgent.generateRetentionOptimizations(
            await this.retentionAgent.analyzeRetentionPatterns(videos, topic, duration),
            topic, style, duration
          )
        ]),
        Promise.all([
          this.engagementAgent.analyzeEngagementPatterns(videos, topic, targetAudience),
          this.engagementAgent.generateEngagementStrategy(
            await this.engagementAgent.analyzeEngagementPatterns(videos, topic, targetAudience),
            topic, style, duration, targetAudience
          )
        ])
      ])

      const [hookAnalysis, hookGeneration] = hookResults
      const [retentionAnalysis, retentionOptimizations] = retentionResults
      const [engagementAnalysis, engagementStrategy] = engagementResults

      this.updateProgress({
        phase: 'synthesis',
        currentAgent: 'Script Synthesizer',
        progress: 70,
        status: 'Synthesizing parallel results...',
        startTime
      })

      // Sequential synthesis (requires all inputs)
      const optimizedScript = await this.synthesizerAgent.synthesizeOptimizedScript(
        hookAnalysis,
        retentionAnalysis,
        engagementAnalysis,
        topic,
        style,
        duration,
        targetAudience,
        additionalNotes
      )

      const validation = await this.synthesizerAgent.validateAndRefineScript(
        optimizedScript,
        hookAnalysis,
        retentionAnalysis,
        engagementAnalysis
      )

      const totalProcessingTime = Date.now() - startTime

      this.updateProgress({
        phase: 'completed',
        currentAgent: 'Parallel System',
        progress: 100,
        status: 'Parallel optimization completed!',
        startTime
      })

      console.log(`⚡ Parallel processing completed in ${totalProcessingTime}ms`)

      // Return similar results structure as sequential version
      return {
        optimizedScript: {
          ...optimizedScript,
          fullScript: (validation.finalScript && validation.finalScript !== 'Refined script with all optimizations applied') 
            ? validation.finalScript 
            : optimizedScript.fullScript
        },
        agentInsights: {
          hookAnalysis: { ...hookAnalysis, generatedHooks: hookGeneration },
          retentionAnalysis: { ...retentionAnalysis, optimizations: retentionOptimizations },
          engagementAnalysis: { ...engagementAnalysis, strategy: engagementStrategy }
        },
        performance: {
          totalProcessingTime,
          agentPerformance: [
            { agent: 'Parallel Processing', processingTime: totalProcessingTime, successRate: 95 }
          ],
          optimizationGains: {
            retentionImprovement: 25,
            engagementBoost: 30,
            algorithmicBoost: 20
          }
        },
        validation: validation.validationResults
      }

    } catch (error) {
      console.error('❌ Parallel multi-agent processing failed:', error)
      throw new Error(`Parallel optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Gets the current status of all agents
   */
  getAgentStatus(): {
    agents: Array<{
      name: string
      status: 'idle' | 'processing' | 'completed' | 'error'
      lastActivity?: Date
    }>
    systemHealth: 'excellent' | 'good' | 'degraded' | 'error'
  } {
    return {
      agents: [
        { name: 'Hook Psychology Specialist', status: 'idle' },
        { name: 'Retention Engineer', status: 'idle' },
        { name: 'Engagement Architect', status: 'idle' },
        { name: 'Script Synthesizer', status: 'idle' }
      ],
      systemHealth: 'excellent'
    }
  }

  /**
   * Analyzes the potential impact before running full optimization
   */
  async analyzePotentialImpact(videos: VideoData[], topic: string): Promise<{
    estimatedGains: {
      retentionImprovement: string
      engagementBoost: string
      algorithmicBenefit: string
    }
    processingTime: string
    confidence: number
    recommendations: string[]
  }> {
    console.log('🔍 Analyzing potential optimization impact...')

    // Quick analysis of video data quality
    const avgViewCount = videos.reduce((sum, v) => sum + (v.viewCount || 0), 0) / videos.length
    const avgDuration = videos.reduce((sum, v) => sum + v.duration, 0) / videos.length
    const totalCaptions = videos.reduce((sum, v) => sum + v.captions.length, 0)

    // Estimate potential gains based on data quality
    const dataQuality = Math.min(100, (totalCaptions / 100) + (avgViewCount / 10000) + (avgDuration / 60))
    const confidence = Math.max(75, Math.min(95, dataQuality))

    return {
      estimatedGains: {
        retentionImprovement: `+${Math.round(15 + (dataQuality * 0.3))}%`,
        engagementBoost: `+${Math.round(20 + (dataQuality * 0.4))}%`,
        algorithmicBenefit: `+${Math.round(18 + (dataQuality * 0.35))}%`
      },
      processingTime: videos.length > 3 ? '4-6 minutes' : '3-4 minutes',
      confidence,
      recommendations: [
        `${videos.length} videos provide ${videos.length > 4 ? 'excellent' : 'good'} data foundation`,
        `Topic "${topic}" has ${confidence > 85 ? 'high' : 'moderate'} optimization potential`,
        'Multi-agent system will analyze psychology, retention, and engagement patterns',
        'Expected significant improvement in viewer retention and engagement'
      ]
    }
  }

  private updateProgress(progress: OrchestrationProgress): void {
    if (this.progressCallback) {
      this.progressCallback(progress)
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}