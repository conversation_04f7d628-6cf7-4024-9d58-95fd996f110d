import { GeminiService } from '@/lib/gemini'

interface EngagementTrigger {
  type: 'question' | 'poll' | 'challenge' | 'cta' | 'interaction'
  timing: number
  content: string
  expectedResponse: string
  engagementScore: number
  implementation: string
}

interface InteractionPattern {
  pattern: string
  effectiveness: number
  placement: string[]
  audienceResponse: string
  psychology: string
  examples: string[]
}

interface EngagementAnalysis {
  interactionPatterns: InteractionPattern[]
  engagementTriggers: EngagementTrigger[]
  commentDrivers: {
    questions: string[]
    controversialPoints: string[]
    personalConnections: string[]
    actionableAdvice: string[]
  }
  socialProofElements: {
    credibilitySignals: string[]
    communityBuilding: string[]
    shareableQuotes: string[]
  }
  algorithmicOptimizations: {
    retentionBoosts: string[]
    engagementSignals: string[]
    shareabilityFactors: string[]
  }
  overallEngagementScore: number
}

export class EngagementArchitectAgent {
  private gemini: GeminiService
  private agentName = 'Engagement Architect'
  
  constructor() {
    this.gemini = new GeminiService()
  }

  /**
   * Analyzes successful videos to identify engagement patterns and interaction triggers
   */
  async analyzeEngagementPatterns(
    videos: Array<{
      title: string
      captions: Array<{ text: string; start: number; duration: number }>
      duration: number
      viewCount: number
    }>,
    topic: string,
    targetAudience: string
  ): Promise<EngagementAnalysis> {
    console.log('🎯 Engagement Architect: Analyzing viewer interaction and engagement patterns')
    
    // Extract engagement-rich segments
    const engagementSegments = this.extractEngagementSegments(videos)
    
    const analysisPrompt = this.buildEngagementAnalysisPrompt(engagementSegments, topic, targetAudience)
    
    try {
      const result = await this.gemini.generateContent(
        analysisPrompt,
        {
          temperature: 0.3, // Balanced for pattern recognition and creativity
          maxOutputTokens: 12000, // Increased for more comprehensive analysis
          thinkingConfig: {
            thinkingBudget: 8192, // Enhanced deep engagement psychology analysis
            includeThoughts: false
          }
        },
        'Engagement Pattern Analysis'
      )

      const analysis = this.parseEngagementAnalysis(result.response)
      
      console.log(`✅ Engagement Architect: Identified ${analysis.interactionPatterns.length} interaction patterns`)
      console.log(`💬 Comment drivers: ${analysis.commentDrivers.questions.length} question triggers found`)
      console.log(`📈 Overall engagement score: ${analysis.overallEngagementScore}%`)
      
      return analysis

    } catch (error) {
      console.error('❌ Engagement Architect failed:', error)
      throw new Error(`Engagement analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generates strategic engagement optimization recommendations
   */
  async generateEngagementStrategy(
    analysis: EngagementAnalysis,
    topic: string,
    style: string,
    duration: string,
    targetAudience: string
  ): Promise<{
    engagementPlan: {
      opening: {
        hooks: string[]
        interactionPrompts: string[]
        communityBuilding: string
      }
      throughout: Array<{
        timing: string
        technique: string
        implementation: string
        expectedOutcome: string
      }>
      closing: {
        ctaStrategy: string
        communityAction: string
        nextVideoTease: string
      }
    }
    commentOptimization: {
      questionSeeds: string[]
      controversyPoints: string[]
      personalStories: string[]
      actionableTakeaways: string[]
    }
    algorithmicBoosts: {
      retentionTechniques: string[]
      engagementSignals: string[]
      virality: string[]
    }
    measurementStrategy: {
      keyMetrics: string[]
      successIndicators: string[]
      optimizationPoints: string[]
    }
  }> {
    console.log('🚀 Engagement Architect: Generating comprehensive engagement strategy')

    const strategyPrompt = this.buildStrategyPrompt(analysis, topic, style, duration, targetAudience)

    try {
      const result = await this.gemini.generateContent(
        strategyPrompt,
        {
          temperature: 0.6, // Creative for strategic recommendations
          maxOutputTokens: 6000,
          thinkingConfig: {
            thinkingBudget: 4096,
            includeThoughts: false
          }
        },
        'Engagement Strategy Generation'
      )

      const strategy = this.parseEngagementStrategy(result.response)
      
      console.log('✅ Engagement Architect: Generated comprehensive engagement strategy')
      
      return strategy

    } catch (error) {
      console.error('❌ Engagement strategy generation failed:', error)
      throw new Error(`Strategy generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private extractEngagementSegments(
    videos: Array<{
      title: string
      captions: Array<{ text: string; start: number; duration: number }>
      duration: number
      viewCount: number
    }>
  ): Array<{
    title: string
    engagementMoments: Array<{
      timeStamp: number
      content: string
      type: 'question' | 'cta' | 'interaction' | 'story' | 'controversy'
      context: string
    }>
    viewCount: number
  }> {
    return videos.map(video => {
      const engagementMoments: Array<{
        timeStamp: number
        content: string
        type: 'question' | 'cta' | 'interaction' | 'story' | 'controversy'
        context: string
      }> = []

      // Identify potential engagement moments in captions
      video.captions.forEach(caption => {
        const text = caption.text.toLowerCase()
        
        // Questions
        if (text.includes('?') || text.includes('what do you think') || text.includes('let me know')) {
          engagementMoments.push({
            timeStamp: caption.start,
            content: caption.text,
            type: 'question',
            context: this.getContext(video.captions, caption.start, 30)
          })
        }
        
        // CTAs
        if (text.includes('subscribe') || text.includes('like') || text.includes('comment') || text.includes('share')) {
          engagementMoments.push({
            timeStamp: caption.start,
            content: caption.text,
            type: 'cta',
            context: this.getContext(video.captions, caption.start, 20)
          })
        }
        
        // Interactive elements
        if (text.includes('try this') || text.includes('test') || text.includes('experiment') || text.includes('challenge')) {
          engagementMoments.push({
            timeStamp: caption.start,
            content: caption.text,
            type: 'interaction',
            context: this.getContext(video.captions, caption.start, 25)
          })
        }
        
        // Personal stories
        if (text.includes('i remember') || text.includes('when i') || text.includes('my experience') || text.includes('personally')) {
          engagementMoments.push({
            timeStamp: caption.start,
            content: caption.text,
            type: 'story',
            context: this.getContext(video.captions, caption.start, 35)
          })
        }
        
        // Controversial/debate points
        if (text.includes('controversial') || text.includes('unpopular opinion') || text.includes('disagree') || text.includes('debate')) {
          engagementMoments.push({
            timeStamp: caption.start,
            content: caption.text,
            type: 'controversy',
            context: this.getContext(video.captions, caption.start, 40)
          })
        }
      })

      return {
        title: video.title,
        engagementMoments: engagementMoments.slice(0, 15), // Limit for analysis
        viewCount: video.viewCount
      }
    })
  }

  private getContext(
    captions: Array<{ text: string; start: number; duration: number }>,
    targetTime: number,
    seconds: number
  ): string {
    const contextCaptions = captions.filter(
      cap => cap.start >= targetTime - seconds && cap.start <= targetTime + seconds
    )
    return contextCaptions.map(cap => cap.text).join(' ').substring(0, 200)
  }

  private buildEngagementAnalysisPrompt(
    engagementSegments: Array<{
      title: string
      engagementMoments: Array<{
        timeStamp: number
        content: string
        type: 'question' | 'cta' | 'interaction' | 'story' | 'controversy'
        context: string
      }>
      viewCount: number
    }>,
    topic: string,
    targetAudience: string
  ): string {
    const segmentAnalysis = engagementSegments.map((video, index) => `
**VIDEO ${index + 1}: "${video.title}"**
Views: ${video.viewCount?.toLocaleString() || 'Unknown'}

**Engagement Moments:**
${video.engagementMoments.map(moment => `
[${Math.round(moment.timeStamp)}s] ${moment.type.toUpperCase()}: "${moment.content}"
Context: "${moment.context}"
`).join('')}
---`).join('\n')

    return `You are an Engagement Architect AI agent specializing in YouTube audience interaction optimization and community building psychology. Your expertise is maximizing likes, comments, shares, and algorithmic performance.

# MISSION
Analyze these successful videos about "${topic}" targeting "${targetAudience}" to identify engagement patterns that drive viewer interaction and algorithmic promotion.

# ENGAGEMENT MOMENTS ANALYSIS
${segmentAnalysis}

# ENGAGEMENT ARCHITECTURE FRAMEWORK

## 1. INTERACTION PATTERN ANALYSIS
- Question placement and timing effectiveness
- Call-to-action positioning and psychology
- Community-building language and techniques
- Audience participation triggers

## 2. COMMENT PSYCHOLOGY
- Questions that generate discussion
- Controversial points that spark debate (constructive)
- Personal connection opportunities
- Actionable advice that prompts sharing

## 3. SOCIAL PROOF & CREDIBILITY
- Authority establishment techniques
- Community validation methods
- Shareable quote identification
- Trust-building mechanisms

## 4. ALGORITHMIC OPTIMIZATION
- Retention-boosting engagement techniques
- Signal generation for YouTube algorithm
- Shareability and virality factors
- Session time optimization

# ANALYSIS REQUIREMENTS

Provide comprehensive JSON analysis:

\`\`\`json
{
  "interactionPatterns": [
    {
      "pattern": "Question-driven engagement",
      "effectiveness": 88,
      "placement": ["2:30", "5:45", "8:20"],
      "audienceResponse": "High comment volume",
      "psychology": "Curiosity and opinion sharing",
      "examples": ["Specific examples from videos"]
    }
  ],
  "engagementTriggers": [
    {
      "type": "question",
      "timing": 150,
      "content": "What's your experience with this?",
      "expectedResponse": "Personal stories in comments",
      "engagementScore": 85,
      "implementation": "Natural integration method"
    }
  ],
  "commentDrivers": {
    "questions": ["Thought-provoking questions"],
    "controversialPoints": ["Respectful debate starters"],
    "personalConnections": ["Relatability triggers"],
    "actionableAdvice": ["Shareable tips"]
  },
  "socialProofElements": {
    "credibilitySignals": ["Authority markers"],
    "communityBuilding": ["Community language"],
    "shareableQuotes": ["Memorable phrases"]
  },
  "algorithmicOptimizations": {
    "retentionBoosts": ["Retention techniques"],
    "engagementSignals": ["Interaction triggers"],
    "shareabilityFactors": ["Viral elements"]
  },
  "overallEngagementScore": 83
}
\`\`\`

Focus on patterns that specifically work for "${targetAudience}" consuming "${topic}" content. Identify measurable engagement drivers with clear implementation strategies.`
  }

  private buildStrategyPrompt(
    analysis: EngagementAnalysis,
    topic: string,
    style: string,
    duration: string,
    targetAudience: string
  ): string {
    return `You are an Engagement Architect creating a comprehensive engagement optimization strategy based on proven patterns.

# TASK
Generate a complete engagement strategy for a ${style} video about "${topic}" targeting "${targetAudience}" with ${duration} duration.

# ENGAGEMENT ANALYSIS DATA
**Current Engagement Score:** ${analysis.overallEngagementScore}%
**Top Interaction Patterns:** ${analysis.interactionPatterns.map(p => p.pattern).join(', ')}
**Key Comment Drivers:** ${analysis.commentDrivers.questions.length} questions, ${analysis.commentDrivers.controversialPoints.length} debate points
**Social Proof Elements:** ${analysis.socialProofElements.credibilitySignals.length} credibility signals available

# STRATEGY REQUIREMENTS

## 1. COMPREHENSIVE ENGAGEMENT PLAN
- Opening: Hook + interaction setup + community building
- Throughout: Timed engagement triggers with specific implementations
- Closing: CTA strategy + community action + retention tactics

## 2. COMMENT OPTIMIZATION STRATEGY
- Question seeds that generate discussion
- Respectful controversy points for healthy debate
- Personal story prompts for connection
- Actionable takeaways for value sharing

## 3. ALGORITHMIC BOOST TACTICS
- Retention-focused engagement techniques
- Signal generation for YouTube algorithm
- Virality and shareability optimization
- Session time and click-through optimization

## 4. MEASUREMENT & OPTIMIZATION
- Key engagement metrics to track
- Success indicators and benchmarks
- Continuous optimization opportunities

# OUTPUT FORMAT

\`\`\`json
{
  "engagementPlan": {
    "opening": {
      "hooks": ["Engagement-focused hook options"],
      "interactionPrompts": ["Early interaction triggers"],
      "communityBuilding": "Community connection strategy"
    },
    "throughout": [
      {
        "timing": "2:30",
        "technique": "Question integration",
        "implementation": "Specific implementation steps",
        "expectedOutcome": "Predicted engagement result"
      }
    ],
    "closing": {
      "ctaStrategy": "Strategic call-to-action approach",
      "communityAction": "Community building action",
      "nextVideoTease": "Retention and subscription strategy"
    }
  },
  "commentOptimization": {
    "questionSeeds": ["Discussion-generating questions"],
    "controversyPoints": ["Respectful debate starters"],
    "personalStories": ["Connection opportunities"],
    "actionableTakeaways": ["Shareable insights"]
  },
  "algorithmicBoosts": {
    "retentionTechniques": ["Algorithm-friendly retention methods"],
    "engagementSignals": ["Signal generation tactics"],
    "virality": ["Shareability optimization"]
  },
  "measurementStrategy": {
    "keyMetrics": ["Primary engagement metrics"],
    "successIndicators": ["Performance benchmarks"],
    "optimizationPoints": ["Improvement opportunities"]
  }
}
\`\`\`

Create strategies that will measurably improve engagement beyond the current ${analysis.overallEngagementScore}% baseline. Focus on ${targetAudience}-specific tactics for "${topic}" content.`
  }

  private parseEngagementAnalysis(response: string): EngagementAnalysis {
    try {
      const cleaned = response.replace(/```json\n?/g, '').replace(/\n?```/g, '').trim()
      const parsed = JSON.parse(cleaned)
      
      if (this.isValidEngagementAnalysis(parsed)) {
        return parsed
      }
    } catch (error) {
      console.log('📝 Parsing engagement analysis manually due to JSON error')
    }

    // Fallback manual parsing
    return this.extractEngagementInsights(response)
  }

  private parseEngagementStrategy(response: string): {
    engagementPlan: {
      opening: {
        hooks: string[]
        interactionPrompts: string[]
        communityBuilding: string
      }
      throughout: Array<{
        timing: string
        technique: string
        implementation: string
        expectedOutcome: string
      }>
      closing: {
        ctaStrategy: string
        communityAction: string
        nextVideoTease: string
      }
    }
    commentOptimization: {
      questionSeeds: string[]
      controversyPoints: string[]
      personalStories: string[]
      actionableTakeaways: string[]
    }
    algorithmicBoosts: {
      retentionTechniques: string[]
      engagementSignals: string[]
      virality: string[]
    }
    measurementStrategy: {
      keyMetrics: string[]
      successIndicators: string[]
      optimizationPoints: string[]
    }
  } {
    try {
      const cleaned = response.replace(/```json\n?/g, '').replace(/\n?```/g, '').trim()
      const parsed = JSON.parse(cleaned)
      
      if (parsed.engagementPlan && parsed.commentOptimization) {
        return parsed
      }
    } catch (error) {
      console.log('📝 Parsing engagement strategy manually')
    }

    // Fallback strategy generation
    const extractList = (text: string, keywords: string[]): string[] => {
      const items: string[] = []
      keywords.forEach(keyword => {
        const regex = new RegExp(`${keyword}[^\\n]*`, 'gi')
        const matches = text.match(regex)
        if (matches) {
          items.push(...matches.map(m => m.replace(/^[^:]*:/, '').trim()))
        }
      })
      return [...new Set(items)].slice(0, 5)
    }

    return {
      engagementPlan: {
        opening: {
          hooks: extractList(response, ['hook', 'opening', 'start']),
          interactionPrompts: extractList(response, ['question', 'ask', 'interact']),
          communityBuilding: 'Build immediate connection with audience through relatable opening'
        },
        throughout: [
          {
            timing: '2:30',
            technique: 'Strategic question placement',
            implementation: 'Ask thought-provoking question about viewer experience',
            expectedOutcome: 'Increased comment engagement'
          }
        ],
        closing: {
          ctaStrategy: 'Multi-layer call-to-action with specific asks',
          communityAction: 'Invite viewers to share experiences in comments',
          nextVideoTease: 'Preview valuable content in upcoming videos'
        }
      },
      commentOptimization: {
        questionSeeds: extractList(response, ['question', 'ask', 'what do you']),
        controversyPoints: extractList(response, ['controversial', 'debate', 'opinion']),
        personalStories: extractList(response, ['story', 'experience', 'personal']),
        actionableTakeaways: extractList(response, ['tip', 'advice', 'takeaway'])
      },
      algorithmicBoosts: {
        retentionTechniques: extractList(response, ['retention', 'keep watching', 'stay']),
        engagementSignals: extractList(response, ['engagement', 'interact', 'signal']),
        virality: extractList(response, ['viral', 'share', 'spread'])
      },
      measurementStrategy: {
        keyMetrics: ['Comments per view', 'Like ratio', 'Share rate', 'Session duration'],
        successIndicators: ['Above 2% engagement rate', 'High comment quality', 'Increased subscribers'],
        optimizationPoints: ['Question placement timing', 'CTA effectiveness', 'Community response rate']
      }
    }
  }

  private isValidEngagementAnalysis(obj: any): obj is EngagementAnalysis {
    return (
      obj &&
      Array.isArray(obj.interactionPatterns) &&
      Array.isArray(obj.engagementTriggers) &&
      obj.commentDrivers &&
      obj.overallEngagementScore
    )
  }

  private extractEngagementInsights(text: string): EngagementAnalysis {
    const extractList = (text: string, keywords: string[]): string[] => {
      const items: string[] = []
      keywords.forEach(keyword => {
        const regex = new RegExp(`${keyword}[^\\n]*`, 'gi')
        const matches = text.match(regex)
        if (matches) {
          items.push(...matches.map(m => m.replace(/^[^:]*:/, '').trim()))
        }
      })
      return [...new Set(items)].slice(0, 6)
    }

    return {
      interactionPatterns: [
        {
          pattern: 'Question-driven engagement',
          effectiveness: 85,
          placement: ['Opening', 'Mid-content', 'Closing'],
          audienceResponse: 'High comment participation',
          psychology: 'Curiosity and opinion sharing drive',
          examples: extractList(text, ['question', 'ask', 'what do you think'])
        },
        {
          pattern: 'Personal story connection',
          effectiveness: 78,
          placement: ['Throughout content'],
          audienceResponse: 'Relatable sharing in comments',
          psychology: 'Identity and experience connection',
          examples: extractList(text, ['story', 'experience', 'personally'])
        }
      ],
      engagementTriggers: [
        {
          type: 'question',
          timing: 150,
          content: 'What has been your experience with this?',
          expectedResponse: 'Personal stories and opinions in comments',
          engagementScore: 82,
          implementation: 'Natural conversation integration'
        },
        {
          type: 'cta',
          timing: 300,
          content: 'Let me know your thoughts in the comments',
          expectedResponse: 'Direct comment engagement',
          engagementScore: 75,
          implementation: 'Strategic placement after value delivery'
        }
      ],
      commentDrivers: {
        questions: extractList(text, ['question', 'ask', 'what', 'how', 'why']),
        controversialPoints: extractList(text, ['controversial', 'debate', 'opinion', 'disagree']),
        personalConnections: extractList(text, ['relate', 'experience', 'story', 'personally']),
        actionableAdvice: extractList(text, ['tip', 'advice', 'try this', 'implement'])
      },
      socialProofElements: {
        credibilitySignals: extractList(text, ['expert', 'experience', 'proven', 'results']),
        communityBuilding: extractList(text, ['community', 'together', 'we', 'us']),
        shareableQuotes: extractList(text, ['quote', 'remember', 'key insight'])
      },
      algorithmicOptimizations: {
        retentionBoosts: extractList(text, ['retention', 'keep watching', 'stay tuned']),
        engagementSignals: extractList(text, ['like', 'comment', 'subscribe', 'share']),
        shareabilityFactors: extractList(text, ['shareable', 'viral', 'spread', 'tell friends'])
      },
      overallEngagementScore: 79
    }
  }
}