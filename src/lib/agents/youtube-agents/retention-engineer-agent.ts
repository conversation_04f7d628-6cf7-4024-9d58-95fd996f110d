import { GeminiService } from '@/lib/gemini'

interface RetentionPattern {
  timeframe: string
  dropOffPoints: number[]
  retentionTechniques: string[]
  effectiveness: number
  psychologyPrinciple: string
  implementation: string
}

interface PatternInterrupt {
  type: string
  timing: number
  description: string
  impactScore: number
  examples: string[]
}

interface RetentionAnalysis {
  criticalDropOffPoints: number[]
  retentionPatterns: RetentionPattern[]
  patternInterrupts: PatternInterrupt[]
  pacing: {
    segments: Array<{
      timeRange: string
      recommendedPace: 'fast' | 'medium' | 'slow'
      contentDensity: 'high' | 'medium' | 'low'
      transitionType: string
    }>
  }
  overallRetentionScore: number
  improvements: {
    immediateWins: string[]
    longTermOptimizations: string[]
    avoidancePatterns: string[]
  }
}

export class RetentionEngineerAgent {
  private gemini: GeminiService
  private agentName = 'Retention Engineer'
  
  constructor() {
    this.gemini = new GeminiService()
  }

  /**
   * Analyzes video content to identify drop-off patterns and retention opportunities
   */
  async analyzeRetentionPatterns(
    videos: Array<{
      title: string
      captions: Array<{ text: string; start: number; duration: number }>
      duration: number
      viewCount: number
    }>,
    topic: string,
    targetDuration: string
  ): Promise<RetentionAnalysis> {
    console.log('📊 Retention Engineer: Analyzing drop-off patterns and retention mechanisms')
    
    // Segment videos by time intervals for drop-off analysis
    const segmentedContent = this.segmentVideosForAnalysis(videos, targetDuration)
    
    const analysisPrompt = this.buildRetentionAnalysisPrompt(segmentedContent, topic, targetDuration)
    
    try {
      const result = await this.gemini.generateContent(
        analysisPrompt,
        {
          temperature: 0.1, // Very analytical for pattern recognition
          maxOutputTokens: 12000, // Increased for more comprehensive analysis
          thinkingConfig: {
            thinkingBudget: 12288, // Enhanced deep analysis of retention patterns
            includeThoughts: false
          }
        },
        'Retention Pattern Analysis'
      )

      const analysis = this.parseRetentionAnalysis(result.response, targetDuration)
      
      console.log(`✅ Retention Engineer: Identified ${analysis.criticalDropOffPoints.length} critical drop-off points`)
      console.log(`🔄 Pattern interrupts: ${analysis.patternInterrupts.length} optimization opportunities`)
      console.log(`📈 Overall retention score: ${analysis.overallRetentionScore}%`)
      
      return analysis

    } catch (error) {
      console.error('❌ Retention Engineer failed:', error)
      throw new Error(`Retention analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Generates retention optimization strategies for script structure
   */
  async generateRetentionOptimizations(
    analysis: RetentionAnalysis,
    topic: string,
    style: string,
    duration: string
  ): Promise<{
    structuralOptimizations: {
      openingStrategy: string
      middleRetention: string[]
      closingStrategy: string
    }
    patternInterrupts: Array<{
      timing: string
      technique: string
      implementation: string
      expectedImpact: number
    }>
    pacingRecommendations: {
      segments: Array<{
        timeRange: string
        strategy: string
        content: string
        transitions: string
      }>
    }
    retentionHacks: string[]
  }> {
    console.log('🚀 Retention Engineer: Generating optimization strategies')

    const optimizationPrompt = this.buildOptimizationPrompt(analysis, topic, style, duration)

    try {
      const result = await this.gemini.generateContent(
        optimizationPrompt,
        {
          temperature: 0.4, // Balanced for strategic recommendations
          maxOutputTokens: 6000,
          thinkingConfig: {
            thinkingBudget: 4096,
            includeThoughts: false
          }
        },
        'Retention Optimization'
      )

      const optimizations = this.parseOptimizations(result.response)
      
      console.log('✅ Retention Engineer: Generated comprehensive retention optimizations')
      
      return optimizations

    } catch (error) {
      console.error('❌ Retention optimization failed:', error)
      throw new Error(`Optimization generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private segmentVideosForAnalysis(
    videos: Array<{
      title: string
      captions: Array<{ text: string; start: number; duration: number }>
      duration: number
      viewCount: number
    }>,
    targetDuration: string
  ): Array<{
    title: string
    segments: Array<{
      timeRange: string
      content: string
      position: 'opening' | 'early' | 'middle' | 'late' | 'closing'
      wordCount: number
      contentDensity: number
    }>
    totalDuration: number
    viewCount: number
  }> {
    return videos.map(video => {
      const duration = video.duration || 600 // Default 10 minutes
      const segmentSize = Math.max(30, duration / 10) // 10 segments per video
      
      const segments = []
      for (let i = 0; i < 10; i++) {
        const startTime = i * segmentSize
        const endTime = Math.min((i + 1) * segmentSize, duration)
        
        const segmentCaptions = video.captions.filter(
          cap => cap.start >= startTime && cap.start < endTime
        )
        
        const content = segmentCaptions.map(cap => cap.text).join(' ')
        const wordCount = content.split(/\s+/).length
        
        let position: 'opening' | 'early' | 'middle' | 'late' | 'closing'
        if (i === 0) position = 'opening'
        else if (i <= 2) position = 'early'
        else if (i <= 6) position = 'middle'
        else if (i <= 8) position = 'late'
        else position = 'closing'
        
        segments.push({
          timeRange: `${Math.round(startTime)}s-${Math.round(endTime)}s`,
          content: content.substring(0, 500), // Limit content length
          position,
          wordCount,
          contentDensity: wordCount / (segmentSize / 60) // Words per minute
        })
      }
      
      return {
        title: video.title,
        segments,
        totalDuration: duration,
        viewCount: video.viewCount
      }
    })
  }

  private buildRetentionAnalysisPrompt(
    segmentedContent: Array<{
      title: string
      segments: Array<{
        timeRange: string
        content: string
        position: 'opening' | 'early' | 'middle' | 'late' | 'closing'
        wordCount: number
        contentDensity: number
      }>
      totalDuration: number
      viewCount: number
    }>,
    topic: string,
    targetDuration: string
  ): string {
    const videoAnalysis = segmentedContent.map((video, index) => `
**VIDEO ${index + 1}: "${video.title}"**
Duration: ${Math.round(video.totalDuration / 60)}m | Views: ${video.viewCount?.toLocaleString() || 'Unknown'}

**Segment Analysis:**
${video.segments.map(seg => `
${seg.timeRange} (${seg.position}): ${seg.wordCount} words | Density: ${Math.round(seg.contentDensity)} wpm
Content: "${seg.content.substring(0, 150)}..."
`).join('')}
---`).join('\n')

    return `You are a Retention Engineer AI agent specializing in YouTube audience retention psychology and drop-off pattern analysis. Your expertise is in identifying why viewers leave and implementing retention mechanisms.

# MISSION
Analyze these successful videos about "${topic}" to identify retention patterns, drop-off risks, and optimization opportunities for a ${targetDuration} video.

# VIDEO SEGMENTATION ANALYSIS
${videoAnalysis}

# RETENTION ENGINEERING FRAMEWORK

## 1. DROP-OFF PATTERN IDENTIFICATION
- Analyze content density changes that cause viewer fatigue
- Identify transition points where engagement typically drops
- Map pacing issues that lead to abandonment
- Detect information overload moments

## 2. RETENTION MECHANISM ANALYSIS
- Pattern interrupts and surprise elements
- Callback references and narrative loops
- Content variety and pacing patterns
- Audience engagement touchpoints

## 3. PSYCHOLOGICAL ENGAGEMENT FACTORS
- Curiosity maintenance throughout video
- Value delivery pacing and timing
- Cognitive load management
- Emotional engagement sustainability

## 4. STRUCTURAL RETENTION OPTIMIZATION
- Opening retention (0-30 seconds)
- Early engagement (30 seconds - 2 minutes)
- Middle retention strategies (2 minutes+)
- Closing engagement and retention

# ANALYSIS REQUIREMENTS

Provide comprehensive JSON analysis:

\`\`\`json
{
  "criticalDropOffPoints": [30, 90, 180, 300],
  "retentionPatterns": [
    {
      "timeframe": "0-30 seconds",
      "dropOffPoints": [15, 25],
      "retentionTechniques": ["Hook reinforcement", "Value preview"],
      "effectiveness": 85,
      "psychologyPrinciple": "Commitment escalation",
      "implementation": "Specific implementation strategy"
    }
  ],
  "patternInterrupts": [
    {
      "type": "Content interrupt",
      "timing": 120,
      "description": "Strategic content shift",
      "impactScore": 78,
      "examples": ["Specific examples from videos"]
    }
  ],
  "pacing": {
    "segments": [
      {
        "timeRange": "0-60s",
        "recommendedPace": "fast",
        "contentDensity": "high",
        "transitionType": "Smooth flow"
      }
    ]
  },
  "overallRetentionScore": 82,
  "improvements": {
    "immediateWins": ["Quick retention fixes"],
    "longTermOptimizations": ["Strategic improvements"],
    "avoidancePatterns": ["What not to do"]
  }
}
\`\`\`

Focus on data-driven insights that maximize viewer retention for "${topic}" content. Identify specific time points where engagement typically drops and provide actionable retention strategies.`
  }

  private buildOptimizationPrompt(
    analysis: RetentionAnalysis,
    topic: string,
    style: string,
    duration: string
  ): string {
    return `You are a Retention Engineer creating actionable optimization strategies based on retention analysis data.

# TASK
Generate specific retention optimization strategies for a ${style} video about "${topic}" with ${duration} duration.

# RETENTION ANALYSIS DATA
**Critical Drop-off Points:** ${analysis.criticalDropOffPoints.join(', ')} seconds
**Current Retention Score:** ${analysis.overallRetentionScore}%
**Key Patterns:** ${analysis.retentionPatterns.map(p => p.timeframe + ': ' + p.retentionTechniques.join(', ')).join(' | ')}

**Pattern Interrupts Available:** ${analysis.patternInterrupts.length}
**Immediate Wins:** ${analysis.improvements.immediateWins.join(', ')}

# OPTIMIZATION REQUIREMENTS

## 1. STRUCTURAL OPTIMIZATIONS
- Opening strategy (0-30s): Maximum retention approach
- Middle retention (30s+): Sustained engagement techniques  
- Closing strategy: Strong finish and CTA optimization

## 2. PATTERN INTERRUPT STRATEGY
- Strategic timing for maximum impact
- Variety in interrupt types
- Psychology-based implementation
- Measurable impact predictions

## 3. PACING OPTIMIZATION
- Content density management
- Information flow optimization
- Cognitive load balancing
- Engagement rhythm creation

## 4. RETENTION HACKS
- Proven psychological techniques
- Implementation-ready strategies
- Quick wins and long-term strategies

# OUTPUT FORMAT

\`\`\`json
{
  "structuralOptimizations": {
    "openingStrategy": "Specific 30-second retention strategy",
    "middleRetention": ["Technique 1", "Technique 2", "Technique 3"],
    "closingStrategy": "Strong closing retention approach"
  },
  "patternInterrupts": [
    {
      "timing": "2:30",
      "technique": "Content pattern break",
      "implementation": "Specific implementation steps",
      "expectedImpact": 15
    }
  ],
  "pacingRecommendations": {
    "segments": [
      {
        "timeRange": "0-1:00",
        "strategy": "High-energy opening",
        "content": "Content type and density",
        "transitions": "Transition technique"
      }
    ]
  },
  "retentionHacks": [
    "Psychology-based retention technique 1",
    "Implementation-ready hack 2",
    "Proven strategy 3"
  ]
}
\`\`\`

Create optimizations that will measurably improve retention beyond current ${analysis.overallRetentionScore}% baseline. Focus on implementable strategies with clear psychological backing.`
  }

  private parseRetentionAnalysis(response: string, targetDuration: string): RetentionAnalysis {
    try {
      const cleaned = response.replace(/```json\n?/g, '').replace(/\n?```/g, '').trim()
      const parsed = JSON.parse(cleaned)
      
      if (this.isValidRetentionAnalysis(parsed)) {
        return parsed
      }
    } catch (error) {
      console.log('📝 Parsing retention analysis manually due to JSON error')
    }

    // Fallback manual parsing
    return this.extractRetentionInsights(response, targetDuration)
  }

  private parseOptimizations(response: string): {
    structuralOptimizations: {
      openingStrategy: string
      middleRetention: string[]
      closingStrategy: string
    }
    patternInterrupts: Array<{
      timing: string
      technique: string
      implementation: string
      expectedImpact: number
    }>
    pacingRecommendations: {
      segments: Array<{
        timeRange: string
        strategy: string
        content: string
        transitions: string
      }>
    }
    retentionHacks: string[]
  } {
    try {
      const cleaned = response.replace(/```json\n?/g, '').replace(/\n?```/g, '').trim()
      const parsed = JSON.parse(cleaned)
      
      if (parsed.structuralOptimizations && parsed.retentionHacks) {
        return {
          structuralOptimizations: parsed.structuralOptimizations,
          patternInterrupts: parsed.patternInterrupts || [],
          pacingRecommendations: parsed.pacingRecommendations || { segments: [] },
          retentionHacks: parsed.retentionHacks || []
        }
      }
    } catch (error) {
      console.log('📝 Parsing optimizations manually')
    }

    // Fallback extraction
    const extractList = (text: string, keywords: string[]): string[] => {
      const items: string[] = []
      keywords.forEach(keyword => {
        const regex = new RegExp(`${keyword}[^\\n]*`, 'gi')
        const matches = text.match(regex)
        if (matches) {
          items.push(...matches.map(m => m.replace(/^[^:]*:/, '').trim()))
        }
      })
      return [...new Set(items)].slice(0, 5)
    }

    return {
      structuralOptimizations: {
        openingStrategy: 'High-energy hook with immediate value promise',
        middleRetention: extractList(response, ['middle', 'retention', 'engage']),
        closingStrategy: 'Strong call-to-action with next video tease'
      },
      patternInterrupts: [
        {
          timing: '2:00',
          technique: 'Content break or transition',
          implementation: 'Shift topic or introduce new concept',
          expectedImpact: 12
        }
      ],
      pacingRecommendations: {
        segments: [
          {
            timeRange: '0-1:00',
            strategy: 'Fast-paced opening',
            content: 'High-value information density',
            transitions: 'Smooth content transitions'
          }
        ]
      },
      retentionHacks: extractList(response, ['hack', 'technique', 'strategy', 'tip'])
    }
  }

  private isValidRetentionAnalysis(obj: any): obj is RetentionAnalysis {
    return (
      obj &&
      Array.isArray(obj.criticalDropOffPoints) &&
      Array.isArray(obj.retentionPatterns) &&
      obj.overallRetentionScore &&
      obj.improvements
    )
  }

  private extractRetentionInsights(text: string, targetDuration: string): RetentionAnalysis {
    // Extract critical drop-off points based on common patterns
    const dropOffPoints = [15, 30, 90, 180, 300, 600].filter(point => {
      const durationNum = parseInt(targetDuration.replace(/[^\d]/g, '')) || 5
      const maxSeconds = durationNum * 60
      return point <= maxSeconds
    })

    const extractList = (text: string, keywords: string[]): string[] => {
      const items: string[] = []
      keywords.forEach(keyword => {
        const regex = new RegExp(`${keyword}[^\\n]*`, 'gi')
        const matches = text.match(regex)
        if (matches) {
          items.push(...matches.map(m => m.replace(/^[^:]*:/, '').trim()))
        }
      })
      return [...new Set(items)].slice(0, 8)
    }

    return {
      criticalDropOffPoints: dropOffPoints,
      retentionPatterns: [
        {
          timeframe: '0-30 seconds',
          dropOffPoints: [15, 25],
          retentionTechniques: extractList(text, ['hook', 'opening', 'start']),
          effectiveness: 85,
          psychologyPrinciple: 'Initial commitment',
          implementation: 'Strong hook with value preview'
        },
        {
          timeframe: '30s-2min',
          dropOffPoints: [90, 120],
          retentionTechniques: extractList(text, ['engage', 'interaction', 'question']),
          effectiveness: 78,
          psychologyPrinciple: 'Sustained interest',
          implementation: 'Regular engagement touchpoints'
        }
      ],
      patternInterrupts: [
        {
          type: 'Content shift',
          timing: 120,
          description: 'Strategic topic transition',
          impactScore: 75,
          examples: extractList(text, ['transition', 'change', 'shift'])
        }
      ],
      pacing: {
        segments: [
          {
            timeRange: '0-60s',
            recommendedPace: 'fast',
            contentDensity: 'high',
            transitionType: 'Smooth flow'
          },
          {
            timeRange: '1-3min',
            recommendedPace: 'medium',
            contentDensity: 'medium',
            transitionType: 'Natural progression'
          }
        ]
      },
      overallRetentionScore: 76,
      improvements: {
        immediateWins: extractList(text, ['immediate', 'quick', 'instant']),
        longTermOptimizations: extractList(text, ['long term', 'strategy', 'optimize']),
        avoidancePatterns: extractList(text, ['avoid', 'don\'t', 'never'])
      }
    }
  }
}