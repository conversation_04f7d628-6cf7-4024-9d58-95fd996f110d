/**
 * Types for Video Script Multi-Agent System
 * Defines the state, agents, and interfaces for LangGraph-based video script generation
 */

import { YouTubeService } from '@/lib/youtube-service';

// Based on the YouTubeService implementation
type YouTubeVideo = {
  id: string;
  title: string;
  description: string;
  channelTitle: string;
  viewCount: string;
  likeCount?: string;
  publishedAt: string;
  duration: string;
  thumbnailUrl: string;
};

type YouTubeCaption = {
  text: string;
  start: number;
  duration: number;
};

// Legacy types for backwards compatibility
export interface VideoScriptState {
  // Input parameters
  topic: string;
  userRequirements: {
    tone?: string;
    duration?: number;
    targetAudience?: string;
    specificPoints?: string[];
    style?: string;
  };
  
  // Research phase
  searchQuery: string;
  videos: YouTubeVideo[];
  selectedVideos: string[]; // video IDs
  
  // Caption extraction phase
  captions: Array<{
    videoId: string;
    video: YouTubeVideo;
    captions: YouTubeCaption[];
    transcript: string;
  }>;
  
  // Analysis phase
  videoAnalysis: Array<{
    videoId: string;
    flowPatterns: string[];
    engagementTechniques: string[];
    dataPresentation: string[];
    narrationStyle: string[];
    hookStrategies: string[];
    keyMoments: string[];
  }>;
  
  // Script generation phase
  script: {
    title: string;
    hook: string;
    introduction: string;
    mainContent: string[];
    conclusion: string;
    callToAction: string;
    estimatedDuration: number;
  };
  
  // Web search for additional context
  additionalResearch: Array<{
    query: string;
    results: string[];
    summary: string;
  }>;
  
  // Quality assessment
  qualityScore: number;
  feedback: string[];
  
  // Metadata
  executionTime: number;
  agentDecisions: Array<{
    agent: string;
    decision: string;
    reasoning: string;
    timestamp: number;
  }>;
}

export interface VideoScriptAgent {
  name: string;
  description: string;
  execute(state: VideoScriptState): Promise<Partial<VideoScriptState>>;
}

export interface VideoScriptConfig {
  maxVideos: number;
  maxCaptionsPerVideo: number;
  qualityThreshold: number;
  maxRetries: number;
  searchDepth: number;
}

export interface ScriptGenerationRequest {
  topic: string;
  userRequirements?: {
    tone?: string;
    duration?: number;
    targetAudience?: string;
    specificPoints?: string[];
    style?: string;
  };
  config?: Partial<VideoScriptConfig>;
}