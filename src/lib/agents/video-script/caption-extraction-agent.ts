import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { YouTubeService } from '@/lib/youtube-service';
import { VideoScriptState, YouTubeVideo, YouTubeVideoWithRelevance } from './types';

/**
 * Video Caption Extraction Agent
 * Responsible for extracting captions from YouTube videos using multiple fallback methods
 */
export class CaptionExtractionAgent {
  private youtubeService: YouTubeService;
  private model: ChatOpenAI;

  constructor() {
    this.youtubeService = new YouTubeService();
    this.model = new ChatOpenAI({
      modelName: 'gpt-4o-mini',
      temperature: 0.1,
    });
  }

  /**
   * Execute the caption extraction process
   */
  async execute(state: VideoScriptState): Promise<Partial<VideoScriptState>> {
    console.log('🔍 Starting caption extraction process...');
    
    try {
      // Step 1: Search for videos based on topic
      const searchResults = await this.searchVideos(state.topic);
      
      // Step 2: Extract captions from selected videos
      const captions = await this.extractCaptionsFromVideos(searchResults);
      
      // Step 3: Validate and clean captions
      const validatedCaptions = await this.validateCaptions(captions);
      
      const messages = [
        new AIMessage(`Successfully extracted and validated captions from ${validatedCaptions.length} videos`)
      ];

      return {
        videos: searchResults,
        captions: validatedCaptions,
        messages: [...(state.messages || []), ...messages]
      };
    } catch (error) {
      console.error('Error in caption extraction:', error);
      throw new Error(`Failed to extract captions: ${error}`);
    }
  }

  /**
   * Search for relevant YouTube videos based on topic
   */
  private async searchVideos(topic: string): Promise<YouTubeVideo[]> {
    console.log('🔍 Searching for videos on topic:', topic);
    
    try {
      const searchResults = await this.youtubeService.searchVideos(
        topic || 'educational content',
        5 // Limit to 5 videos for analysis
      );

      const videosWithRelevance = searchResults.videos.map(video => ({
        ...video,
        relevanceScore: this.calculateRelevanceScore(video, topic || '')
      }));

      // Sort by relevance and view count
      videosWithRelevance.sort((a, b) => {
        const scoreA = a.relevanceScore * Math.log10(parseInt(a.viewCount) || 1);
        const scoreB = b.relevanceScore * Math.log10(parseInt(b.viewCount) || 1);
        return scoreB - scoreA;
      });

      console.log(`✅ Found ${videosWithRelevance.length} relevant videos`);
      return videosWithRelevance.slice(0, 3); // Take top 3 most relevant
    } catch (error) {
      console.error('Error searching videos:', error);
      throw new Error(`Failed to search videos: ${error}`);
    }
  }

  /**
   * Extract captions from selected videos
   */
  private async extractCaptionsFromVideos(videos: YouTubeVideo[]): Promise<Array<{
    videoId: string;
    video: YouTubeVideo;
    captions: Array<{ text: string; start: number; duration: number }>;
    transcript: string;
  }>> {
    console.log('🎬 Extracting captions from videos...');
    
    const extractedCaptions = [];

    for (const video of videos) {
      try {
        console.log(`📹 Processing video: ${video.title}`);
        
        const captions = await this.youtubeService.extractCaptions(video.id, 'en');
        const transcript = this.youtubeService.combineCaptions(captions);
        
        console.log(`✅ Extracted ${captions.length} captions from "${video.title}"`);
        
        extractedCaptions.push({
          videoId: video.id,
          video,
          captions,
          transcript
        });
      } catch (error) {
        console.error(`Failed to extract captions for ${video.id}:`, error);
        // Still include video with empty captions to maintain structure
        extractedCaptions.push({
          videoId: video.id,
          video,
          captions: [],
          transcript: ''
        });
      }
    }

    return extractedCaptions;
  }

  /**
   * Validate and clean extracted captions
   */
  private async validateCaptions(captions: Array<{
    videoId: string;
    video: YouTubeVideo;
    captions: Array<{ text: string; start: number; duration: number }>;
    transcript: string;
  }>): Promise<Array<{
    videoId: string;
    video: YouTubeVideo;
    captions: Array<{ text: string; start: number; duration: number }>;
    transcript: string;
  }>> {
    console.log('🔍 Validating extracted captions...');
    
    const validatedCaptions = captions.map(item => {
      // Clean and validate captions
      const cleanedCaptions = item.captions.filter(caption => {
        // Remove empty or very short captions
        if (!caption.text || caption.text.trim().length < 3) return false;
        
        // Remove captions with too many special characters
        const specialCharRatio = (caption.text.match(/[^a-zA-Z0-9\s]/g) || []).length / caption.text.length;
        return specialCharRatio < 0.3;
      });

      // Remove duplicate or very similar captions
      const uniqueCaptions = this.removeDuplicateCaptions(cleanedCaptions);

      return {
        ...item,
        captions: uniqueCaptions,
        transcript: this.youtubeService.combineCaptions(uniqueCaptions)
      };
    });

    // Filter out videos with insufficient captions
    const highQualityCaptions = validatedCaptions.filter(item => 
      item.captions.length > 20 && item.transcript.length > 500
    );

    console.log(`Validated ${validatedCaptions.length} caption sets, ${highQualityCaptions.length} are high quality`);
    return highQualityCaptions;
  }

  /**
   * Calculate relevance score based on title/description match
   */
  private calculateRelevanceScore(video: YouTubeVideo, topic: string): number {
    const topicWords = topic.toLowerCase().split(/\s+/);
    const titleWords = video.title.toLowerCase().split(/\s+/);
    const descriptionWords = video.description.toLowerCase().split(/\s+/);

    let score = 0;
    
    // Calculate matches in title (higher weight)
    topicWords.forEach(word => {
      if (titleWords.includes(word)) {
        score += 2;
      }
    });

    // Calculate matches in description (lower weight)
    topicWords.forEach(word => {
      if (descriptionWords.includes(word)) {
        score += 1;
      }
    });

    return score;
  }

  /**
   * Remove duplicate captions based on text similarity
   */
  private removeDuplicateCaptions(captions: Array<{ text: string; start: number; duration: number }>): Array<{ text: string; start: number; duration: number }> {
    const uniqueCaptions = [];
    const seenTexts = new Set();

    for (const caption of captions) {
      const normalizedText = caption.text.toLowerCase().trim();
      if (!seenTexts.has(normalizedText)) {
        seenTexts.add(normalizedText);
        uniqueCaptions.push(caption);
      }
    }

    return uniqueCaptions;
  }
}