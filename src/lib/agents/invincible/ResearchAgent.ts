import { enhancedTavilySearch } from '@/lib/tools/tavily-search'
import { OpenRouterService } from '@/lib/openrouter'
import { WebSearchResult, AgentConfig } from './types'

export class ResearchAgent {
  private openRouter: OpenRouterService
  private agentName = 'Research Agent'
  private config: AgentConfig

  constructor(config?: Partial<AgentConfig>) {
    this.openRouter = new OpenRouterService()
    this.config = {
      temperature: 0.7,
      maxTokens: 4000,
      model: 'moonshotai/kimi-k2',
      retryAttempts: 3,
      ...config
    }
  }

  async generateSearchQueries(topic: string, targetAudience?: string): Promise<string[]> {
    console.log(`🔍 ${this.agentName}: Generating search queries for "${topic}"`)
    
    const prompt = `You are an expert research strategist. Generate 5-7 diverse search queries to comprehensively research the topic: "${topic}"

${targetAudience ? `Target Audience: ${targetAudience}` : ''}

Requirements:
- Create queries that cover different angles and perspectives
- Include both broad and specific queries
- Consider current trends and 2025 context
- Focus on authoritative and data-driven sources
- Include queries for practical applications and examples

Return ONLY a JSON array of strings, no other text:
["query1", "query2", "query3", ...]`

    try {
      const response = await this.openRouter.generateContent(prompt, {
        temperature: this.config.temperature,
        maxTokens: this.config.maxTokens,
        model: this.config.model
      })

      const queries = JSON.parse(response.trim())
      console.log(`✅ ${this.agentName}: Generated ${queries.length} search queries`)
      return queries
    } catch (error) {
      console.warn(`⚠️ ${this.agentName}: Failed to generate queries, using fallback`)
      return [
        topic,
        `${topic} 2025 trends`,
        `${topic} best practices`,
        `${topic} guide tutorial`,
        `${topic} examples case studies`,
        `${topic} latest research data`
      ]
    }
  }

  async conductWebSearch(queries: string[]): Promise<WebSearchResult[]> {
    console.log(`🌐 ${this.agentName}: Conducting web search with ${queries.length} queries`)
    
    const allResults: WebSearchResult[] = []
    const maxResultsPerQuery = 3

    for (const query of queries) {
      try {
        console.log(`🔍 Searching: "${query}"`)
        
        const searchResults = await enhancedTavilySearch.search(query, {
          maxResults: maxResultsPerQuery,
          searchDepth: 'advanced',
          includeAnswer: true,
          includeRawContent: true,
          temporalFocus: 'current'
        })

        if (searchResults && searchResults.results) {
          const formattedResults: WebSearchResult[] = searchResults.results.map((result: any) => ({
            title: result.title || '',
            url: result.url || '',
            content: result.content || result.raw_content || '',
            snippet: result.content?.substring(0, 300) || '',
            score: result.score || 0.5,
            timestamp: new Date().toISOString()
          }))

          allResults.push(...formattedResults)
          console.log(`✅ Found ${formattedResults.length} results for "${query}"`)
        }

        // Small delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.warn(`⚠️ Search failed for query "${query}":`, error)
        // Continue with other queries
      }
    }

    // Remove duplicates based on URL
    const uniqueResults = allResults.filter((result, index, self) => 
      index === self.findIndex(r => r.url === result.url)
    )

    // Sort by score and limit total results
    const sortedResults = uniqueResults
      .sort((a, b) => b.score - a.score)
      .slice(0, 20) // Limit to top 20 results

    console.log(`✅ ${this.agentName}: Collected ${sortedResults.length} unique web results`)
    return sortedResults
  }

  async enhanceSearchResults(results: WebSearchResult[], topic: string): Promise<WebSearchResult[]> {
    console.log(`🧠 ${this.agentName}: Enhancing search results relevance`)

    const prompt = `You are an expert content analyst. Analyze these search results for the topic "${topic}" and score their relevance.

Search Results:
${results.map((r, i) => `${i + 1}. ${r.title}\nURL: ${r.url}\nSnippet: ${r.snippet}\n`).join('\n')}

For each result, provide a relevance score from 0.0 to 1.0 based on:
- Direct relevance to the topic
- Content quality and authority
- Recency and current relevance
- Practical value for content creation

Return ONLY a JSON array of numbers (scores in order):
[0.9, 0.7, 0.8, ...]`

    try {
      const response = await this.openRouter.generateContent(prompt, {
        temperature: 0.3,
        maxTokens: 1000,
        model: this.config.model
      })

      const scores = JSON.parse(response.trim())
      
      // Apply the enhanced scores
      const enhancedResults = results.map((result, index) => ({
        ...result,
        score: scores[index] || result.score
      }))

      // Re-sort by enhanced scores
      const sortedResults = enhancedResults.sort((a, b) => b.score - a.score)
      
      console.log(`✅ ${this.agentName}: Enhanced relevance scoring completed`)
      return sortedResults
    } catch (error) {
      console.warn(`⚠️ ${this.agentName}: Failed to enhance results, using original scores`)
      return results
    }
  }

  async performResearch(topic: string, targetAudience?: string): Promise<{
    results: WebSearchResult[]
    queries: string[]
  }> {
    console.log(`🚀 ${this.agentName}: Starting comprehensive research for "${topic}"`)
    
    try {
      // Generate search queries
      const queries = await this.generateSearchQueries(topic, targetAudience)
      
      // Conduct web search
      const rawResults = await this.conductWebSearch(queries)
      
      // Enhance results with relevance scoring
      const enhancedResults = await this.enhanceSearchResults(rawResults, topic)
      
      console.log(`✅ ${this.agentName}: Research completed - ${enhancedResults.length} sources found`)
      
      return {
        results: enhancedResults,
        queries
      }
    } catch (error) {
      console.error(`❌ ${this.agentName}: Research failed:`, error)
      throw new Error(`Research failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}
