import { OpenRouterService } from '@/lib/openrouter'
import { GeminiService } from '@/lib/gemini'
import { 
  WebSearchResult, 
  YouTubeVideoData, 
  CompetitiveAnalysis, 
  ContentPattern,
  AgentConfig 
} from './types'

export class AnalysisAgent {
  private openRouter: OpenRouterService
  private gemini: GeminiService
  private agentName = 'Analysis Agent'
  private config: AgentConfig

  constructor(config?: Partial<AgentConfig>) {
    this.openRouter = new OpenRouterService()
    this.gemini = new GeminiService()
    this.config = {
      temperature: 0.3,
      maxTokens: 6000,
      model: 'moonshotai/kimi-k2',
      retryAttempts: 3,
      ...config
    }
  }

  async analyzeWebContent(webResults: WebSearchResult[], topic: string): Promise<Partial<CompetitiveAnalysis>> {
    console.log(`🧠 ${this.agentName}: Analyzing ${webResults.length} web sources for competitive insights`)
    
    const contentSample = webResults.slice(0, 10).map(result => ({
      title: result.title,
      content: result.content.substring(0, 1000), // Limit content length
      url: result.url
    }))

    const prompt = `You are an expert content strategist analyzing competitive content for the topic: "${topic}"

Analyze these web sources and identify:

Content Sources:
${contentSample.map((source, i) => `
${i + 1}. Title: ${source.title}
URL: ${source.url}
Content Preview: ${source.content}
---`).join('\n')}

Provide a comprehensive analysis in JSON format:
{
  "patterns": [
    {
      "type": "content structure pattern",
      "frequency": 0.8,
      "examples": ["example1", "example2"],
      "effectiveness": 0.9
    }
  ],
  "gaps": ["identified content gaps"],
  "opportunities": ["content opportunities"],
  "bestPractices": ["best practices found"],
  "contentTypes": ["article types found"],
  "averageLength": 1500,
  "commonStructures": ["intro-body-conclusion", "listicle", "how-to"]
}

Focus on:
- Content structure patterns
- Writing styles and approaches
- Common topics and angles
- Content gaps and opportunities
- Best practices and effective techniques
- Average content length and depth`

    try {
      const response = await this.openRouter.generateContent(prompt, {
        temperature: this.config.temperature,
        maxTokens: this.config.maxTokens,
        model: this.config.model
      })

      const analysis = JSON.parse(response.trim())
      console.log(`✅ ${this.agentName}: Web content analysis completed`)
      return analysis
    } catch (error) {
      console.warn(`⚠️ ${this.agentName}: Web analysis failed, using fallback`)
      return {
        patterns: [],
        gaps: [`Limited analysis available for ${topic}`],
        opportunities: [`Create comprehensive content about ${topic}`],
        bestPractices: ['Use clear structure', 'Include examples', 'Provide actionable insights'],
        contentTypes: ['article', 'guide'],
        averageLength: 1500,
        commonStructures: ['introduction', 'main content', 'conclusion']
      }
    }
  }

  async analyzeVideoContent(videos: YouTubeVideoData[], topic: string): Promise<{
    videoPatterns: ContentPattern[]
    scriptingInsights: string[]
    engagementTechniques: string[]
  }> {
    console.log(`🎥 ${this.agentName}: Analyzing ${videos.length} YouTube videos for content insights`)
    
    if (videos.length === 0) {
      return {
        videoPatterns: [],
        scriptingInsights: [],
        engagementTechniques: []
      }
    }

    const videoSample = videos.slice(0, 5).map(video => ({
      title: video.title,
      channel: video.channel,
      captions: video.captions.slice(0, 20).map(c => c.text).join(' ').substring(0, 800)
    }))

    const prompt = `You are an expert video content analyst. Analyze these YouTube videos about "${topic}" for content patterns and techniques:

Video Data:
${videoSample.map((video, i) => `
${i + 1}. Title: ${video.title}
Channel: ${video.channel}
Script Sample: ${video.captions}
---`).join('\n')}

Provide analysis in JSON format:
{
  "videoPatterns": [
    {
      "type": "pattern type",
      "frequency": 0.8,
      "examples": ["example1", "example2"],
      "effectiveness": 0.9
    }
  ],
  "scriptingInsights": ["insight1", "insight2"],
  "engagementTechniques": ["technique1", "technique2"]
}

Focus on:
- Common video structures and formats
- Scripting patterns and techniques
- Engagement and retention strategies
- Educational approaches
- Content presentation styles`

    try {
      const response = await this.gemini.generateContent(prompt)
      const analysis = JSON.parse(response.trim())
      console.log(`✅ ${this.agentName}: Video content analysis completed`)
      return analysis
    } catch (error) {
      console.warn(`⚠️ ${this.agentName}: Video analysis failed, using fallback`)
      return {
        videoPatterns: [{
          type: 'educational format',
          frequency: 0.7,
          examples: ['tutorial style', 'explanation format'],
          effectiveness: 0.8
        }],
        scriptingInsights: ['Clear introduction', 'Step-by-step explanation', 'Practical examples'],
        engagementTechniques: ['Hook in first 15 seconds', 'Visual demonstrations', 'Call to action']
      }
    }
  }

  async identifyContentGaps(
    webAnalysis: Partial<CompetitiveAnalysis>, 
    videoAnalysis: any, 
    topic: string
  ): Promise<string[]> {
    console.log(`🔍 ${this.agentName}: Identifying content gaps and opportunities`)

    const prompt = `You are a content strategy expert. Based on the competitive analysis, identify content gaps and opportunities for the topic: "${topic}"

Web Content Analysis:
- Common patterns: ${JSON.stringify(webAnalysis.patterns || [])}
- Existing content types: ${JSON.stringify(webAnalysis.contentTypes || [])}
- Best practices: ${JSON.stringify(webAnalysis.bestPractices || [])}

Video Content Analysis:
- Video patterns: ${JSON.stringify(videoAnalysis.videoPatterns || [])}
- Scripting insights: ${JSON.stringify(videoAnalysis.scriptingInsights || [])}

Identify 5-7 specific content gaps or opportunities that could make our content superior. Consider:
- Missing perspectives or angles
- Underexplored subtopics
- Format innovations
- Depth and comprehensiveness gaps
- Practical application gaps
- Current trends not covered

Return ONLY a JSON array of strings:
["gap1", "gap2", "gap3", ...]`

    try {
      const response = await this.openRouter.generateContent(prompt, {
        temperature: 0.4,
        maxTokens: 2000,
        model: this.config.model
      })

      const gaps = JSON.parse(response.trim())
      console.log(`✅ ${this.agentName}: Identified ${gaps.length} content gaps`)
      return gaps
    } catch (error) {
      console.warn(`⚠️ ${this.agentName}: Gap analysis failed, using fallback`)
      return [
        `Comprehensive 2025 perspective on ${topic}`,
        `Practical implementation examples`,
        `Data-driven insights and statistics`,
        `Step-by-step actionable guidance`,
        `Common mistakes and how to avoid them`
      ]
    }
  }

  async performCompetitiveAnalysis(
    webResults: WebSearchResult[], 
    videos: YouTubeVideoData[], 
    topic: string
  ): Promise<CompetitiveAnalysis> {
    console.log(`🚀 ${this.agentName}: Starting comprehensive competitive analysis for "${topic}"`)
    
    try {
      // Analyze web content
      const webAnalysis = await this.analyzeWebContent(webResults, topic)
      
      // Analyze video content
      const videoAnalysis = await this.analyzeVideoContent(videos, topic)
      
      // Identify content gaps
      const gaps = await this.identifyContentGaps(webAnalysis, videoAnalysis, topic)
      
      // Combine insights
      const analysis: CompetitiveAnalysis = {
        patterns: [
          ...(webAnalysis.patterns || []),
          ...(videoAnalysis.videoPatterns || [])
        ],
        gaps,
        opportunities: [
          ...(webAnalysis.opportunities || []),
          'Integrate video insights into written content',
          'Combine web and video best practices',
          'Create multimedia-informed content structure'
        ],
        bestPractices: [
          ...(webAnalysis.bestPractices || []),
          ...(videoAnalysis.scriptingInsights || []),
          ...(videoAnalysis.engagementTechniques || [])
        ],
        contentTypes: webAnalysis.contentTypes || ['article', 'guide'],
        averageLength: webAnalysis.averageLength || 1500,
        commonStructures: webAnalysis.commonStructures || ['introduction', 'main content', 'conclusion']
      }

      console.log(`✅ ${this.agentName}: Competitive analysis completed`)
      console.log(`📊 Analysis summary:`)
      console.log(`   - ${analysis.patterns.length} content patterns identified`)
      console.log(`   - ${analysis.gaps.length} content gaps found`)
      console.log(`   - ${analysis.opportunities.length} opportunities identified`)
      console.log(`   - ${analysis.bestPractices.length} best practices collected`)
      
      return analysis
    } catch (error) {
      console.error(`❌ ${this.agentName}: Competitive analysis failed:`, error)
      throw new Error(`Competitive analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}
