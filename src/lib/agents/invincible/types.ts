// Invincible .1V Super Agent Types
export interface InvincibleRequest {
  topic: string
  wordCount?: number
  tone?: string
  targetAudience?: string
  includeYouTube?: boolean
  additionalInstructions?: string
}

export interface ResearchData {
  webResults: WebSearchResult[]
  youtubeVideos: YouTubeVideoData[]
  queries: string[]
  totalSources: number
}

export interface WebSearchResult {
  title: string
  url: string
  content: string
  snippet: string
  score: number
  timestamp: string
}

export interface YouTubeVideoData {
  id: string
  title: string
  channel: string
  url: string
  duration: string
  views: number
  publishedTime: string
  captions: CaptionSegment[]
  relevanceScore: number
}

export interface CaptionSegment {
  text: string
  start: number
  duration: number
}

export interface CompetitiveAnalysis {
  patterns: ContentPattern[]
  gaps: string[]
  opportunities: string[]
  bestPractices: string[]
  contentTypes: string[]
  averageLength: number
  commonStructures: string[]
}

export interface ContentPattern {
  type: string
  frequency: number
  examples: string[]
  effectiveness: number
}

export interface GeneratedContent {
  title: string
  content: string
  metadata: ContentMetadata
  sources: SourceReference[]
  youtubeReferences: YouTubeReference[]
}

export interface ContentMetadata {
  wordCount: number
  readingTime: number
  seoScore: number
  topics: string[]
  sentiment: string
  complexity: string
}

export interface SourceReference {
  title: string
  url: string
  relevance: number
  usedInSection: string
}

export interface YouTubeReference {
  title: string
  channel: string
  url: string
  timestamp?: string
  relevance: number
  usedInSection: string
}

export interface ProgressUpdate {
  stage: 'initialization' | 'research' | 'youtube' | 'analysis' | 'writing' | 'completed' | 'error'
  progress: number
  message: string
  currentAgent: string
  data?: {
    queries?: string[]
    webSources?: number
    youtubeVideos?: number
    analysisComplete?: boolean
    wordCount?: number
  }
  timestamp: string
}

export interface AgentConfig {
  temperature: number
  maxTokens: number
  model: string
  retryAttempts: number
}

export interface InvincibleResponse {
  success: boolean
  content?: GeneratedContent
  research?: ResearchData
  analysis?: CompetitiveAnalysis
  error?: string
  processingTime: number
  totalSources: number
}
