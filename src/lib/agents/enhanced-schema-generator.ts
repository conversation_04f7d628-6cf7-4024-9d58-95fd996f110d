/**
 * Enhanced Schema Markup Generator for 2025
 * Incorporates AI-friendly structured data for SEO, AEO, and GEO optimization
 */

import { EnhancedArticlePattern } from '../enhanced-article-patterns-2025';

export interface SchemaConfiguration {
  primarySchema: string;
  additionalSchemas: string[];
  aiOptimization: boolean;
  llmCompatibility: boolean;
  voiceSearchOptimization: boolean;
  featuredSnippetOptimization: boolean;
}

export interface AIOptimizedSchema {
  '@context': string;
  '@type': string | string[];
  '@id'?: string;
  name: string;
  description: string;
  author: AuthorSchema;
  datePublished: string;
  dateModified: string;
  image?: ImageSchema[];
  url?: string;
  mainEntity?: MainEntitySchema;
  about?: AboutSchema[];
  mentions?: MentionSchema[];
  keywords?: string[];
  inLanguage?: string;
  audience?: AudienceSchema;
  // AI-specific enhancements
  aiReadabilityScore?: number;
  semanticTags?: string[];
  entityRelationships?: EntityRelationshipSchema[];
  contentComplexity?: string;
  voiceSearchOptimized?: boolean;
  // Extended schemas
  faqPage?: FAQPageSchema;
  howTo?: HowToSchema;
  article?: ArticleSchema;
  review?: ReviewSchema;
  product?: ProductSchema;
  organization?: OrganizationSchema;
  breadcrumbList?: BreadcrumbListSchema;
  webPage?: WebPageSchema;
  qaPage?: QAPageSchema;
}

export interface AuthorSchema {
  '@type': string;
  name: string;
  url?: string;
  sameAs?: string[];
  jobTitle?: string;
  worksFor?: OrganizationSchema;
  description?: string;
  image?: string;
  knowsAbout?: string[];
  expertise?: string[];
}

export interface ImageSchema {
  '@type': string;
  url: string;
  width?: number;
  height?: number;
  caption?: string;
  description?: string;
  contentUrl?: string;
  thumbnail?: string;
  representativeOfPage?: boolean;
}

export interface MainEntitySchema {
  '@type': string;
  name: string;
  description: string;
  url?: string;
  sameAs?: string[];
  additionalType?: string;
  identifier?: string;
  category?: string;
}

export interface AboutSchema {
  '@type': string;
  name: string;
  description?: string;
  url?: string;
  sameAs?: string[];
}

export interface MentionSchema {
  '@type': string;
  name: string;
  url?: string;
  description?: string;
}

export interface AudienceSchema {
  '@type': string;
  audienceType: string;
  name?: string;
  description?: string;
  geographicArea?: string;
}

export interface EntityRelationshipSchema {
  subject: string;
  predicate: string;
  object: string;
  confidence: number;
}

export interface FAQPageSchema {
  '@type': string;
  mainEntity: {
    '@type': string;
    name: string;
    acceptedAnswer: {
      '@type': string;
      text: string;
    };
  }[];
}

export interface HowToSchema {
  '@type': string;
  name: string;
  description: string;
  totalTime?: string;
  estimatedCost?: MonetaryAmountSchema;
  tool?: string[];
  supply?: string[];
  step: HowToStepSchema[];
}

export interface HowToStepSchema {
  '@type': string;
  name: string;
  text: string;
  url?: string;
  image?: ImageSchema;
  tool?: string[];
  supply?: string[];
}

export interface ArticleSchema {
  '@type': string;
  headline: string;
  alternativeHeadline?: string;
  description: string;
  image: ImageSchema[];
  datePublished: string;
  dateModified: string;
  author: AuthorSchema;
  publisher: OrganizationSchema;
  mainEntityOfPage: string;
  articleSection?: string;
  wordCount?: number;
  articleBody?: string;
  speakable?: SpeakableSchema;
}

export interface SpeakableSchema {
  '@type': string;
  cssSelector?: string[];
  xpath?: string[];
}

export interface ReviewSchema {
  '@type': string;
  itemReviewed: ReviewItemSchema;
  author: AuthorSchema;
  datePublished: string;
  description: string;
  name: string;
  reviewBody: string;
  reviewRating: RatingSchema;
}

export interface ReviewItemSchema {
  '@type': string;
  name: string;
  description?: string;
  image?: ImageSchema;
  brand?: OrganizationSchema;
  category?: string;
}

export interface RatingSchema {
  '@type': string;
  ratingValue: number;
  bestRating: number;
  worstRating?: number;
}

export interface ProductSchema {
  '@type': string;
  name: string;
  description: string;
  image: ImageSchema[];
  brand: OrganizationSchema;
  category: string;
  offers: OfferSchema;
  aggregateRating?: AggregateRatingSchema;
  review?: ReviewSchema[];
  sku?: string;
  gtin?: string;
  mpn?: string;
}

export interface OfferSchema {
  '@type': string;
  price: string;
  priceCurrency: string;
  availability: string;
  validFrom?: string;
  validThrough?: string;
  seller: OrganizationSchema;
}

export interface AggregateRatingSchema {
  '@type': string;
  ratingValue: number;
  reviewCount: number;
  bestRating: number;
  worstRating?: number;
}

export interface OrganizationSchema {
  '@type': string;
  name: string;
  url?: string;
  logo?: ImageSchema;
  description?: string;
  sameAs?: string[];
  contactPoint?: ContactPointSchema[];
  address?: PostalAddressSchema;
  foundingDate?: string;
  expertise?: string[];
}

export interface ContactPointSchema {
  '@type': string;
  telephone?: string;
  contactType: string;
  email?: string;
  areaServed?: string;
}

export interface PostalAddressSchema {
  '@type': string;
  streetAddress?: string;
  addressLocality?: string;
  addressRegion?: string;
  postalCode?: string;
  addressCountry?: string;
}

export interface MonetaryAmountSchema {
  '@type': string;
  currency: string;
  value: number;
}

export interface BreadcrumbListSchema {
  '@type': string;
  itemListElement: ListItemSchema[];
}

export interface ListItemSchema {
  '@type': string;
  position: number;
  name: string;
  item: string;
}

export interface WebPageSchema {
  '@type': string;
  name: string;
  description: string;
  url: string;
  mainEntity: string;
  breadcrumb?: BreadcrumbListSchema;
  lastReviewed?: string;
  reviewedBy?: AuthorSchema;
  significantLink?: string[];
  speakable?: SpeakableSchema;
}

export interface QAPageSchema {
  '@type': string;
  mainEntity: {
    '@type': string;
    name: string;
    text: string;
    answerCount: number;
    upvoteCount?: number;
    dateCreated: string;
    author: AuthorSchema;
    acceptedAnswer: {
      '@type': string;
      text: string;
      dateCreated: string;
      upvoteCount?: number;
      author: AuthorSchema;
    };
    suggestedAnswer?: {
      '@type': string;
      text: string;
      dateCreated: string;
      upvoteCount?: number;
      author: AuthorSchema;
    }[];
  };
}

export class EnhancedSchemaGenerator {
  private baseUrl: string;
  private organizationInfo: OrganizationSchema;
  private currentDate: string;

  constructor(baseUrl: string = '', organizationInfo?: Partial<OrganizationSchema>) {
    this.baseUrl = baseUrl;
    this.currentDate = new Date().toISOString();
    this.organizationInfo = {
      '@type': 'Organization',
      name: organizationInfo?.name || 'AI Content Platform',
      url: organizationInfo?.url || this.baseUrl,
      description: organizationInfo?.description || 'AI-powered content generation platform',
      expertise: organizationInfo?.expertise || ['Content Creation', 'SEO Optimization', 'AI Technology'],
      ...organizationInfo
    };
  }

  /**
   * Generate comprehensive schema markup for content
   */
  generateSchemaMarkup(
    topic: string,
    content: string,
    pattern: EnhancedArticlePattern,
    config: Partial<SchemaConfiguration> = {}
  ): AIOptimizedSchema {
    const configuration: SchemaConfiguration = {
      primarySchema: pattern.schemaMarkup.primarySchema,
      additionalSchemas: pattern.schemaMarkup.additionalSchemas,
      aiOptimization: true,
      llmCompatibility: true,
      voiceSearchOptimization: true,
      featuredSnippetOptimization: true,
      ...config
    };

    const baseSchema = this.generateBaseSchema(topic, content, pattern);
    const enhancedSchema = this.addAIOptimizations(baseSchema, pattern, configuration);
    const extendedSchema = this.addExtendedSchemas(enhancedSchema, topic, content, pattern, configuration);

    return extendedSchema;
  }

  /**
   * Generate base schema structure
   */
  private generateBaseSchema(
    topic: string,
    content: string,
    pattern: EnhancedArticlePattern
  ): AIOptimizedSchema {
    const wordCount = content.split(' ').length;
    const contentComplexity = this.determineContentComplexity(content);

    return {
      '@context': 'https://schema.org',
      '@type': pattern.schemaMarkup.primarySchema,
      '@id': `${this.baseUrl}/#${pattern.schemaMarkup.primarySchema.toLowerCase()}`,
      name: this.generateSchemaTitle(topic, pattern),
      description: this.generateSchemaDescription(topic, pattern),
      author: this.generateAuthorSchema(),
      datePublished: this.currentDate,
      dateModified: this.currentDate,
      url: `${this.baseUrl}/${this.slugify(topic)}`,
      inLanguage: 'en-US',
      keywords: this.extractKeywords(topic, content),
      mainEntity: this.generateMainEntitySchema(topic),
      about: this.generateAboutSchemas(topic),
      audience: this.generateAudienceSchema(pattern),
      // AI-specific enhancements
      aiReadabilityScore: this.calculateAIReadabilityScore(content),
      semanticTags: this.generateSemanticTags(topic, content),
      entityRelationships: this.generateEntityRelationships(topic),
      contentComplexity,
      voiceSearchOptimized: true
    };
  }

  /**
   * Add AI-specific optimizations to schema
   */
  private addAIOptimizations(
    schema: AIOptimizedSchema,
    pattern: EnhancedArticlePattern,
    config: SchemaConfiguration
  ): AIOptimizedSchema {
    if (!config.aiOptimization) return schema;

    // Add speakable markup for voice search
    if (config.voiceSearchOptimization) {
      schema.article = {
        ...schema.article,
        speakable: {
          '@type': 'SpeakableSpecification',
          cssSelector: ['h1', 'h2', '.summary', '.key-points'],
          xpath: ['/html/head/title', '//h1', '//h2[1]']
        }
      } as ArticleSchema;
    }

    // Add image schemas with AI-friendly descriptions
    schema.image = this.generateImageSchemas(schema.name);

    // Add mentions for entity recognition
    schema.mentions = this.generateMentionSchemas(schema.name);

    return schema;
  }

  /**
   * Add extended schemas based on pattern type
   */
  private addExtendedSchemas(
    schema: AIOptimizedSchema,
    topic: string,
    content: string,
    pattern: EnhancedArticlePattern,
    config: SchemaConfiguration
  ): AIOptimizedSchema {
    // Add FAQ schema for question-based content
    if (pattern.schemaMarkup.additionalSchemas.includes('FAQPage')) {
      schema.faqPage = this.generateFAQSchema(topic, content);
    }

    // Add HowTo schema for instructional content
    if (pattern.schemaMarkup.additionalSchemas.includes('HowTo')) {
      schema.howTo = this.generateHowToSchema(topic, content);
    }

    // Add Article schema with enhanced metadata
    if (pattern.schemaMarkup.additionalSchemas.includes('Article')) {
      schema.article = this.generateArticleSchema(topic, content, schema);
    }

    // Add Review schema for comparison content
    if (pattern.schemaMarkup.additionalSchemas.includes('Review')) {
      schema.review = this.generateReviewSchema(topic, content);
    }

    // Add Product schema for product-related content
    if (pattern.schemaMarkup.additionalSchemas.includes('Product')) {
      schema.product = this.generateProductSchema(topic);
    }

    // Add WebPage schema
    if (pattern.schemaMarkup.additionalSchemas.includes('WebPage')) {
      schema.webPage = this.generateWebPageSchema(topic, schema);
    }

    // Add QAPage schema for Q&A content
    if (pattern.schemaMarkup.additionalSchemas.includes('QAPage')) {
      schema.qaPage = this.generateQAPageSchema(topic, content);
    }

    // Add BreadcrumbList for navigation
    schema.breadcrumbList = this.generateBreadcrumbSchema(topic);

    return schema;
  }

  /**
   * Generate FAQ schema optimized for AEO
   */
  private generateFAQSchema(topic: string, content: string): FAQPageSchema {
    const questions = this.extractQuestions(content);
    
    return {
      '@type': 'FAQPage',
      mainEntity: questions.map(q => ({
        '@type': 'Question',
        name: q.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: q.answer
        }
      }))
    };
  }

  /**
   * Generate HowTo schema for instructional content
   */
  private generateHowToSchema(topic: string, content: string): HowToSchema {
    const steps = this.extractSteps(content);
    
    return {
      '@type': 'HowTo',
      name: `How to ${topic}`,
      description: `Step-by-step guide for ${topic}`,
      totalTime: 'PT30M',
      step: steps.map((step, index) => ({
        '@type': 'HowToStep',
        name: `Step ${index + 1}: ${step.title}`,
        text: step.description,
        url: `${this.baseUrl}/${this.slugify(topic)}#step-${index + 1}`
      }))
    };
  }

  /**
   * Generate Article schema with AI enhancements
   */
  private generateArticleSchema(topic: string, content: string, baseSchema: AIOptimizedSchema): ArticleSchema {
    return {
      '@type': 'Article',
      headline: baseSchema.name,
      alternativeHeadline: `Complete Guide to ${topic}`,
      description: baseSchema.description,
      image: baseSchema.image || [],
      datePublished: baseSchema.datePublished,
      dateModified: baseSchema.dateModified,
      author: baseSchema.author,
      publisher: this.organizationInfo,
      mainEntityOfPage: baseSchema.url || '',
      articleSection: this.determineArticleSection(topic),
      wordCount: content.split(' ').length,
      articleBody: content.substring(0, 500) + '...',
      speakable: {
        '@type': 'SpeakableSpecification',
        cssSelector: ['h1', 'h2', '.summary']
      }
    };
  }

  /**
   * Generate Review schema for comparison content
   */
  private generateReviewSchema(topic: string, content: string): ReviewSchema {
    return {
      '@type': 'Review',
      itemReviewed: {
        '@type': 'Thing',
        name: topic,
        description: `Comprehensive review of ${topic}`
      },
      author: this.generateAuthorSchema(),
      datePublished: this.currentDate,
      description: `Expert review and analysis of ${topic}`,
      name: `${topic} Review`,
      reviewBody: content.substring(0, 300) + '...',
      reviewRating: {
        '@type': 'Rating',
        ratingValue: 4.5,
        bestRating: 5,
        worstRating: 1
      }
    };
  }

  /**
   * Generate Product schema for product-related content
   */
  private generateProductSchema(topic: string): ProductSchema {
    return {
      '@type': 'Product',
      name: topic,
      description: `Comprehensive information about ${topic}`,
      image: this.generateImageSchemas(topic),
      brand: this.organizationInfo,
      category: 'Information Resource',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock',
        seller: this.organizationInfo
      }
    };
  }

  /**
   * Generate WebPage schema
   */
  private generateWebPageSchema(topic: string, baseSchema: AIOptimizedSchema): WebPageSchema {
    return {
      '@type': 'WebPage',
      name: baseSchema.name,
      description: baseSchema.description,
      url: baseSchema.url || '',
      mainEntity: baseSchema.url || '',
      breadcrumb: this.generateBreadcrumbSchema(topic),
      lastReviewed: this.currentDate,
      reviewedBy: baseSchema.author,
      speakable: {
        '@type': 'SpeakableSpecification',
        cssSelector: ['h1', 'h2', '.key-points']
      }
    };
  }

  /**
   * Generate QAPage schema for Q&A content
   */
  private generateQAPageSchema(topic: string, content: string): QAPageSchema {
    const mainQuestion = `What is ${topic}?`;
    const mainAnswer = content.substring(0, 200) + '...';

    return {
      '@type': 'QAPage',
      mainEntity: {
        '@type': 'Question',
        name: mainQuestion,
        text: mainQuestion,
        answerCount: 1,
        upvoteCount: 10,
        dateCreated: this.currentDate,
        author: this.generateAuthorSchema(),
        acceptedAnswer: {
          '@type': 'Answer',
          text: mainAnswer,
          dateCreated: this.currentDate,
          upvoteCount: 15,
          author: this.generateAuthorSchema()
        }
      }
    };
  }

  /**
   * Generate BreadcrumbList schema
   */
  private generateBreadcrumbSchema(topic: string): BreadcrumbListSchema {
    return {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: this.baseUrl
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Articles',
          item: `${this.baseUrl}/articles`
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: topic,
          item: `${this.baseUrl}/${this.slugify(topic)}`
        }
      ]
    };
  }

  /**
   * Helper methods for schema generation
   */
  private generateAuthorSchema(): AuthorSchema {
    return {
      '@type': 'Person',
      name: 'AI Content Expert',
      jobTitle: 'Content Strategist',
      worksFor: this.organizationInfo,
      description: 'Expert in AI-powered content creation and optimization',
      knowsAbout: ['Content Strategy', 'SEO Optimization', 'AI Technology'],
      expertise: ['Technical Writing', 'Digital Marketing', 'AI Implementation']
    };
  }

  private generateSchemaTitle(topic: string, pattern: EnhancedArticlePattern): string {
    const titleFormulas = pattern.seoOptimization.titleFormulas;
    return titleFormulas[0].replace(/\[.*?\]/g, topic);
  }

  private generateSchemaDescription(topic: string, pattern: EnhancedArticlePattern): string {
    return pattern.seoOptimization.metaDescription
      .replace(/\[.*?\]/g, topic)
      .substring(0, 160);
  }

  private generateMainEntitySchema(topic: string): MainEntitySchema {
    return {
      '@type': 'Thing',
      name: topic,
      description: `Comprehensive information about ${topic}`,
      additionalType: 'CreativeWork',
      category: 'Educational Content'
    };
  }

  private generateAboutSchemas(topic: string): AboutSchema[] {
    return [
      {
        '@type': 'Thing',
        name: topic,
        description: `Main topic: ${topic}`
      },
      {
        '@type': 'Thing',
        name: 'Content Strategy',
        description: 'Strategic content development'
      }
    ];
  }

  private generateAudienceSchema(pattern: EnhancedArticlePattern): AudienceSchema {
    return {
      '@type': 'Audience',
      audienceType: 'General Audience',
      name: 'Content Seekers',
      description: 'Individuals seeking comprehensive information and guidance'
    };
  }

  private generateImageSchemas(topic: string): ImageSchema[] {
    return [
      {
        '@type': 'ImageObject',
        url: `${this.baseUrl}/images/${this.slugify(topic)}-hero.jpg`,
        width: 1200,
        height: 630,
        caption: `${topic} overview image`,
        description: `Visual representation of ${topic}`,
        representativeOfPage: true
      }
    ];
  }

  private generateMentionSchemas(topic: string): MentionSchema[] {
    return [
      {
        '@type': 'Thing',
        name: 'Technology',
        description: 'Technology and innovation'
      },
      {
        '@type': 'Thing',
        name: 'Best Practices',
        description: 'Industry best practices and standards'
      }
    ];
  }

  private generateSemanticTags(topic: string, content: string): string[] {
    return [
      topic.toLowerCase(),
      'guide',
      'tutorial',
      'information',
      'education',
      'best-practices',
      'implementation',
      'strategy'
    ];
  }

  private generateEntityRelationships(topic: string): EntityRelationshipSchema[] {
    return [
      {
        subject: topic,
        predicate: 'isRelatedTo',
        object: 'technology',
        confidence: 0.8
      },
      {
        subject: topic,
        predicate: 'hasCategory',
        object: 'education',
        confidence: 0.9
      }
    ];
  }

  private extractKeywords(topic: string, content: string): string[] {
    const keywords = [topic.toLowerCase()];
    const commonTerms = ['guide', 'tutorial', 'how to', 'best practices', 'implementation'];
    
    commonTerms.forEach(term => {
      if (content.toLowerCase().includes(term)) {
        keywords.push(term);
      }
    });

    return keywords.slice(0, 10);
  }

  private extractQuestions(content: string): { question: string; answer: string }[] {
    // Simple extraction logic - in production, this would be more sophisticated
    const questions = content.match(/.*\?/g) || [];
    return questions.slice(0, 5).map((q, index) => ({
      question: q.trim(),
      answer: `Answer to ${q.trim().substring(0, 50)}...`
    }));
  }

  private extractSteps(content: string): { title: string; description: string }[] {
    // Simple extraction logic - in production, this would be more sophisticated
    const steps = [];
    for (let i = 1; i <= 5; i++) {
      steps.push({
        title: `Step ${i}`,
        description: `Description for step ${i} of the process`
      });
    }
    return steps;
  }

  private calculateAIReadabilityScore(content: string): number {
    const wordCount = content.split(' ').length;
    const sentenceCount = content.split(/[.!?]+/).length;
    const avgWordsPerSentence = wordCount / sentenceCount;
    
    // Simple readability calculation
    if (avgWordsPerSentence < 15) return 0.9;
    if (avgWordsPerSentence < 20) return 0.7;
    return 0.5;
  }

  private determineContentComplexity(content: string): string {
    const technicalTerms = content.match(/\b(implementation|methodology|optimization|integration)\b/gi) || [];
    
    if (technicalTerms.length > 10) return 'high';
    if (technicalTerms.length > 5) return 'medium';
    return 'low';
  }

  private determineArticleSection(topic: string): string {
    const topicLower = topic.toLowerCase();
    
    if (topicLower.includes('technology')) return 'Technology';
    if (topicLower.includes('business')) return 'Business';
    if (topicLower.includes('tutorial')) return 'Education';
    
    return 'General';
  }

  private slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  }

  /**
   * Generate JSON-LD markup string
   */
  generateJSONLD(schema: AIOptimizedSchema): string {
    return JSON.stringify(schema, null, 2);
  }

  /**
   * Generate HTML script tag with JSON-LD
   */
  generateJSONLDScript(schema: AIOptimizedSchema): string {
    return `<script type="application/ld+json">
${this.generateJSONLD(schema)}
</script>`;
  }

  /**
   * Validate schema markup
   */
  validateSchema(schema: AIOptimizedSchema): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!schema['@context']) errors.push('Missing @context');
    if (!schema['@type']) errors.push('Missing @type');
    if (!schema.name) errors.push('Missing name');
    if (!schema.description) errors.push('Missing description');
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
} 