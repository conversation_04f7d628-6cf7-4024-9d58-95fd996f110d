import * as cheerio from 'cheerio';
import TurndownService from 'turndown';
import axios from 'axios';

export interface ScrapedContent {
  url: string;
  title: string;
  content: string;
  markdown: string;
  html: string;
  success: boolean;
  metadata?: {
    description?: string;
    author?: string;
    publishDate?: string;
    keywords?: string[];
    ogTitle?: string;
    ogDescription?: string;
    ogImage?: string;
  };
  statistics?: string[];
  keyInsights?: string[];
  wordCount: number;
  links: string[];
  images: string[];
  error?: string;
}

export interface ScrapingConfig {
  timeout?: number;
  waitForSelector?: string;
  extractImages?: boolean;
  extractLinks?: boolean;
  onlyMainContent?: boolean;
  javascript?: boolean;
  userAgent?: string;
  viewport?: { width: number; height: number };
  headers?: Record<string, string>;
  stealth?: boolean;
  maxLength?: number;
}

export class NodeWebScraperService {
  private turndownService: any;
  private defaultConfig: ScrapingConfig = {
    timeout: 15000,
    extractImages: false,
    extractLinks: true,
    onlyMainContent: true,
    javascript: false, // Not needed for cheerio
    stealth: false, // Not applicable for axios
    maxLength: 8000,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    headers: {
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
      'Accept-Language': 'en-US,en;q=0.5',
      'Accept-Encoding': 'gzip, deflate',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
    }
  };

  constructor() {
    try {
      this.turndownService = new TurndownService({
        headingStyle: 'atx',
        codeBlockStyle: 'fenced',
        emDelimiter: '_',
      });
      
      // Configure turndown rules for better markdown
      this.turndownService.addRule('removeScripts', {
        filter: ['script', 'style', 'noscript'],
        replacement: () => ''
      });
    } catch (error) {
      console.error('Error initializing TurndownService:', error);
      // Create a fallback service
      this.turndownService = (html: string) => html.replace(/<[^>]*>/g, '');
      this.turndownService.addRule = () => {};
    }
  }

  async initialize(): Promise<void> {
    // No initialization needed for cheerio-based scraper
    console.log('✅ Simple web scraper ready (no browser needed)');
  }

  async scrapeUrl(url: string, config?: Partial<ScrapingConfig>): Promise<ScrapedContent> {
    const mergedConfig = { ...this.defaultConfig, ...config };
    
    try {
      console.log(`🔍 Scraping: ${url}`);
      
      // Check if URL is a PDF and skip it
      if (this.isPdfUrl(url)) {
        console.log(`⏭️ Skipping PDF file: ${url}`);
        return {
          url,
          title: 'PDF Document (Skipped)',
          content: 'PDF documents are not supported by the web scraper',
          markdown: '',
          html: '',
          success: false,
          wordCount: 0,
          links: [],
          images: [],
          error: 'PDF files are not supported for scraping',
        };
      }
      
      // Make HTTP request with axios with enhanced error handling
      const response = await Promise.race([
        axios.get(url, {
          timeout: mergedConfig.timeout,
          headers: {
            'User-Agent': mergedConfig.userAgent,
            ...mergedConfig.headers,
          },
          maxRedirects: 5,
          validateStatus: (status) => status < 500, // Accept redirects and client errors
          // Add these for better compatibility
          withCredentials: false,
          responseType: 'text',
          maxContentLength: 10 * 1024 * 1024, // 10MB max
          maxBodyLength: 10 * 1024 * 1024, // 10MB max
        }),
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error(`Request timeout after ${mergedConfig.timeout}ms`)), mergedConfig.timeout! + 5000);
        })
      ]) as any;
      
      // Check if we got a valid response
      if (!response || !response.data) {
        throw new Error('No response data received');
      }

      // Load HTML with cheerio
      const $ = cheerio.load(response.data);
      
      // Extract title
      const title = $('title').text().trim() || 
                   $('h1').first().text().trim() || 
                   $('meta[property="og:title"]').attr('content') || 
                   'Extracted Content';

      // Remove unwanted elements
      $('script, style, noscript, nav, header, footer, aside, .advertisement, .ads, .cookie-banner, .popup, .modal, .social-share').remove();
      
      // Extract main content
      let mainContent = '';
      let htmlContent = '';
      
      if (mergedConfig.onlyMainContent) {
        // Try to find main content areas with priority
        const contentSelectors = [
          'article',
          '[role="main"]',
          'main',
          '.content',
          '.main-content',
          '.post-content',
          '.entry-content',
          '.article-content',
          '.article-body',
          '.post-body',
          '.story-body',
          '#content',
          '.page-content'
        ];
        
        for (const selector of contentSelectors) {
          const element = $(selector);
          if (element.length > 0) {
            const elementText = element.text().trim();
            const elementHtml = element.html() || '';
            if (elementText.length > mainContent.length && elementText.length > 200) {
              mainContent = elementText;
              htmlContent = elementHtml;
            }
          }
        }
        
        // Fallback strategy: find largest text block
        if (!mainContent || mainContent.length < 200) {
          $('div, section, p').each((_, element) => {
            const elementText = $(element).text().trim();
            const elementHtml = $(element).html() || '';
            if (elementText.length > mainContent.length && elementText.length > 100) {
              mainContent = elementText;
              htmlContent = elementHtml;
            }
          });
        }
        
        // Final fallback to body if no main content found
        if (!mainContent) {
          mainContent = $('body').text().trim();
          htmlContent = $('body').html() || '';
        }
      } else {
        mainContent = $('body').text().trim();
        htmlContent = $('body').html() || '';
      }

      // Extract metadata
      const metadata = this.extractMetadata($);
      
      // Extract statistics and insights
      const statistics = this.extractStatistics($);
      const keyInsights = this.extractKeyInsights($);
      
      // Extract links and images
      const links = mergedConfig.extractLinks ? this.extractLinks($, url) : [];
      const images = mergedConfig.extractImages ? this.extractImages($, url) : [];
      
      // Clean content text
      const cleanContent = this.cleanText(mainContent);
      
      // Limit content length if specified
      const finalContent = mergedConfig.maxLength && cleanContent.length > mergedConfig.maxLength
        ? cleanContent.substring(0, mergedConfig.maxLength) + '...'
        : cleanContent;
      
      // Convert to markdown
      let markdown = '';
      try {
        markdown = this.turndownService(htmlContent);
      } catch (error) {
        console.warn('TurndownService failed, using fallback HTML-to-text conversion');
        markdown = htmlContent.replace(/<[^>]*>/g, '');
      }
      
      const wordCount = finalContent.split(/\s+/).filter(w => w.length > 0).length;
      
      console.log(`✅ Successfully scraped: ${url} (${wordCount} words, ${finalContent.length} chars)`);
      
      return {
        url,
        title,
        content: finalContent,
        markdown,
        html: htmlContent,
        success: true,
        metadata,
        statistics,
        keyInsights,
        wordCount,
        links,
        images,
      };
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Failed to scrape ${url}:`, errorMessage);
      
      // Check if this is a known problematic domain
      if (this.isProblematicDomain(url)) {
        console.log(`⚠️ Skipping problematic domain: ${url}`);
        return {
          url,
          title: this.extractDomainTitle(url),
          content: `Content from ${url} is not accessible due to anti-bot measures. This URL was identified but could not be scraped.`,
          markdown: `# ${this.extractDomainTitle(url)}\n\nContent from ${url} is not accessible due to anti-bot measures.`,
          html: `<h1>${this.extractDomainTitle(url)}</h1><p>Content from ${url} is not accessible due to anti-bot measures.</p>`,
          success: false,
          wordCount: 0,
          links: [],
          images: [],
          error: `Blocked by anti-bot measures: ${errorMessage}`,
        };
      }
      
      return {
        url,
        title: '',
        content: '',
        markdown: '',
        html: '',
        success: false,
        wordCount: 0,
        links: [],
        images: [],
        error: `Failed to scrape ${url}: ${errorMessage}`,
      };
    }
  }

  async scrapeMultipleUrls(urls: string[], config?: Partial<ScrapingConfig>): Promise<ScrapedContent[]> {
    const results: ScrapedContent[] = [];
    const concurrency = 3; // Limit concurrent requests
    
    console.log(`🚀 Fast scraping ${urls.length} URLs (concurrency: ${concurrency})`);
    
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency);
      console.log(`📦 Processing batch ${Math.floor(i / concurrency) + 1}/${Math.ceil(urls.length / concurrency)}`);
      
      const batchPromises = batch.map(url => this.scrapeUrl(url, config));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Add delay between batches to be polite
      if (i + concurrency < urls.length) {
        console.log('⏳ Waiting 800ms between batches...');
        await new Promise(resolve => setTimeout(resolve, 800));
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    console.log(`✅ Fast scraping completed: ${successCount}/${urls.length} successful`);
    
    return results;
  }

  private extractMetadata($: cheerio.Root): ScrapedContent['metadata'] {
    return {
      description: $('meta[name="description"]').attr('content') || 
                  $('meta[property="og:description"]').attr('content'),
      author: $('meta[name="author"]').attr('content') || 
             $('[rel="author"]').text().trim(),
      publishDate: $('meta[property="article:published_time"]').attr('content') ||
                  $('time').attr('datetime') ||
                  $('[datetime]').attr('datetime'),
      keywords: $('meta[name="keywords"]').attr('content')?.split(',').map(k => k.trim()),
      ogTitle: $('meta[property="og:title"]').attr('content'),
      ogDescription: $('meta[property="og:description"]').attr('content'),
      ogImage: $('meta[property="og:image"]').attr('content'),
    };
  }

  private extractStatistics($: cheerio.Root): string[] {
    const stats: string[] = [];
    
    // Look for numbers that might be statistics
    $('*').each((_, element) => {
      const text = $(element).text();
      const numberMatches = text.match(/\b\d{1,3}(,\d{3})*(\.\d+)?(%|k|M|B|million|billion|thousand)?\b/g);
      
      if (numberMatches) {
        numberMatches.forEach(match => {
          const context = text.substring(
            Math.max(0, text.indexOf(match) - 30),
            text.indexOf(match) + match.length + 30
          ).trim();
          
          if (context.length > 10 && !stats.includes(context)) {
            stats.push(context);
          }
        });
      }
    });
    
    return stats.slice(0, 10); // Limit to top 10 statistics
  }

  private extractKeyInsights($: cheerio.Root): string[] {
    const insights: string[] = [];
    
    // Look for highlighted text, quotes, or important statements
    const selectors = [
      'blockquote',
      '.highlight',
      '.important',
      '.key-point',
      'strong',
      'b',
      'em',
      'i'
    ];
    
    selectors.forEach(selector => {
      $(selector).each((_, element) => {
        const text = $(element).text().trim();
        if (text.length > 20 && text.length < 200 && !insights.includes(text)) {
          insights.push(text);
        }
      });
    });
    
    return insights.slice(0, 15); // Limit to top 15 insights
  }

  private extractLinks($: cheerio.Root, baseUrl: string): string[] {
    const links: string[] = [];
    
    $('a[href]').each((_, element) => {
      const href = $(element).attr('href');
      if (href) {
        try {
          const absoluteUrl = new URL(href, baseUrl).toString();
          if (!links.includes(absoluteUrl)) {
            links.push(absoluteUrl);
          }
        } catch {
          // Invalid URL, skip
        }
      }
    });
    
    return links.slice(0, 50); // Limit to prevent memory issues
  }

  private extractImages($: cheerio.Root, baseUrl: string): string[] {
    const images: string[] = [];
    
    $('img[src]').each((_, element) => {
      const src = $(element).attr('src');
      if (src) {
        try {
          const absoluteUrl = new URL(src, baseUrl).toString();
          if (!images.includes(absoluteUrl)) {
            images.push(absoluteUrl);
          }
        } catch {
          // Invalid URL, skip
        }
      }
    });
    
    return images.slice(0, 20); // Limit to prevent memory issues
  }

  private cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/\n\s*\n/g, '\n\n') // Clean up excessive line breaks
      .trim();
  }

  private isPdfUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      // Check if URL ends with .pdf
      if (urlObj.pathname.toLowerCase().endsWith('.pdf')) {
        return true;
      }
      // Check if URL contains common PDF indicators
      if (url.toLowerCase().includes('.pdf')) {
        return true;
      }
      return false;
    } catch {
      return false;
    }
  }

  private isProblematicDomain(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      // List of domains known to have anti-bot measures
      const problematicDomains = [
        'medium.com',
        'linkedin.com',
        'facebook.com',
        'twitter.com',
        'instagram.com',
        'cloudflare.com',
        'quora.com',
        'reddit.com'
      ];
      
      return problematicDomains.some(domain => hostname.includes(domain));
    } catch {
      return false;
    }
  }

  private extractDomainTitle(url: string): string {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.toLowerCase();
      
      // Create a readable title from the domain
      const domainMap: { [key: string]: string } = {
        'medium.com': 'Medium Article',
        'linkedin.com': 'LinkedIn Content',
        'facebook.com': 'Facebook Content',
        'twitter.com': 'Twitter Content',
        'instagram.com': 'Instagram Content',
        'quora.com': 'Quora Content',
        'reddit.com': 'Reddit Content'
      };
      
      for (const [domain, title] of Object.entries(domainMap)) {
        if (hostname.includes(domain)) {
          return title;
        }
      }
      
      // Default title based on domain
      return hostname.replace(/^www\./, '').replace(/\.[^.]+$/, '').replace(/[^a-zA-Z0-9]/g, ' ').trim();
    } catch {
      return 'Web Content';
    }
  }

  async close(): Promise<void> {
    // No cleanup needed for axios-based scraper
    console.log('✅ Simple web scraper closed (no browser to close)');
  }
}

// Export singleton instance
export const webScraperService = new NodeWebScraperService(); 