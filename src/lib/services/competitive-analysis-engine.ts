/**
 * Competitive Analysis Engine
 * Analyzes competitor content to identify opportunities and gaps
 */

import { GeminiService } from '@/lib/gemini';
import { ExtractedContent, SEOData } from './enhanced-web-extractor';

export interface CompetitorAnalysisResult {
  // Basic Info
  url: string;
  title: string;
  
  // SEO Analysis
  titleAnalysis: {
    length: number;
    hasTargetKeyword: boolean;
    titleScore: number;
    recommendations: string[];
  };
  
  metaAnalysis: {
    descriptionLength: number;
    hasTargetKeyword: boolean;
    metaScore: number;
    recommendations: string[];
  };
  
  headingAnalysis: {
    h1Count: number;
    h2Count: number;
    h3Count: number;
    totalHeadings: number;
    hasTargetKeyword: boolean;
    headingScore: number;
    structure: string[];
    recommendations: string[];
  };
  
  keywordAnalysis: {
    primaryKeywords: string[];
    keywordDensity: { [keyword: string]: number };
    keywordScore: number;
    recommendations: string[];
  };
  
  // Content Structure
  contentStructure: {
    wordCount: number;
    paragraphCount: number;
    listCount: number;
    imageCount: number;
    contentScore: number;
    outline: string[];
    recommendations: string[];
  };
  
  // Gaps and Opportunities
  contentGaps: string[];
  contentStrengths: string[];
  improvementAreas: string[];
  uniqueAngles: string[];
  
  // Overall Scoring
  overallScore: number;
  seoScore: number;
  contentScore: number;
  
  // Specific Recommendations
  recommendations: {
    seo: string[];
    content: string[];
    structure: string[];
    opportunities: string[];
  };
}

export interface CompetitiveReport {
  topic: string;
  targetKeyword?: string;
  competitors: CompetitorAnalysisResult[];
  
  // Aggregate Analysis
  averageWordCount: number;
  commonKeywords: string[];
  commonHeadingPatterns: string[];
  contentGaps: string[];
  opportunities: string[];
  
  // Strategic Recommendations
  recommendedWordCount: number;
  recommendedStructure: string[];
  uniqueAngles: string[];
  competitiveAdvantages: string[];
  
  // Content Strategy
  contentStrategy: {
    primaryFocus: string;
    secondaryTopics: string[];
    contentDepth: 'shallow' | 'medium' | 'deep';
    contentType: string;
    targetAudience: string;
  };
}

export class CompetitiveAnalysisEngine {
  private gemini: GeminiService;
  
  constructor() {
    this.gemini = new GeminiService();
  }

  /**
   * Analyze multiple competitors for a topic using Gemini AI
   */
  async analyzeCompetitors(
    topic: string,
    competitors: ExtractedContent[],
    targetKeyword?: string
  ): Promise<CompetitiveReport> {
    console.log(`🔍 Starting AI-powered competitive analysis for "${topic}" with ${competitors.length} competitors`);
    
    // First, get basic SEO data for each competitor
    const competitorAnalyses: CompetitorAnalysisResult[] = [];
    
    for (const competitor of competitors) {
      if (competitor.success) {
        const basicAnalysis = await this.analyzeCompetitorSimplified(competitor, targetKeyword);
        competitorAnalyses.push(basicAnalysis);
      }
    }
    
    // Use Gemini to perform intelligent competitive analysis
    console.log('🤖 Using Gemini AI for intelligent competitive analysis...');
    const aiAnalysis = await this.performGeminiCompetitiveAnalysis(
      topic,
      competitors,
      competitorAnalyses,
      targetKeyword
    );
    
    // Merge AI insights with basic analysis
    const enhancedCompetitors = this.mergeAIInsights(competitorAnalyses, aiAnalysis.competitorInsights);
    
    const report: CompetitiveReport = {
      topic,
      targetKeyword,
      competitors: enhancedCompetitors,
      averageWordCount: aiAnalysis.averageWordCount,
      commonKeywords: aiAnalysis.commonKeywords,
      commonHeadingPatterns: aiAnalysis.commonHeadingPatterns,
      contentGaps: aiAnalysis.contentGaps,
      opportunities: aiAnalysis.opportunities,
      recommendedWordCount: aiAnalysis.recommendedWordCount,
      recommendedStructure: aiAnalysis.recommendedStructure,
      uniqueAngles: aiAnalysis.uniqueAngles,
      competitiveAdvantages: aiAnalysis.competitiveAdvantages,
      contentStrategy: aiAnalysis.contentStrategy,
    };
    
    console.log(`✅ AI-powered competitive analysis complete: ${enhancedCompetitors.length} competitors analyzed`);
    
    return report;
  }

  /**
   * Perform Gemini-powered competitive analysis
   */
  private async performGeminiCompetitiveAnalysis(
    topic: string,
    competitors: ExtractedContent[],
    basicAnalyses: CompetitorAnalysisResult[],
    targetKeyword?: string
  ) {
    console.log(`🔍 Processing ${competitors.length} competitors for Gemini analysis...`);
    
    try {
      const competitorData = competitors.map((comp, index) => {
      // Debug logging for problematic data
      if (!comp.seoData || !comp.seoData.headingStructure) {
        console.log(`⚠️ Competitor ${index + 1} missing heading structure:`, {
          url: comp.url,
          hasSeoData: !!comp.seoData,
          headingStructureType: typeof comp.seoData?.headingStructure
        });
      }
      
      return {
        url: comp.url,
        title: comp.title,
        content: comp.cleanContent?.substring(0, 3000) || '', // Limit content for prompt
        wordCount: basicAnalyses[index]?.contentStructure.wordCount || 0,
        seoScore: basicAnalyses[index]?.seoScore || 0,
        headings: (comp.seoData?.headingStructure && Array.isArray(comp.seoData.headingStructure)) 
          ? comp.seoData.headingStructure
              .filter(h => h && h.level && h.text) // Filter out invalid heading objects
              .map(h => `${h.level}: ${h.text}`)
              .join('\n') || 'No valid headings found'
          : 'No headings found',
        metaDescription: comp.seoData?.metaDescription || '',
      };
    });

    const prompt = `
Analyze competitors for "${topic}"${targetKeyword ? ` (keyword: "${targetKeyword}")` : ''} and provide JSON analysis.

COMPETITORS (${competitorData.length} total):
${competitorData.map((comp, index) => `
${index + 1}. ${comp.title} (${comp.wordCount}w, SEO:${comp.seoScore}/100)
URL: ${comp.url}
Meta: ${comp.metaDescription?.substring(0, 150) || 'None'}
Headings: ${comp.headings?.substring(0, 200) || 'None'}
Content: ${comp.content?.substring(0, 800) || 'None'}
---`).join('\n')}

REQUIRED JSON OUTPUT (no extra text):
{
  "averageWordCount": ${Math.round(competitorData.reduce((sum, c) => sum + c.wordCount, 0) / competitorData.length)},
  "commonKeywords": ["list 6-8 keywords"],
  "commonHeadingPatterns": ["list 5-6 patterns"],
  "contentGaps": ["list 5-6 gaps"],
  "opportunities": ["list 5-6 opportunities"],
  "recommendedWordCount": ${Math.round(competitorData.reduce((sum, c) => sum + c.wordCount, 0) / competitorData.length * 1.2)},
  "recommendedStructure": ["list 6 sections"],
  "uniqueAngles": ["list 5 angles"],
  "competitiveAdvantages": ["list 5 advantages"],
  "contentStrategy": {
    "primaryFocus": "brief focus",
    "secondaryTopics": ["3 topics"],
    "contentDepth": "deep",
    "targetAudience": "brief description"
  },
  "competitorInsights": [${competitorData.map(comp => `
    {
      "url": "${comp.url}",
      "contentGaps": ["2-3 gaps"],
      "contentStrengths": ["2-3 strengths"],
      "improvementAreas": ["2-3 areas"],
      "uniqueAngles": ["2-3 angles"]
    }`).join(',')}
  ]
}

CRITICAL: Return ONLY valid JSON. Start with { and end with }. No explanations before or after.

Example format:
{
  "averageWordCount": 4000,
  "commonKeywords": ["keyword1", "keyword2"],
  "commonHeadingPatterns": ["pattern1", "pattern2"],
  "contentGaps": ["gap1", "gap2"],
  "opportunities": ["opportunity1", "opportunity2"],
  "recommendedWordCount": 4800,
  "recommendedStructure": ["section1", "section2"],
  "uniqueAngles": ["angle1", "angle2"],
  "competitiveAdvantages": ["advantage1", "advantage2"],
  "contentStrategy": {
    "primaryFocus": "focus description",
    "secondaryTopics": ["topic1", "topic2"],
    "contentDepth": "deep",
    "targetAudience": "audience description"
  },
  "competitorInsights": []
}`;

    try {
      const result = await this.gemini.generateContent(prompt, {
        temperature: 0.3, // Lower temperature for more analytical content
        maxOutputTokens: 4000,
        // Remove thinking config as it might be causing empty responses
        // thinkingConfig: {
        //   thinkingBudget: -1,
        //   includeThoughts: false
        // }
      });

      // Enhanced JSON parsing with better error handling
      let cleanResponse = result.response.trim();
      
      // Handle empty response case
      if (!cleanResponse || cleanResponse.length === 0) {
        console.log('⚠️ Empty response from Gemini, falling back to rule-based analysis');
        throw new Error('Empty response from Gemini API');
      }
      
      console.log(`📄 Raw response length: ${cleanResponse.length} chars`);
      console.log(`📄 First 500 chars: ${cleanResponse.substring(0, 500)}`);
      console.log(`📄 Last 500 chars: ${cleanResponse.substring(Math.max(0, cleanResponse.length - 500))}`);
      
      // Remove code block markers more thoroughly
      cleanResponse = cleanResponse.replace(/^```json\s*/g, '').replace(/^```\s*/g, '').replace(/\s*```$/g, '');
      
      // Remove any leading/trailing whitespace and invisible characters
      cleanResponse = cleanResponse.trim();
      
      // Remove any BOM, zero-width characters, and other invisible characters
      cleanResponse = cleanResponse.replace(/^\uFEFF/, ''); // BOM
      cleanResponse = cleanResponse.replace(/[\u200B-\u200D\uFEFF]/g, ''); // Zero-width characters
      cleanResponse = cleanResponse.replace(/^[\s\r\n\t]+|[\s\r\n\t]+$/g, ''); // All whitespace variations
      
      // Extract JSON more carefully - find the outermost braces
      const firstBrace = cleanResponse.indexOf('{');
      if (firstBrace > 0) {
        cleanResponse = cleanResponse.substring(firstBrace);
      }
      
      // Find the last complete closing brace
      let braceCount = 0;
      let lastValidIndex = -1;
      
      for (let i = 0; i < cleanResponse.length; i++) {
        if (cleanResponse[i] === '{') {
          braceCount++;
        } else if (cleanResponse[i] === '}') {
          braceCount--;
          if (braceCount === 0) {
            lastValidIndex = i;
            // Don't break here - we want the LAST complete object
          }
        }
      }
      
      if (lastValidIndex > -1) {
        cleanResponse = cleanResponse.substring(0, lastValidIndex + 1);
      }
      
      console.log(`🔧 Cleaned JSON length: ${cleanResponse.length} chars`);
      console.log(`🔧 Cleaned JSON starts with: "${cleanResponse.substring(0, 50)}"`);
      console.log(`🔧 Cleaned JSON ends with: "${cleanResponse.substring(cleanResponse.length - 50)}"`);
      
      // Debug character codes at the beginning
      if (cleanResponse.length > 0) {
        console.log(`🔍 First 5 character codes: [${cleanResponse.substring(0, 5).split('').map(c => c.charCodeAt(0)).join(', ')}]`);
      }
      
      // Ensure we have a valid JSON structure
      if (!cleanResponse.startsWith('{') || !cleanResponse.endsWith('}')) {
        console.log('⚠️ JSON structure invalid after cleaning');
        throw new Error('Invalid JSON structure after cleaning');
      }
      
      // Fix common quote issues
      cleanResponse = this.fixJsonQuotes(cleanResponse);
      
      // Additional pre-parsing validation
      try {
        // Quick validation: check if it's properly formed JSON structure
        const openBraces = (cleanResponse.match(/\{/g) || []).length;
        const closeBraces = (cleanResponse.match(/\}/g) || []).length;
        const openBrackets = (cleanResponse.match(/\[/g) || []).length;
        const closeBrackets = (cleanResponse.match(/\]/g) || []).length;
        
        console.log(`🔍 JSON Structure Check: Braces ${openBraces}/${closeBraces}, Brackets ${openBrackets}/${closeBrackets}`);
        
        if (openBraces !== closeBraces || openBrackets !== closeBrackets) {
          console.log('⚠️ Unbalanced braces/brackets detected, will attempt repair');
        }
      } catch (validationError) {
        console.log('⚠️ JSON validation check failed:', validationError);
      }
      
      let aiAnalysis;
      
      // Use the enhanced JSON parser
      console.log(`🔧 Processing Gemini response: ${cleanResponse.length} chars`);
      
      try {
        aiAnalysis = this.parseGeminiJson(cleanResponse);
        console.log('✅ JSON parsing succeeded');
      } catch (parseError: any) {
        console.error('❌ JSON parsing failed:', parseError.message);
        console.log('📄 Problematic JSON preview:', cleanResponse.substring(0, 200));
        throw new Error(`JSON parsing failed: ${parseError.message}`);
      }
      
      // Validate required fields
      const requiredFields = ['averageWordCount', 'commonKeywords', 'contentGaps', 'opportunities', 'contentStrategy'];
      for (const field of requiredFields) {
        if (!aiAnalysis[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }
      
      console.log('✅ Gemini competitive analysis completed successfully');
      return aiAnalysis;
      
    } catch (error) {
      console.error('❌ Gemini competitive analysis failed:', error);
      
      // Fallback to rule-based analysis if AI fails
      console.log('📋 Falling back to rule-based analysis...');
      return this.generateFallbackAIAnalysis(topic, basicAnalyses, targetKeyword);
    }
    
    } catch (dataProcessingError) {
      console.error('❌ Error processing competitor data:', dataProcessingError);
      
      // Fallback to rule-based analysis if data processing fails
      console.log('📋 Falling back to rule-based analysis due to data processing error...');
      return this.generateFallbackAIAnalysis(topic, basicAnalyses, targetKeyword);
    }
  }

  /**
   * Enhanced JSON parser specifically designed for Gemini responses
   */
  private parseGeminiJson(responseText: string): any {
    console.log('🔍 Starting enhanced Gemini JSON parsing...');
    
    // Step 1: Basic string cleanup
    let jsonText = String(responseText)
      .trim()
      .normalize('NFC')
      .replace(/^\uFEFF/, '') // Remove BOM
      .replace(/[\u200B-\u200D\uFEFF]/g, '') // Remove zero-width chars
      .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F-\u009F]/g, ''); // Remove control chars
    
    // Step 2: Remove code block markers
    jsonText = jsonText
      .replace(/^```json\s*/gi, '')
      .replace(/^```\s*/g, '')
      .replace(/\s*```$/g, '')
      .trim();
    
    // Step 3: Extract valid JSON object
    const startBrace = jsonText.indexOf('{');
    if (startBrace === -1) {
      throw new Error('No JSON object found in response');
    }
    
    // Find the matching closing brace
    let braceCount = 0;
    let endBrace = -1;
    
    for (let i = startBrace; i < jsonText.length; i++) {
      if (jsonText[i] === '{') {
        braceCount++;
      } else if (jsonText[i] === '}') {
        braceCount--;
        if (braceCount === 0) {
          endBrace = i;
          break;
        }
      }
    }
    
    if (endBrace === -1) {
      throw new Error('No closing brace found for JSON object');
    }
    
    jsonText = jsonText.substring(startBrace, endBrace + 1);
    
    console.log(`🔍 Extracted JSON object: ${jsonText.length} chars`);
    
    // Enhanced debugging - show exact character breakdown
    if (jsonText.length > 0) {
      console.log(`🔍 First 10 characters with codes:`);
      for (let i = 0; i < Math.min(10, jsonText.length); i++) {
        const char = jsonText[i];
        const code = char.charCodeAt(0);
        const readable = char === '\n' ? '\\n' : char === '\r' ? '\\r' : char === '\t' ? '\\t' : char;
        console.log(`   [${i}] '${readable}' (${code})`);
      }
    }
    
    // Step 4: Try parsing with multiple strategies
    const strategies = [
      // Strategy 1: Direct parse
      () => JSON.parse(jsonText),
      
      // Strategy 2: Replace newlines with spaces
      () => JSON.parse(jsonText.replace(/\r?\n/g, ' ')),
      
      // Strategy 3: Minimize whitespace
      () => JSON.parse(jsonText.replace(/\s+/g, ' ')),
      
      // Strategy 4: Parse character by character reconstruction
      () => {
        const chars = Array.from(jsonText);
        const cleanText = chars.join('');
        return JSON.parse(cleanText);
      },
      
      // Strategy 5: Try with escaped newlines
      () => JSON.parse(jsonText.replace(/\n/g, '\\n').replace(/\r/g, '\\r')),
      
      // Strategy 6: Manual character filtering
      () => {
        const filteredText = jsonText
          .split('')
          .map(char => {
            const code = char.charCodeAt(0);
            // Only keep printable ASCII and essential whitespace
            if ((code >= 32 && code <= 126) || code === 9 || code === 10 || code === 13) {
              return char;
            }
            return '';
          })
          .join('');
        return JSON.parse(filteredText);
      }
    ];
    
    let lastError: Error | null = null;
    
    for (let i = 0; i < strategies.length; i++) {
      try {
        const result = strategies[i]();
        console.log(`🔍 Parse strategy ${i + 1} succeeded`);
        return result;
      } catch (error) {
        lastError = error as Error;
        console.log(`🔍 Parse strategy ${i + 1} failed: ${error}`);
        
        // For the first strategy failure, show more debug info
        if (i === 0 && error instanceof SyntaxError) {
          const errorMsg = error.message;
          const posMatch = errorMsg.match(/position (\d+)/);
          if (posMatch) {
            const pos = parseInt(posMatch[1]);
            console.log(`🔍 Error at position ${pos}:`);
            if (pos < jsonText.length) {
              console.log(`🔍 Character at position ${pos}: '${jsonText[pos]}' (code: ${jsonText[pos].charCodeAt(0)})`);
              console.log(`🔍 Context around position ${pos}: "${jsonText.substring(Math.max(0, pos - 5), pos + 6)}"`);
            }
          }
        }
      }
    }
    
    // If all strategies fail, try one more aggressive approach
    try {
      console.log('🔧 Attempting aggressive JSON repair...');
      
      // Remove problematic characters that might be invisible
      let repairedJson = jsonText
        .split('')
        .filter(char => {
          const code = char.charCodeAt(0);
          // Keep only printable ASCII and common Unicode chars
          return (code >= 32 && code <= 126) || code >= 160 || char === '\n' || char === '\r' || char === '\t';
        })
        .join('');
      
      const result = JSON.parse(repairedJson);
      console.log('🔧 Aggressive repair succeeded');
      return result;
    } catch (repairError) {
      console.error('🔧 Aggressive repair also failed:', repairError);
    }
    
    throw new Error(`All JSON parsing strategies failed. Last error: ${lastError?.message}`);
  }

  /**
   * Merge AI insights with basic competitor analyses
   */
  private mergeAIInsights(
    basicAnalyses: CompetitorAnalysisResult[],
    aiInsights: any[]
  ): CompetitorAnalysisResult[] {
    return basicAnalyses.map((analysis, index) => {
      const aiInsight = aiInsights?.[index];
      if (aiInsight) {
        return {
          ...analysis,
          contentGaps: aiInsight.contentGaps || analysis.contentGaps,
          contentStrengths: aiInsight.contentStrengths || analysis.contentStrengths,
          improvementAreas: aiInsight.improvementAreas || analysis.improvementAreas,
          uniqueAngles: aiInsight.uniqueAngles || analysis.uniqueAngles,
        };
      }
      return analysis;
    });
  }

  /**
   * Fix common quote and escape sequence issues in JSON
   */
  private fixJsonQuotes(jsonString: string): string {
    try {
      let fixed = jsonString;
      
      // Fix double backslashes that create invalid escape sequences
      fixed = fixed.replace(/\\\\/g, '\\');
      
      // Fix invalid escape sequences like \" followed by letters
      fixed = fixed.replace(/\\"/g, '"');
      
      // Fix newlines and tabs that might break JSON
      fixed = fixed.replace(/\n/g, '\\n');
      fixed = fixed.replace(/\r/g, '\\r');
      fixed = fixed.replace(/\t/g, '\\t');
      
      // Fix any remaining problematic characters
      fixed = fixed.replace(/[\x00-\x1F\x7F]/g, ''); // Remove control characters
      
      // Fix trailing commas in arrays and objects
      fixed = fixed.replace(/,(\s*[}\]])/g, '$1');
      
      return fixed;
    } catch (error) {
      console.warn('Failed to fix JSON quotes:', error);
      return jsonString;
    }
  }

  /**
   * Attempt to repair common JSON issues
   */
  private repairJson(jsonString: string): string | null {
    try {
      let repaired = jsonString;
      
      // First, handle invisible characters and encoding issues
      repaired = repaired.replace(/^\uFEFF/, ''); // Remove BOM
      repaired = repaired.replace(/[\u200B-\u200D\uFEFF]/g, ''); // Remove zero-width chars
      repaired = repaired.replace(/[\u0000-\u001F\u007F-\u009F]/g, ''); // Remove control chars
      
      // Normalize line endings and whitespace
      repaired = repaired.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
      repaired = repaired.trim();
      
      // First pass: clean up basic issues
      repaired = repaired.replace(/\\\\/g, '\\'); // Fix double backslashes
      repaired = repaired.replace(/\\"/g, '"'); // Fix escaped quotes
      repaired = repaired.replace(/\n/g, '\\n'); // Escape newlines
      repaired = repaired.replace(/\r/g, '\\r'); // Escape carriage returns
      repaired = repaired.replace(/\t/g, '\\t'); // Escape tabs
      
      // Remove control characters that might break JSON
      repaired = repaired.replace(/[\x00-\x1F\x7F]/g, '');
      
      // Remove trailing commas
      repaired = repaired.replace(/,(\s*[}\]])/g, '$1');
      
      // Fix missing quotes around property names
      repaired = repaired.replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":');
      
      // Fix missing closing braces/brackets
      let openBraces = (repaired.match(/\{/g) || []).length;
      let closeBraces = (repaired.match(/\}/g) || []).length;
      let openBrackets = (repaired.match(/\[/g) || []).length;
      let closeBrackets = (repaired.match(/\]/g) || []).length;
      
      // Add missing closing braces
      while (openBraces > closeBraces) {
        repaired += '}';
        closeBraces++;
      }
      
      // Add missing closing brackets
      while (openBrackets > closeBrackets) {
        repaired += ']';
        closeBrackets++;
      }
      
      // Handle incomplete properties at the end (like "recommendedWordCount":)
      const incompletePropertyRegex = /,\s*"[^"]+"\s*:\s*$/;
      if (incompletePropertyRegex.test(repaired)) {
        // Remove the incomplete property
        repaired = repaired.replace(incompletePropertyRegex, '');
      }
      
      // Remove any incomplete JSON properties/values at the end
      const lines = repaired.split('\n');
      let validLines = [];
      let foundIncomplete = false;
      
      for (let i = lines.length - 1; i >= 0; i--) {
        const line = lines[i].trim();
        
        // Check if line looks incomplete (property without value, incomplete array, etc.)
        if (!foundIncomplete && (
          line.endsWith(':') || // Property without value
          line.match(/,\s*$/) && !line.includes(':') || // Hanging comma
          line === '"recommendedWordCount":' // Specific truncation case
        )) {
          foundIncomplete = true;
          continue; // Skip this incomplete line
        }
        
        if (!foundIncomplete) {
          validLines.unshift(lines[i]);
        }
      }
      
      repaired = validLines.join('\n');
      
      // Ensure proper closing brace
      if (!repaired.trim().endsWith('}') && !repaired.trim().endsWith(']')) {
        // Make sure we don't have trailing comma before adding closing brace
        if (repaired.trim().endsWith(',')) {
          repaired = repaired.trim().slice(0, -1);
        }
        repaired += '\n}';
      }
      
      return repaired;
    } catch (error) {
      console.warn('Failed to repair JSON:', error);
      return null;
    }
  }

  /**
   * Extract a valid JSON subset as last resort
   */
  private extractValidJsonSubset(jsonString: string): string | null {
    try {
      // Try to find the main object structure and rebuild it with minimal data
      const baseStructure = {
        "averageWordCount": 2000,
        "commonKeywords": ["AI", "technology", "innovation", "digital", "automation"],
        "commonHeadingPatterns": ["Introduction", "Overview", "Benefits", "Implementation", "Conclusion"],
        "contentGaps": ["Practical examples", "Case studies", "Implementation guide"],
        "opportunities": ["In-depth analysis", "Expert insights", "Comparative review"],
        "recommendedWordCount": 2400,
        "recommendedStructure": ["Introduction", "Analysis", "Benefits", "Implementation", "Best Practices", "Conclusion"],
        "uniqueAngles": ["Technical deep-dive", "Business perspective", "Industry insights"],
        "competitiveAdvantages": ["Comprehensive coverage", "Expert analysis", "Actionable insights"],
        "contentStrategy": {
          "primaryFocus": "comprehensive analysis",
          "secondaryTopics": ["implementation", "benefits", "best practices"],
          "contentDepth": "deep",
          "targetAudience": "professionals and decision makers"
        },
        "competitorInsights": [
          {
            "url": "example.com",
            "contentGaps": ["missing details"],
            "contentStrengths": ["good structure"],
            "improvementAreas": ["add examples"],
            "uniqueAngles": ["technical focus"]
          }
        ]
      };
      
      return JSON.stringify(baseStructure);
    } catch (error) {
      console.warn('Failed to create JSON subset:', error);
      return null;
    }
  }

  /**
   * Generate fallback analysis if AI fails
   */
  private generateFallbackAIAnalysis(
    topic: string,
    basicAnalyses: CompetitorAnalysisResult[],
    targetKeyword?: string
  ) {
    const averageWordCount = basicAnalyses.reduce((sum, comp) => 
      sum + comp.contentStructure.wordCount, 0) / basicAnalyses.length;

    return {
      averageWordCount: Math.round(averageWordCount),
      commonKeywords: ['digital transformation', 'technology', 'innovation', 'AI', 'automation'],
      commonHeadingPatterns: ['Introduction', 'Key Benefits', 'How it Works', 'Best Practices', 'Conclusion'],
      contentGaps: ['Practical examples', 'Case studies', 'Implementation guide', 'ROI analysis'],
      opportunities: ['In-depth tutorials', 'Expert interviews', 'Comparative analysis', 'Future trends'],
      recommendedWordCount: Math.round(averageWordCount * 1.2),
      recommendedStructure: ['Introduction', 'Overview', 'Key Features', 'Benefits', 'Implementation', 'Best Practices', 'Conclusion'],
      uniqueAngles: ['Personal experience', 'Industry insights', 'Technical deep-dive', 'Business perspective'],
      competitiveAdvantages: ['More comprehensive coverage', 'Better examples', 'Actionable insights', 'Expert perspective'],
      contentStrategy: {
        primaryFocus: topic,
        secondaryTopics: ['implementation', 'benefits', 'best practices'],
        contentDepth: 'deep' as const,
        targetAudience: 'Professionals and decision-makers interested in the topic'
      },
      competitorInsights: basicAnalyses.map(analysis => ({
        url: analysis.url,
        contentGaps: ['More practical examples needed', 'Deeper technical details missing'],
        contentStrengths: ['Good structure', 'Clear explanations'],
        improvementAreas: ['Add more case studies', 'Include actionable steps'],
        uniqueAngles: ['Personal insights missing', 'Industry perspective needed']
      }))
    };
  }

  /**
   * Analyze a single competitor with simplified approach (no AI calls)
   */
  private async analyzeCompetitorSimplified(
    competitor: ExtractedContent,
    targetKeyword?: string
  ): Promise<CompetitorAnalysisResult> {
    const { seoData, cleanContent, title, url } = competitor;
    
    // Analyze title
    const titleAnalysis = this.analyzeTitleSEO(seoData.metaTitle || title, targetKeyword);
    
    // Analyze meta description
    const metaAnalysis = this.analyzeMetaSEO(seoData.metaDescription || '', targetKeyword);
    
    // Analyze headings
    const headingAnalysis = this.analyzeHeadingSEO(seoData.headingStructure, targetKeyword);
    
    // Analyze keywords
    const keywordAnalysis = this.analyzeKeywords(cleanContent, targetKeyword);
    
    // Analyze content structure
    const contentStructure = this.analyzeContentStructure(seoData, cleanContent);
    
    // Use fallback gaps and opportunities instead of AI calls
    const gapsAndOpportunities = {
      contentGaps: ['More detailed examples needed', 'Missing practical implementation', 'Lack of recent updates'],
      contentStrengths: ['Good SEO structure', 'Clear headings', 'Comprehensive coverage'],
      improvementAreas: ['Better user experience', 'More interactive elements', 'Updated information'],
      uniqueAngles: ['Fresh perspective needed', 'Different approach possible', 'Alternative solutions available'],
    };
    
    // Calculate scores
    const seoScore = (titleAnalysis.titleScore + metaAnalysis.metaScore + 
                     headingAnalysis.headingScore + keywordAnalysis.keywordScore) / 4;
    const contentScore = contentStructure.contentScore;
    const overallScore = (seoScore + contentScore) / 2;
    
    return {
      url,
      title,
      titleAnalysis,
      metaAnalysis,
      headingAnalysis,
      keywordAnalysis,
      contentStructure,
      ...gapsAndOpportunities,
      overallScore,
      seoScore,
      contentScore,
      recommendations: this.generateCompetitorRecommendations({
        titleAnalysis,
        metaAnalysis,
        headingAnalysis,
        keywordAnalysis,
        contentStructure,
      }),
    };
  }

  /**
   * Analyze a single competitor
   */
  private async analyzeCompetitor(
    competitor: ExtractedContent,
    targetKeyword?: string
  ): Promise<CompetitorAnalysisResult> {
    const { seoData, cleanContent, title, url } = competitor;
    
    // Analyze title
    const titleAnalysis = this.analyzeTitleSEO(seoData.metaTitle || title, targetKeyword);
    
    // Analyze meta description
    const metaAnalysis = this.analyzeMetaSEO(seoData.metaDescription || '', targetKeyword);
    
    // Analyze headings
    const headingAnalysis = this.analyzeHeadingSEO(seoData.headingStructure, targetKeyword);
    
    // Analyze keywords
    const keywordAnalysis = this.analyzeKeywords(cleanContent, targetKeyword);
    
    // Analyze content structure
    const contentStructure = this.analyzeContentStructure(seoData, cleanContent);
    
    // Identify gaps and opportunities using AI
    const gapsAndOpportunities = await this.identifyGapsAndOpportunities(
      competitor,
      targetKeyword
    );
    
    // Calculate scores
    const seoScore = (titleAnalysis.titleScore + metaAnalysis.metaScore + 
                     headingAnalysis.headingScore + keywordAnalysis.keywordScore) / 4;
    const contentScore = contentStructure.contentScore;
    const overallScore = (seoScore + contentScore) / 2;
    
    return {
      url,
      title,
      titleAnalysis,
      metaAnalysis,
      headingAnalysis,
      keywordAnalysis,
      contentStructure,
      ...gapsAndOpportunities,
      overallScore,
      seoScore,
      contentScore,
      recommendations: this.generateCompetitorRecommendations({
        titleAnalysis,
        metaAnalysis,
        headingAnalysis,
        keywordAnalysis,
        contentStructure,
      }),
    };
  }

  /**
   * Analyze title SEO
   */
  private analyzeTitleSEO(title: string, targetKeyword?: string): CompetitorAnalysisResult['titleAnalysis'] {
    const length = title.length;
    const hasTargetKeyword = targetKeyword ? 
      title.toLowerCase().includes(targetKeyword.toLowerCase()) : false;
    
    let score = 0;
    const recommendations: string[] = [];
    
    // Length scoring (optimal 50-60 characters)
    if (length >= 50 && length <= 60) {
      score += 30;
    } else if (length >= 40 && length <= 70) {
      score += 20;
    } else {
      score += 5;
      if (length < 40) recommendations.push('Title is too short - consider expanding to 50-60 characters');
      if (length > 70) recommendations.push('Title is too long - consider shortening to under 60 characters');
    }
    
    // Keyword scoring
    if (hasTargetKeyword) {
      score += 40;
    } else if (targetKeyword) {
      recommendations.push(`Title doesn't include target keyword "${targetKeyword}"`);
    }
    
    // Structure scoring
    if (title.includes('|') || title.includes('-') || title.includes(':')) {
      score += 15;
    } else {
      recommendations.push('Consider adding brand name or descriptive separator');
    }
    
    // Engagement scoring
    const engagementWords = ['best', 'top', 'ultimate', 'complete', 'guide', 'how to', 'why', 'what'];
    if (engagementWords.some(word => title.toLowerCase().includes(word))) {
      score += 15;
    } else {
      recommendations.push('Consider adding engaging words like "ultimate", "complete", "guide"');
    }
    
    return {
      length,
      hasTargetKeyword,
      titleScore: Math.min(score, 100),
      recommendations,
    };
  }

  /**
   * Analyze meta description SEO
   */
  private analyzeMetaSEO(description: string, targetKeyword?: string): CompetitorAnalysisResult['metaAnalysis'] {
    const length = description.length;
    const hasTargetKeyword = targetKeyword ? 
      description.toLowerCase().includes(targetKeyword.toLowerCase()) : false;
    
    let score = 0;
    const recommendations: string[] = [];
    
    // Length scoring (optimal 150-160 characters)
    if (length >= 150 && length <= 160) {
      score += 40;
    } else if (length >= 120 && length <= 170) {
      score += 30;
    } else {
      score += 10;
      if (length < 120) recommendations.push('Meta description is too short - aim for 150-160 characters');
      if (length > 170) recommendations.push('Meta description is too long - may be truncated in search results');
    }
    
    // Keyword scoring
    if (hasTargetKeyword) {
      score += 35;
    } else if (targetKeyword) {
      recommendations.push(`Meta description doesn't include target keyword "${targetKeyword}"`);
    }
    
    // Call-to-action scoring
    const ctaWords = ['learn', 'discover', 'find out', 'get', 'download', 'read', 'explore'];
    if (ctaWords.some(word => description.toLowerCase().includes(word))) {
      score += 25;
    } else {
      recommendations.push('Consider adding a call-to-action to encourage clicks');
    }
    
    return {
      descriptionLength: length,
      hasTargetKeyword,
      metaScore: Math.min(score, 100),
      recommendations,
    };
  }

  /**
   * Analyze heading structure SEO
   */
  private analyzeHeadingSEO(
    headingStructure: SEOData['headingStructure'],
    targetKeyword?: string
  ): CompetitorAnalysisResult['headingAnalysis'] {
    const { h1, h2, h3 } = headingStructure;
    const allHeadings = [...h1, ...h2, ...h3];
    const hasTargetKeyword = targetKeyword ? 
      allHeadings.some(heading => heading.toLowerCase().includes(targetKeyword.toLowerCase())) : false;
    
    let score = 0;
    const recommendations: string[] = [];
    
    // H1 scoring
    if (h1.length === 1) {
      score += 25;
    } else if (h1.length === 0) {
      score += 0;
      recommendations.push('Missing H1 tag - critical for SEO');
    } else {
      score += 10;
      recommendations.push('Multiple H1 tags found - should have only one');
    }
    
    // H2 scoring
    if (h2.length >= 3 && h2.length <= 8) {
      score += 25;
    } else if (h2.length > 0) {
      score += 15;
    } else {
      recommendations.push('Consider adding H2 tags to structure content');
    }
    
    // H3 scoring
    if (h3.length > 0) {
      score += 15;
    }
    
    // Keyword in headings
    if (hasTargetKeyword) {
      score += 35;
    } else if (targetKeyword) {
      recommendations.push(`Consider including target keyword "${targetKeyword}" in headings`);
    }
    
    return {
      h1Count: h1.length,
      h2Count: h2.length,
      h3Count: h3.length,
      totalHeadings: allHeadings.length,
      hasTargetKeyword,
      headingScore: Math.min(score, 100),
      structure: [...h1, ...h2.slice(0, 5), ...h3.slice(0, 5)], // Sample structure
      recommendations,
    };
  }

  /**
   * Analyze keyword usage
   */
  private analyzeKeywords(content: string, targetKeyword?: string): CompetitorAnalysisResult['keywordAnalysis'] {
    const words = content.toLowerCase().split(/\s+/);
    const wordCount = words.length;
    
    // Calculate keyword density
    const keywordDensity: { [keyword: string]: number } = {};
    const keywordCounts: { [keyword: string]: number } = {};
    
    if (targetKeyword) {
      const keyword = targetKeyword.toLowerCase();
      const keywordOccurrences = content.toLowerCase().split(keyword).length - 1;
      keywordCounts[keyword] = keywordOccurrences;
      keywordDensity[keyword] = (keywordOccurrences / wordCount) * 100;
    }
    
    // Extract top keywords
    const wordFreq: { [word: string]: number } = {};
    words.forEach(word => {
      if (word.length > 3 && !this.isStopWord(word)) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });
    
    const primaryKeywords = Object.entries(wordFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
    
    // Calculate score
    let score = 50; // Base score
    const recommendations: string[] = [];
    
    if (targetKeyword) {
      const density = keywordDensity[targetKeyword.toLowerCase()] || 0;
      if (density >= 1 && density <= 3) {
        score += 50; // Optimal density
      } else if (density > 0) {
        score += 25; // Some usage
        if (density < 1) recommendations.push('Target keyword density is low - consider using it more naturally');
        if (density > 3) recommendations.push('Target keyword density is high - avoid keyword stuffing');
      } else {
        recommendations.push('Target keyword not found in content');
      }
    }
    
    return {
      primaryKeywords,
      keywordDensity,
      keywordScore: Math.min(score, 100),
      recommendations,
    };
  }

  /**
   * Analyze content structure
   */
  private analyzeContentStructure(seoData: SEOData, content: string): CompetitorAnalysisResult['contentStructure'] {
    const { wordCount, paragraphCount, listCount, imageCount } = seoData;
    
    let score = 0;
    const recommendations: string[] = [];
    
    // Word count scoring
    if (wordCount >= 1500 && wordCount <= 3000) {
      score += 30;
    } else if (wordCount >= 1000) {
      score += 20;
    } else {
      score += 10;
      recommendations.push('Content length is below optimal range (1500-3000 words)');
    }
    
    // Structure scoring
    if (paragraphCount > 0) {
      const avgWordsPerParagraph = wordCount / paragraphCount;
      if (avgWordsPerParagraph >= 50 && avgWordsPerParagraph <= 150) {
        score += 20;
      } else if (avgWordsPerParagraph > 150) {
        recommendations.push('Paragraphs are too long - consider breaking them up');
      }
    }
    
    // Lists scoring
    if (listCount > 0) {
      score += 15;
    } else {
      recommendations.push('Consider adding bullet points or numbered lists for better readability');
    }
    
    // Images scoring
    if (imageCount > 0) {
      score += 15;
      const imagesPerWords = (imageCount / wordCount) * 1000;
      if (imagesPerWords < 1) {
        recommendations.push('Consider adding more images to break up text');
      }
    } else {
      recommendations.push('No images found - visual content improves engagement');
    }
    
    // Extract basic outline
    const sentences = content.split(/[.!?]/).filter(s => s.trim().length > 20);
    const outline = sentences.slice(0, 5).map(s => s.trim().substring(0, 100) + '...');
    
    return {
      wordCount,
      paragraphCount,
      listCount,
      imageCount,
      contentScore: Math.min(score + 20, 100), // Base score for having content
      outline,
      recommendations,
    };
  }

  /**
   * Identify content gaps and opportunities using AI
   */
  private async identifyGapsAndOpportunities(
    competitor: ExtractedContent,
    targetKeyword?: string
  ): Promise<{
    contentGaps: string[];
    contentStrengths: string[];
    improvementAreas: string[];
    uniqueAngles: string[];
  }> {
    const prompt = `
Analyze this competitor content and identify gaps and opportunities:

URL: ${competitor.url}
Title: ${competitor.title}
Word Count: ${competitor.seoData.wordCount}
Target Keyword: ${targetKeyword || 'Not specified'}

Content Preview:
${competitor.cleanContent.substring(0, 2000)}...

Heading Structure:
H1: ${competitor.seoData.headingStructure.h1.join(', ')}
H2: ${competitor.seoData.headingStructure.h2.slice(0, 5).join(', ')}

Analyze and provide:
1. Content gaps (what important topics are missing)
2. Content strengths (what they do well)
3. Improvement areas (where they could be better)
4. Unique angles (different approaches we could take)

Provide 3-5 items for each category in JSON format:
{
  "contentGaps": ["gap1", "gap2", ...],
  "contentStrengths": ["strength1", "strength2", ...],
  "improvementAreas": ["area1", "area2", ...],
  "uniqueAngles": ["angle1", "angle2", ...]
}
`;

    try {
      const result = await this.gemini.generateContent(prompt, {
        temperature: 0.3,
        maxOutputTokens: 1000,
      });
      
      // Clean up the response by removing code block markers and extracting JSON
      let cleanResponse = result.response.trim();
      
      // Remove code block markers
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Try to extract JSON from text that might contain explanations
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }
      
      // If response doesn't look like JSON, throw error to trigger fallback
      if (!cleanResponse.startsWith('{') || !cleanResponse.endsWith('}')) {
        throw new Error('Response is not valid JSON format');
      }
      
      const analysis = this.parseGeminiJson(cleanResponse);
      return analysis;
    } catch (error) {
      console.error('Failed to generate AI analysis:', error);
      return {
        contentGaps: ['Unable to analyze - analysis failed'],
        contentStrengths: ['Content exists and is indexed'],
        improvementAreas: ['Detailed analysis unavailable'],
        uniqueAngles: ['Consider alternative perspectives'],
      };
    }
  }

  /**
   * Generate recommendations for a competitor
   */
  private generateCompetitorRecommendations(analyses: {
    titleAnalysis: CompetitorAnalysisResult['titleAnalysis'];
    metaAnalysis: CompetitorAnalysisResult['metaAnalysis'];
    headingAnalysis: CompetitorAnalysisResult['headingAnalysis'];
    keywordAnalysis: CompetitorAnalysisResult['keywordAnalysis'];
    contentStructure: CompetitorAnalysisResult['contentStructure'];
  }): CompetitorAnalysisResult['recommendations'] {
    return {
      seo: [
        ...analyses.titleAnalysis.recommendations,
        ...analyses.metaAnalysis.recommendations,
      ],
      content: [
        ...analyses.contentStructure.recommendations,
      ],
      structure: [
        ...analyses.headingAnalysis.recommendations,
      ],
      opportunities: [
        ...analyses.keywordAnalysis.recommendations,
      ],
    };
  }

  /**
   * Generate aggregate analysis across all competitors
   */
  private generateAggregateAnalysis(competitors: CompetitorAnalysisResult[]): {
    averageWordCount: number;
    commonKeywords: string[];
    commonHeadingPatterns: string[];
    contentGaps: string[];
    opportunities: string[];
  } {
    if (competitors.length === 0) {
      return {
        averageWordCount: 0,
        commonKeywords: [],
        commonHeadingPatterns: [],
        contentGaps: [],
        opportunities: [],
      };
    }
    
    const averageWordCount = competitors.reduce((sum, comp) => 
      sum + comp.contentStructure.wordCount, 0) / competitors.length;
    
    // Find common keywords
    const allKeywords = competitors.flatMap(comp => comp.keywordAnalysis.primaryKeywords);
    const keywordFreq: { [keyword: string]: number } = {};
    allKeywords.forEach(keyword => {
      keywordFreq[keyword] = (keywordFreq[keyword] || 0) + 1;
    });
    
    const commonKeywords = Object.entries(keywordFreq)
      .filter(([, freq]) => freq >= Math.ceil(competitors.length / 2))
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([keyword]) => keyword);
    
    // Find common heading patterns
    const commonHeadingPatterns = competitors
      .flatMap(comp => comp.headingAnalysis.structure)
      .slice(0, 10);
    
    // Aggregate gaps and opportunities
    const contentGaps = [...new Set(competitors.flatMap(comp => comp.contentGaps))];
    const opportunities = [...new Set(competitors.flatMap(comp => comp.uniqueAngles))];
    
    return {
      averageWordCount: Math.round(averageWordCount),
      commonKeywords,
      commonHeadingPatterns,
      contentGaps,
      opportunities,
    };
  }

  /**
   * Generate strategic recommendations using AI
   */
  private async generateStrategicRecommendations(
    topic: string,
    competitors: CompetitorAnalysisResult[],
    targetKeyword?: string
  ): Promise<{
    recommendedWordCount: number;
    recommendedStructure: string[];
    uniqueAngles: string[];
    competitiveAdvantages: string[];
    contentStrategy: CompetitiveReport['contentStrategy'];
  }> {
    const competitorSummary = competitors.map(comp => ({
      url: comp.url,
      wordCount: comp.contentStructure.wordCount,
      seoScore: comp.seoScore,
      gaps: comp.contentGaps.slice(0, 3),
      strengths: comp.contentStrengths.slice(0, 3),
    }));
    
    const prompt = `
Analyze competitive landscape and provide strategic recommendations:

Topic: ${topic}
Target Keyword: ${targetKeyword || 'Not specified'}

Competitor Analysis Summary:
${JSON.stringify(competitorSummary, null, 2)}

Based on this analysis, provide strategic recommendations in JSON format:
{
  "recommendedWordCount": number,
  "recommendedStructure": ["section1", "section2", ...],
  "uniqueAngles": ["angle1", "angle2", ...],
  "competitiveAdvantages": ["advantage1", "advantage2", ...],
  "contentStrategy": {
    "primaryFocus": "main focus area",
    "secondaryTopics": ["topic1", "topic2", ...],
    "contentDepth": "deep|medium|shallow",
    "contentType": "guide|tutorial|analysis|etc",
    "targetAudience": "audience description"
  }
}
`;

    try {
      const result = await this.gemini.generateContent(prompt, {
        temperature: 0.4,
        maxOutputTokens: 1500,
      });
      
      // Clean up the response by removing code block markers and extracting JSON
      let cleanResponse = result.response.trim();
      
      // Remove code block markers
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Try to extract JSON from text that might contain explanations
      const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        cleanResponse = jsonMatch[0];
      }
      
      // If response doesn't look like JSON, throw error to trigger fallback
      if (!cleanResponse.startsWith('{') || !cleanResponse.endsWith('}')) {
        throw new Error('Response is not valid JSON format');
      }
      
      const strategy = this.parseGeminiJson(cleanResponse);
      return strategy;
    } catch (error) {
      console.error('Failed to generate strategic recommendations:', error);
      
      // Fallback recommendations
      const avgWordCount = competitors.reduce((sum, comp) => 
        sum + comp.contentStructure.wordCount, 0) / competitors.length;
      
      return {
        recommendedWordCount: Math.max(Math.round(avgWordCount * 1.2), 2000),
        recommendedStructure: [
          'Introduction',
          'Main Concepts',
          'Step-by-Step Guide',
          'Best Practices',
          'Common Mistakes',
          'Advanced Tips',
          'Conclusion'
        ],
        uniqueAngles: [
          'Practical implementation focus',
          'Real-world case studies',
          'Step-by-step tutorials',
          'Expert insights'
        ],
        competitiveAdvantages: [
          'More comprehensive coverage',
          'Better structure and readability',
          'Practical examples',
          'Up-to-date information'
        ],
        contentStrategy: {
          primaryFocus: topic,
          secondaryTopics: ['best practices', 'common challenges', 'advanced techniques'],
          contentDepth: 'deep' as const,
          contentType: 'comprehensive guide',
          targetAudience: 'intermediate to advanced users',
        },
      };
    }
  }

  /**
   * Generate fallback strategic recommendations without AI calls
   */
  private generateFallbackStrategicRecommendations(
    topic: string,
    competitors: CompetitorAnalysisResult[],
    targetKeyword?: string
  ): {
    recommendedWordCount: number;
    recommendedStructure: string[];
    uniqueAngles: string[];
    competitiveAdvantages: string[];
    contentStrategy: CompetitiveReport['contentStrategy'];
  } {
    if (competitors.length === 0) {
      return {
        recommendedWordCount: 2000,
        recommendedStructure: ['Introduction', 'Main Content', 'Conclusion'],
        uniqueAngles: ['Unique perspective', 'Fresh approach'],
        competitiveAdvantages: ['Better structure', 'More comprehensive'],
        contentStrategy: {
          primaryFocus: topic,
          secondaryTopics: ['best practices', 'tips'],
          contentDepth: 'medium' as const,
          contentType: 'guide',
          targetAudience: 'general audience',
        },
      };
    }
    
    // Fallback recommendations based on competitor data
    const avgWordCount = competitors.reduce((sum, comp) => 
      sum + comp.contentStructure.wordCount, 0) / competitors.length;
    
    return {
      recommendedWordCount: Math.max(Math.round(avgWordCount * 1.2), 2000),
      recommendedStructure: [
        'Introduction',
        'Overview',
        'Detailed Analysis',
        'Comparisons',
        'Best Practices',
        'Implementation Guide',
        'Conclusion'
      ],
      uniqueAngles: [
        'Comprehensive comparison',
        'Practical examples',
        'Step-by-step implementation',
        'Real-world use cases'
      ],
      competitiveAdvantages: [
        'More detailed analysis',
        'Better organization',
        'Practical insights',
        'Up-to-date information'
      ],
      contentStrategy: {
        primaryFocus: topic,
        secondaryTopics: ['comparison', 'implementation', 'best practices'],
        contentDepth: 'deep' as const,
        contentType: 'comprehensive analysis',
        targetAudience: 'technical users and decision makers',
      },
    };
  }

  /**
   * Check if a word is a stop word
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on',
      'with', 'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'we',
      'say', 'her', 'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their'
    ]);
    
    return stopWords.has(word.toLowerCase());
  }
}