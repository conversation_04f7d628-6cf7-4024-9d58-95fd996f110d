/**
 * Knowledge Base Service
 * Manages storage and retrieval of researched content and competitive analysis
 */

import { prisma } from '@/lib/prisma';
import { ExtractedContent } from './enhanced-web-extractor';
import { CompetitorAnalysisResult, CompetitiveReport } from './competitive-analysis-engine';

export interface KnowledgeBaseEntry {
  id: string;
  userId: string;
  topic: string;
  sourceUrl: string;
  title: string;
  content: string;
  metaDescription?: string;
  metaKeywords?: string[];
  headingStructure?: any;
  wordCount?: number;
  keywordsFound?: string[];
  internalLinks?: Array<{ href: string; text: string; title?: string }>;
  externalLinks?: Array<{ href: string; text: string; title?: string }>;
  readabilityScore?: number;
  contentScore?: number;
  extractedAt: Date;
  lastUpdated: Date;
  isActive: boolean;
}

export interface CompetitorAnalysisEntry {
  id: string;
  userId: string;
  topic: string;
  competitorUrl: string;
  competitorTitle?: string;
  titleAnalysis?: any;
  metaAnalysis?: any;
  headingAnalysis?: any;
  keywordAnalysis?: any;
  contentLength?: number;
  contentStructure?: any;
  contentGaps?: string[];
  contentStrengths?: string[];
  improvementAreas?: string[];
  uniqueAngles?: string[];
  recommendations?: any;
  overallScore?: number;
  seoScore?: number;
  contentScore?: number;
  analyzedAt: Date;
  lastUpdated: Date;
  isActive: boolean;
}

export interface KnowledgeBaseSearchOptions {
  topic?: string;
  keywords?: string[];
  minWordCount?: number;
  maxWordCount?: number;
  minContentScore?: number;
  sortBy?: 'relevance' | 'date' | 'score';
  limit?: number;
  offset?: number;
}

export class KnowledgeBaseService {
  /**
   * Store extracted content in knowledge base
   */
  static async storeContent(
    userId: string,
    topic: string,
    extractedContent: ExtractedContent[]
  ): Promise<KnowledgeBaseEntry[]> {
    console.log(`📚 Storing ${extractedContent.length} content entries for topic: ${topic}`);
    
    const entries: KnowledgeBaseEntry[] = [];
    
    for (const content of extractedContent) {
      if (!content.success) continue;
      
      try {
        // Check if content already exists
        const existing = await prisma.knowledgeBase.findFirst({
          where: {
            userId,
            sourceUrl: content.url,
          },
        });
        
        const data = {
          userId,
          topic,
          sourceUrl: content.url,
          title: content.title,
          content: content.content,
          metaDescription: content.seoData.metaDescription,
          metaKeywords: content.seoData.metaKeywords ? JSON.stringify(content.seoData.metaKeywords) : null,
          headingStructure: JSON.stringify(content.seoData.headingStructure),
          wordCount: content.seoData.wordCount,
          keywordsFound: content.keyPhrases.length > 0 ? JSON.stringify(content.keyPhrases) : null,
          internalLinks: JSON.stringify(content.seoData.internalLinks),
          externalLinks: JSON.stringify(content.seoData.externalLinks),
          readabilityScore: content.readabilityScore,
          contentScore: this.calculateContentScore(content),
          lastUpdated: new Date(),
          isActive: true,
        };
        
        let entry: any;
        if (existing) {
          // Update existing entry
          entry = await prisma.knowledgeBase.update({
            where: { id: existing.id },
            data,
          });
          console.log(`📝 Updated existing knowledge base entry: ${content.url}`);
        } else {
          // Create new entry
          entry = await prisma.knowledgeBase.create({
            data,
          });
          console.log(`✅ Stored new knowledge base entry: ${content.url}`);
        }
        
        // Store SEO insights
        await this.storeSEOInsights(entry.id, content);
        
        entries.push(this.mapToKnowledgeBaseEntry(entry));
        
      } catch (error) {
        console.error(`❌ Failed to store content from ${content.url}:`, error);
      }
    }
    
    console.log(`✅ Successfully stored ${entries.length} knowledge base entries`);
    return entries;
  }

  /**
   * Store competitive analysis results
   */
  static async storeCompetitiveAnalysis(
    userId: string,
    topic: string,
    competitiveReport: CompetitiveReport
  ): Promise<CompetitorAnalysisEntry[]> {
    console.log(`🏆 Storing competitive analysis for topic: ${topic}`);
    
    const entries: CompetitorAnalysisEntry[] = [];
    
    for (const competitor of competitiveReport.competitors) {
      try {
        // Check if analysis already exists
        const existing = await prisma.competitorAnalysis.findFirst({
          where: {
            userId,
            competitorUrl: competitor.url,
            topic,
          },
        });
        
        const data = {
          userId,
          topic,
          competitorUrl: competitor.url,
          competitorTitle: competitor.title,
          titleAnalysis: JSON.stringify(competitor.titleAnalysis),
          metaAnalysis: JSON.stringify(competitor.metaAnalysis),
          headingAnalysis: JSON.stringify(competitor.headingAnalysis),
          keywordAnalysis: JSON.stringify(competitor.keywordAnalysis),
          contentLength: competitor.contentStructure.wordCount,
          contentStructure: JSON.stringify(competitor.contentStructure),
          contentGaps: JSON.stringify(competitor.contentGaps),
          contentStrengths: JSON.stringify(competitor.contentStrengths),
          improvementAreas: JSON.stringify(competitor.improvementAreas),
          uniqueAngles: JSON.stringify(competitor.uniqueAngles),
          recommendations: JSON.stringify(competitor.recommendations),
          overallScore: competitor.overallScore,
          seoScore: competitor.seoScore,
          contentScore: competitor.contentScore,
          lastUpdated: new Date(),
          isActive: true,
        };
        
        let entry: any;
        if (existing) {
          // Update existing analysis
          entry = await prisma.competitorAnalysis.update({
            where: { id: existing.id },
            data,
          });
          console.log(`📝 Updated existing competitor analysis: ${competitor.url}`);
        } else {
          // Create new analysis
          entry = await prisma.competitorAnalysis.create({
            data,
          });
          console.log(`✅ Stored new competitor analysis: ${competitor.url}`);
        }
        
        entries.push(this.mapToCompetitorAnalysisEntry(entry));
        
      } catch (error) {
        console.error(`❌ Failed to store competitor analysis for ${competitor.url}:`, error);
      }
    }
    
    console.log(`✅ Successfully stored ${entries.length} competitor analyses`);
    return entries;
  }

  /**
   * Search knowledge base entries
   */
  static async searchKnowledgeBase(
    userId: string,
    options: KnowledgeBaseSearchOptions = {}
  ): Promise<{
    entries: KnowledgeBaseEntry[];
    total: number;
    hasMore: boolean;
  }> {
    const {
      topic,
      keywords,
      minWordCount,
      maxWordCount,
      minContentScore,
      sortBy = 'relevance',
      limit = 20,
      offset = 0,
    } = options;
    
    const where: any = {
      userId,
      isActive: true,
    };
    
    // Filter by topic
    if (topic) {
      where.topic = {
        contains: topic,
        mode: 'insensitive',
      };
    }
    
    // Filter by word count
    if (minWordCount || maxWordCount) {
      where.wordCount = {};
      if (minWordCount) where.wordCount.gte = minWordCount;
      if (maxWordCount) where.wordCount.lte = maxWordCount;
    }
    
    // Filter by content score
    if (minContentScore) {
      where.contentScore = {
        gte: minContentScore,
      };
    }
    
    // Set ordering
    let orderBy: any = { extractedAt: 'desc' };
    switch (sortBy) {
      case 'date':
        orderBy = { extractedAt: 'desc' };
        break;
      case 'score':
        orderBy = { contentScore: 'desc' };
        break;
      case 'relevance':
      default:
        orderBy = [
          { contentScore: 'desc' },
          { extractedAt: 'desc' },
        ];
        break;
    }
    
    // Execute queries
    const [entries, total] = await Promise.all([
      prisma.knowledgeBase.findMany({
        where,
        orderBy,
        skip: offset,
        take: limit,
      }),
      prisma.knowledgeBase.count({ where }),
    ]);
    
    const mappedEntries = entries.map(this.mapToKnowledgeBaseEntry);
    
    // Filter by keywords if provided (done after DB query for better performance)
    let filteredEntries = mappedEntries;
    if (keywords && keywords.length > 0) {
      filteredEntries = mappedEntries.filter(entry => {
        const contentLower = entry.content.toLowerCase();
        const titleLower = entry.title.toLowerCase();
        return keywords.some(keyword => 
          contentLower.includes(keyword.toLowerCase()) ||
          titleLower.includes(keyword.toLowerCase())
        );
      });
    }
    
    console.log(`🔍 Knowledge base search: ${filteredEntries.length}/${total} entries found`);
    
    return {
      entries: filteredEntries,
      total,
      hasMore: offset + limit < total,
    };
  }

  /**
   * Get competitive analysis for topic
   */
  static async getCompetitiveAnalysis(
    userId: string,
    topic: string
  ): Promise<CompetitorAnalysisEntry[]> {
    const analyses = await prisma.competitorAnalysis.findMany({
      where: {
        userId,
        topic: {
          contains: topic,
          mode: 'insensitive',
        },
        isActive: true,
      },
      orderBy: {
        overallScore: 'desc',
      },
    });
    
    return analyses.map(this.mapToCompetitorAnalysisEntry);
  }

  /**
   * Get knowledge base summary for topic
   */
  static async getTopicSummary(userId: string, topic: string): Promise<{
    totalEntries: number;
    totalWordCount: number;
    averageContentScore: number;
    topSources: Array<{ url: string; title: string; score: number }>;
    competitorCount: number;
    lastUpdated: Date | null;
  }> {
    const [knowledgeStats, competitorCount] = await Promise.all([
      prisma.knowledgeBase.aggregate({
        where: {
          userId,
          topic: {
            contains: topic,
            mode: 'insensitive',
          },
          isActive: true,
        },
        _count: { id: true },
        _sum: { wordCount: true },
        _avg: { contentScore: true },
        _max: { lastUpdated: true },
      }),
      prisma.competitorAnalysis.count({
        where: {
          userId,
          topic: {
            contains: topic,
            mode: 'insensitive',
          },
          isActive: true,
        },
      }),
    ]);
    
    // Get top sources
    const topSources = await prisma.knowledgeBase.findMany({
      where: {
        userId,
        topic: {
          contains: topic,
          mode: 'insensitive',
        },
        isActive: true,
      },
      select: {
        sourceUrl: true,
        title: true,
        contentScore: true,
      },
      orderBy: {
        contentScore: 'desc',
      },
      take: 5,
    });
    
    return {
      totalEntries: knowledgeStats._count.id || 0,
      totalWordCount: knowledgeStats._sum.wordCount || 0,
      averageContentScore: knowledgeStats._avg.contentScore || 0,
      topSources: topSources.map(source => ({
        url: source.sourceUrl,
        title: source.title,
        score: source.contentScore || 0,
      })),
      competitorCount,
      lastUpdated: knowledgeStats._max.lastUpdated,
    };
  }

  /**
   * Clean up old knowledge base entries
   */
  static async cleanupOldEntries(userId: string, daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    const result = await prisma.knowledgeBase.updateMany({
      where: {
        userId,
        lastUpdated: {
          lt: cutoffDate,
        },
      },
      data: {
        isActive: false,
      },
    });
    
    console.log(`🧹 Deactivated ${result.count} old knowledge base entries`);
    return result.count;
  }

  /**
   * Store SEO insights for knowledge base entry
   */
  private static async storeSEOInsights(knowledgeBaseId: string, content: ExtractedContent): Promise<void> {
    try {
      const insights = {
        knowledgeBaseId,
        primaryKeywords: content.mainTopics.length > 0 ? JSON.stringify(content.mainTopics.slice(0, 5)) : null,
        secondaryKeywords: content.keyPhrases.length > 0 ? JSON.stringify(content.keyPhrases.slice(0, 10)) : null,
        longTailKeywords: content.keyPhrases.length > 0 ? JSON.stringify(content.keyPhrases.slice(10, 20)) : null,
        headingStructure: JSON.stringify(content.seoData.headingStructure),
        metaTitle: content.seoData.metaTitle,
        metaDescription: content.seoData.metaDescription,
        canonicalUrl: content.seoData.canonicalUrl,
        schemaMarkup: content.seoData.schemaMarkup ? JSON.stringify(content.seoData.schemaMarkup) : null,
        estimatedReadTime: Math.ceil(content.seoData.wordCount / 200), // Assume 200 WPM
        authorityScore: this.calculateAuthorityScore(content),
        seoRecommendations: JSON.stringify(this.generateSEORecommendations(content)),
        lastUpdated: new Date(),
      };
      
      // Check if insights already exist
      const existing = await prisma.sEOInsights.findFirst({
        where: { knowledgeBaseId },
      });
      
      if (existing) {
        await prisma.sEOInsights.update({
          where: { id: existing.id },
          data: insights,
        });
      } else {
        await prisma.sEOInsights.create({
          data: insights,
        });
      }
      
    } catch (error) {
      console.error('Failed to store SEO insights:', error);
    }
  }

  /**
   * Calculate content score based on various factors
   */
  private static calculateContentScore(content: ExtractedContent): number {
    let score = 50; // Base score
    
    // Word count scoring
    const wordCount = content.seoData.wordCount;
    if (wordCount > 1500) score += 20;
    else if (wordCount > 1000) score += 15;
    else if (wordCount > 500) score += 10;
    
    // SEO factors
    if (content.seoData.hasH1) score += 10;
    if (content.seoData.metaDescription) score += 10;
    if (content.seoData.hasStructuredData) score += 5;
    if (content.seoData.hasAltTags) score += 5;
    
    // Authority indicators
    if (content.authorityIndicators.hasAuthor) score += 5;
    if (content.authorityIndicators.hasPublishDate) score += 5;
    
    return Math.min(score, 100);
  }

  /**
   * Calculate authority score
   */
  private static calculateAuthorityScore(content: ExtractedContent): number {
    let score = 0;
    
    // Domain authority indicators
    const url = new URL(content.url);
    const domain = url.hostname.toLowerCase();
    
    // High authority domains
    const highAuthDomains = ['wikipedia.org', 'github.com', 'stackoverflow.com', 'medium.com'];
    if (highAuthDomains.some(auth => domain.includes(auth))) score += 0.3;
    
    // HTTPS
    if (url.protocol === 'https:') score += 0.1;
    
    // Author and date
    if (content.authorityIndicators.hasAuthor) score += 0.2;
    if (content.authorityIndicators.hasPublishDate) score += 0.2;
    if (content.authorityIndicators.hasContactInfo) score += 0.1;
    
    // Structured data
    if (content.seoData.hasStructuredData) score += 0.1;
    
    return Math.min(score, 1.0);
  }

  /**
   * Generate SEO recommendations
   */
  private static generateSEORecommendations(content: ExtractedContent): string[] {
    const recommendations: string[] = [];
    
    if (!content.seoData.hasH1) {
      recommendations.push('Add H1 tag for better SEO structure');
    }
    
    if (!content.seoData.metaDescription) {
      recommendations.push('Add meta description for better search snippets');
    }
    
    if (content.seoData.wordCount < 1000) {
      recommendations.push('Consider expanding content for better SEO performance');
    }
    
    if (!content.seoData.hasAltTags && content.seoData.imageCount > 0) {
      recommendations.push('Add alt tags to images for accessibility and SEO');
    }
    
    if (!content.authorityIndicators.hasAuthor) {
      recommendations.push('Add author information for better E-A-T signals');
    }
    
    return recommendations;
  }

  /**
   * Map database entry to KnowledgeBaseEntry
   */
  private static mapToKnowledgeBaseEntry(entry: any): KnowledgeBaseEntry {
    return {
      id: entry.id,
      userId: entry.userId,
      topic: entry.topic,
      sourceUrl: entry.sourceUrl,
      title: entry.title,
      content: entry.content,
      metaDescription: entry.metaDescription,
      metaKeywords: entry.metaKeywords ? JSON.parse(entry.metaKeywords) : undefined,
      headingStructure: entry.headingStructure ? JSON.parse(entry.headingStructure) : undefined,
      wordCount: entry.wordCount,
      keywordsFound: entry.keywordsFound ? JSON.parse(entry.keywordsFound) : undefined,
      internalLinks: entry.internalLinks ? JSON.parse(entry.internalLinks) : undefined,
      externalLinks: entry.externalLinks ? JSON.parse(entry.externalLinks) : undefined,
      readabilityScore: entry.readabilityScore,
      contentScore: entry.contentScore,
      extractedAt: entry.extractedAt,
      lastUpdated: entry.lastUpdated,
      isActive: entry.isActive,
    };
  }

  /**
   * Map database entry to CompetitorAnalysisEntry
   */
  private static mapToCompetitorAnalysisEntry(entry: any): CompetitorAnalysisEntry {
    return {
      id: entry.id,
      userId: entry.userId,
      topic: entry.topic,
      competitorUrl: entry.competitorUrl,
      competitorTitle: entry.competitorTitle,
      titleAnalysis: entry.titleAnalysis ? JSON.parse(entry.titleAnalysis) : undefined,
      metaAnalysis: entry.metaAnalysis ? JSON.parse(entry.metaAnalysis) : undefined,
      headingAnalysis: entry.headingAnalysis ? JSON.parse(entry.headingAnalysis) : undefined,
      keywordAnalysis: entry.keywordAnalysis ? JSON.parse(entry.keywordAnalysis) : undefined,
      contentLength: entry.contentLength,
      contentStructure: entry.contentStructure ? JSON.parse(entry.contentStructure) : undefined,
      contentGaps: entry.contentGaps ? JSON.parse(entry.contentGaps) : undefined,
      contentStrengths: entry.contentStrengths ? JSON.parse(entry.contentStrengths) : undefined,
      improvementAreas: entry.improvementAreas ? JSON.parse(entry.improvementAreas) : undefined,
      uniqueAngles: entry.uniqueAngles ? JSON.parse(entry.uniqueAngles) : undefined,
      recommendations: entry.recommendations ? JSON.parse(entry.recommendations) : undefined,
      overallScore: entry.overallScore,
      seoScore: entry.seoScore,
      contentScore: entry.contentScore,
      analyzedAt: entry.analyzedAt,
      lastUpdated: entry.lastUpdated,
      isActive: entry.isActive,
    };
  }
}