/**
 * Enhanced Web Extractor with SEO Analysis
 * Extracts comprehensive SEO and content data from web pages
 */

import axios from 'axios';
import * as cheerio from 'cheerio';

export interface SEOData {
  // Meta Information
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string[];
  canonicalUrl?: string;
  
  // Open Graph
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  
  // Heading Structure
  headingStructure: {
    h1: string[];
    h2: string[];
    h3: string[];
    h4: string[];
    h5: string[];
    h6: string[];
  };
  
  // Links
  internalLinks: Array<{
    href: string;
    text: string;
    title?: string;
  }>;
  externalLinks: Array<{
    href: string;
    text: string;
    title?: string;
  }>;
  
  // Content Metrics
  wordCount: number;
  paragraphCount: number;
  imageCount: number;
  listCount: number;
  
  // SEO Metrics
  titleLength: number;
  descriptionLength: number;
  hasH1: boolean;
  h1Count: number;
  
  // Schema Markup
  schemaMarkup?: any[];
  
  // Additional SEO Factors
  hasAltTags: boolean;
  altTagsCount: number;
  hasStructuredData: boolean;
}

export interface ExtractedContent {
  success: boolean;
  url: string;
  title: string;
  content: string;
  cleanContent: string; // Content without HTML tags
  seoData: SEOData;
  extractedAt: Date;
  
  // Content Analysis
  mainTopics: string[];
  keyPhrases: string[];
  readabilityScore?: number;
  
  // Quality Indicators
  authorityIndicators: {
    hasAuthor: boolean;
    hasPublishDate: boolean;
    hasLastModified: boolean;
    hasBreadcrumbs: boolean;
    hasContactInfo: boolean;
  };
  
  error?: string;
}

export class EnhancedWebExtractor {
  private static instance: EnhancedWebExtractor;
  
  public static getInstance(): EnhancedWebExtractor {
    if (!EnhancedWebExtractor.instance) {
      EnhancedWebExtractor.instance = new EnhancedWebExtractor();
    }
    return EnhancedWebExtractor.instance;
  }

  /**
   * Extract comprehensive content and SEO data from a URL
   */
  async extractContent(url: string, options: {
    timeout?: number;
    maxContentLength?: number;
    includeImages?: boolean;
  } = {}): Promise<ExtractedContent> {
    const { timeout = 15000, maxContentLength = 10000, includeImages = false } = options;
    
    try {
      console.log(`🔍 Enhanced extraction from: ${url}`);
      
      const response = await axios.get(url, {
        timeout,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Cache-Control': 'no-cache',
        },
      });

      const $ = cheerio.load(response.data);
      
      // Extract SEO data
      const seoData = this.extractSEOData($, url);
      
      // Extract main content
      const { title, content, cleanContent } = this.extractMainContent($);
      
      // Analyze content
      const mainTopics = this.extractMainTopics(cleanContent);
      const keyPhrases = this.extractKeyPhrases(cleanContent);
      
      // Extract authority indicators
      const authorityIndicators = this.extractAuthorityIndicators($);
      
      console.log(`✅ Enhanced extraction complete: ${cleanContent.length} chars, ${seoData.wordCount} words`);
      
      return {
        success: true,
        url,
        title,
        content: content.substring(0, maxContentLength),
        cleanContent: cleanContent.substring(0, maxContentLength),
        seoData,
        extractedAt: new Date(),
        mainTopics,
        keyPhrases,
        authorityIndicators,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Enhanced extraction failed for ${url}:`, errorMessage);
      
      return {
        success: false,
        url,
        title: '',
        content: '',
        cleanContent: '',
        seoData: this.getEmptySEOData(),
        extractedAt: new Date(),
        mainTopics: [],
        keyPhrases: [],
        authorityIndicators: {
          hasAuthor: false,
          hasPublishDate: false,
          hasLastModified: false,
          hasBreadcrumbs: false,
          hasContactInfo: false,
        },
        error: errorMessage,
      };
    }
  }

  /**
   * Extract comprehensive SEO data from the page
   */
  private extractSEOData($: cheerio.CheerioAPI, url: string): SEOData {
    // Meta information
    const metaTitle = $('title').first().text().trim() || 
                     $('meta[property="og:title"]').attr('content') || '';
    const metaDescription = $('meta[name="description"]').attr('content') || 
                           $('meta[property="og:description"]').attr('content') || '';
    const metaKeywords = $('meta[name="keywords"]').attr('content')?.split(',').map(k => k.trim()) || [];
    const canonicalUrl = $('link[rel="canonical"]').attr('href') || url;
    
    // Open Graph data
    const ogTitle = $('meta[property="og:title"]').attr('content');
    const ogDescription = $('meta[property="og:description"]').attr('content');
    const ogImage = $('meta[property="og:image"]').attr('content');
    const ogType = $('meta[property="og:type"]').attr('content');
    
    // Extract heading structure
    const headingStructure = {
      h1: $('h1').map((_, el) => $(el).text().trim()).get(),
      h2: $('h2').map((_, el) => $(el).text().trim()).get(),
      h3: $('h3').map((_, el) => $(el).text().trim()).get(),
      h4: $('h4').map((_, el) => $(el).text().trim()).get(),
      h5: $('h5').map((_, el) => $(el).text().trim()).get(),
      h6: $('h6').map((_, el) => $(el).text().trim()).get(),
    };
    
    // Extract links
    const baseUrl = new URL(url).origin;
    const internalLinks: Array<{href: string, text: string, title?: string}> = [];
    const externalLinks: Array<{href: string, text: string, title?: string}> = [];
    
    $('a[href]').each((_, el) => {
      const href = $(el).attr('href');
      const text = $(el).text().trim();
      const title = $(el).attr('title');
      
      if (href && text) {
        try {
          const fullUrl = new URL(href, url).href;
          const linkData = { href: fullUrl, text, title };
          
          if (fullUrl.startsWith(baseUrl)) {
            internalLinks.push(linkData);
          } else if (fullUrl.startsWith('http')) {
            externalLinks.push(linkData);
          }
        } catch (e) {
          // Invalid URL, skip
        }
      }
    });
    
    // Content metrics
    const bodyText = $('body').text();
    const wordCount = bodyText.split(/\s+/).filter(word => word.length > 0).length;
    const paragraphCount = $('p').length;
    const imageCount = $('img').length;
    const listCount = $('ul, ol').length;
    
    // SEO metrics
    const titleLength = metaTitle.length;
    const descriptionLength = metaDescription.length;
    const hasH1 = headingStructure.h1.length > 0;
    const h1Count = headingStructure.h1.length;
    
    // Check alt tags
    const imagesWithAlt = $('img[alt]').length;
    const hasAltTags = imageCount > 0 && imagesWithAlt === imageCount;
    
    // Extract schema markup
    const schemaMarkup: any[] = [];
    $('script[type="application/ld+json"]').each((_, el) => {
      try {
        const schema = JSON.parse($(el).html() || '');
        schemaMarkup.push(schema);
      } catch (e) {
        // Invalid JSON, skip
      }
    });
    
    return {
      metaTitle,
      metaDescription,
      metaKeywords,
      canonicalUrl,
      ogTitle,
      ogDescription,
      ogImage,
      ogType,
      headingStructure,
      internalLinks,
      externalLinks,
      wordCount,
      paragraphCount,
      imageCount,
      listCount,
      titleLength,
      descriptionLength,
      hasH1,
      h1Count,
      schemaMarkup,
      hasAltTags,
      altTagsCount: imagesWithAlt,
      hasStructuredData: schemaMarkup.length > 0,
    };
  }

  /**
   * Extract main content from the page
   */
  private extractMainContent($: cheerio.CheerioAPI): {
    title: string;
    content: string;
    cleanContent: string;
  } {
    // Remove unwanted elements
    $('script, style, nav, header, footer, aside, .advertisement, .ads, .social-share, .cookie-banner, .popup, .modal, .sidebar, .navigation, .menu').remove();
    
    // Extract title
    const title = $('title').first().text().trim() || 
                 $('h1').first().text().trim() || 
                 $('meta[property="og:title"]').attr('content') || 
                 'Extracted Content';
    
    // Try multiple content extraction strategies
    let content = '';
    const contentSelectors = [
      'article',
      '[role="main"]',
      'main',
      '.content',
      '.post-content',
      '.entry-content',
      '.article-content',
      '.article-body',
      '.post-body',
      '.story-body',
      '#content',
      '.main-content',
      '.page-content',
      '.post',
      '.entry',
    ];
    
    // Try semantic selectors first
    for (const selector of contentSelectors) {
      const element = $(selector);
      if (element.length > 0) {
        const elementContent = element.html() || '';
        if (elementContent.length > content.length && elementContent.length > 500) {
          content = elementContent;
        }
      }
    }
    
    // Fallback: extract from body
    if (!content || content.length < 500) {
      content = $('body').html() || '';
    }
    
    // Clean content for analysis
    const cleanContent = $('<div>').html(content).text()
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim();
    
    return { title, content, cleanContent };
  }

  /**
   * Extract main topics from content using simple keyword extraction
   */
  private extractMainTopics(content: string): string[] {
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    const wordFreq: { [key: string]: number } = {};
    words.forEach(word => {
      if (!this.isStopWord(word)) {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
      }
    });
    
    return Object.entries(wordFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  /**
   * Extract key phrases from content
   */
  private extractKeyPhrases(content: string): string[] {
    const sentences = content.split(/[.!?]/).filter(s => s.trim().length > 20);
    const phrases: string[] = [];
    
    sentences.forEach(sentence => {
      const words = sentence.trim().toLowerCase().split(/\s+/);
      
      // Extract 2-word phrases
      for (let i = 0; i < words.length - 1; i++) {
        const phrase = `${words[i]} ${words[i + 1]}`;
        if (phrase.length > 6 && !this.isStopWord(words[i]) && !this.isStopWord(words[i + 1])) {
          phrases.push(phrase);
        }
      }
      
      // Extract 3-word phrases
      for (let i = 0; i < words.length - 2; i++) {
        const phrase = `${words[i]} ${words[i + 1]} ${words[i + 2]}`;
        if (phrase.length > 10 && !this.isStopWord(words[i])) {
          phrases.push(phrase);
        }
      }
    });
    
    // Count phrase frequency and return top phrases
    const phraseFreq: { [key: string]: number } = {};
    phrases.forEach(phrase => {
      phraseFreq[phrase] = (phraseFreq[phrase] || 0) + 1;
    });
    
    return Object.entries(phraseFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 15)
      .map(([phrase]) => phrase);
  }

  /**
   * Extract authority indicators
   */
  private extractAuthorityIndicators($: cheerio.CheerioAPI): {
    hasAuthor: boolean;
    hasPublishDate: boolean;
    hasLastModified: boolean;
    hasBreadcrumbs: boolean;
    hasContactInfo: boolean;
  } {
    const hasAuthor = $('[rel="author"], .author, .by-author, [class*="author"]').length > 0 ||
                     $('meta[name="author"]').length > 0;
    
    const hasPublishDate = $('time, .date, .published, [datetime]').length > 0 ||
                          $('meta[property="article:published_time"]').length > 0;
    
    const hasLastModified = $('meta[property="article:modified_time"]').length > 0 ||
                           $('[class*="modified"], [class*="updated"]').length > 0;
    
    const hasBreadcrumbs = $('.breadcrumb, .breadcrumbs, [class*="breadcrumb"]').length > 0;
    
    const hasContactInfo = $('[href^="mailto:"], [href^="tel:"], .contact, [class*="contact"]').length > 0;
    
    return {
      hasAuthor,
      hasPublishDate,
      hasLastModified,
      hasBreadcrumbs,
      hasContactInfo,
    };
  }

  /**
   * Check if a word is a stop word
   */
  private isStopWord(word: string): boolean {
    const stopWords = new Set([
      'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on',
      'with', 'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'we',
      'say', 'her', 'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their',
      'what', 'so', 'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me', 'when',
      'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know', 'take', 'people', 'into',
      'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other', 'than', 'then', 'now',
      'look', 'only', 'come', 'its', 'over', 'think', 'also', 'back', 'after', 'use', 'two',
      'how', 'our', 'work', 'first', 'well', 'way', 'even', 'new', 'want', 'because', 'any',
      'these', 'give', 'day', 'most', 'us'
    ]);
    
    return stopWords.has(word.toLowerCase());
  }

  /**
   * Get empty SEO data structure
   */
  private getEmptySEOData(): SEOData {
    return {
      headingStructure: { h1: [], h2: [], h3: [], h4: [], h5: [], h6: [] },
      internalLinks: [],
      externalLinks: [],
      wordCount: 0,
      paragraphCount: 0,
      imageCount: 0,
      listCount: 0,
      titleLength: 0,
      descriptionLength: 0,
      hasH1: false,
      h1Count: 0,
      hasAltTags: false,
      altTagsCount: 0,
      hasStructuredData: false,
    };
  }

  /**
   * Extract content from multiple URLs in parallel
   */
  async extractMultiple(urls: string[], options: {
    maxConcurrent?: number;
    timeout?: number;
    maxContentLength?: number;
  } = {}): Promise<ExtractedContent[]> {
    const { maxConcurrent = 5, ...extractOptions } = options;
    
    console.log(`🔍 Enhanced extraction from ${urls.length} URLs (max concurrent: ${maxConcurrent})`);
    
    const results: ExtractedContent[] = [];
    
    // Process URLs in batches
    for (let i = 0; i < urls.length; i += maxConcurrent) {
      const batch = urls.slice(i, i + maxConcurrent);
      console.log(`📦 Processing batch ${Math.floor(i / maxConcurrent) + 1}/${Math.ceil(urls.length / maxConcurrent)}`);
      
      const batchPromises = batch.map(url => this.extractContent(url, extractOptions));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      // Add delay between batches to be respectful
      if (i + maxConcurrent < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    console.log(`✅ Enhanced extraction complete: ${successCount}/${urls.length} successful`);
    
    return results;
  }
}