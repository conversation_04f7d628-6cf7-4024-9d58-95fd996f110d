/**
 * Image Storage Service
 * Handles downloading, converting PNG to WebP, and storing images locally
 */

import sharp from 'sharp'
import fs from 'fs/promises'
import path from 'path'
import { prisma } from '@/lib/prisma'

export interface StoredImage {
  id: string
  originalUrl: string
  localPath: string
  webpPath: string
  filename: string
  size: number
  width: number
  height: number
  contentId?: string
  headingText?: string
  createdAt: Date
}

export class ImageStorageService {
  private storageDir: string
  private webpDir: string

  constructor() {
    // Create storage directories in public folder for web access
    this.storageDir = path.join(process.cwd(), 'public', 'images', 'blog')
    this.webpDir = path.join(process.cwd(), 'public', 'images', 'blog', 'webp')
  }

  /**
   * Ensure storage directories exist
   */
  private async ensureDirectories(): Promise<void> {
    try {
      await fs.mkdir(this.storageDir, { recursive: true })
      await fs.mkdir(this.webpDir, { recursive: true })
    } catch (error) {
      console.error('Failed to create storage directories:', error)
      throw error
    }
  }

  /**
   * Generate unique filename
   */
  private generateFilename(headingText?: string): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    const sanitizedHeading = headingText 
      ? headingText.toLowerCase().replace(/[^a-z0-9]+/g, '-').substring(0, 30)
      : 'image'
    
    return `${sanitizedHeading}-${timestamp}-${random}`
  }

  /**
   * Download image from URL
   */
  private async downloadImage(url: string, filepath: string): Promise<void> {
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.status}`)
      }

      const arrayBuffer = await response.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      
      await fs.writeFile(filepath, buffer)
    } catch (error) {
      console.error('Failed to download image:', error)
      throw error
    }
  }

  /**
   * Convert PNG to WebP and get image metadata
   */
  private async convertToWebP(inputPath: string, outputPath: string): Promise<{
    width: number
    height: number
    size: number
  }> {
    try {
      const metadata = await sharp(inputPath)
        .webp({ quality: 85, effort: 6 }) // High quality WebP
        .toFile(outputPath)

      return {
        width: metadata.width || 0,
        height: metadata.height || 0,
        size: metadata.size || 0
      }
    } catch (error) {
      console.error('Failed to convert to WebP:', error)
      throw error
    }
  }

  /**
   * Store image: download, convert to WebP, save metadata, delete PNG
   */
  async storeImage(
    imageUrl: string, 
    headingText?: string, 
    contentId?: string
  ): Promise<StoredImage> {
    await this.ensureDirectories()

    const filename = this.generateFilename(headingText)
    const pngPath = path.join(this.storageDir, `${filename}.png`)
    const webpPath = path.join(this.webpDir, `${filename}.webp`)
    
    try {
      console.log(`📥 Downloading image: ${imageUrl}`)
      
      // Download original image as PNG
      await this.downloadImage(imageUrl, pngPath)
      
      console.log(`🔄 Converting to WebP: ${filename}`)
      
      // Convert to WebP and get metadata
      const { width, height, size } = await this.convertToWebP(pngPath, webpPath)
      
      console.log(`🗑️ Removing original PNG: ${filename}.png`)
      
      // Delete the original PNG file to save space
      await fs.unlink(pngPath)
      
      // Create database record
      const storedImage = await prisma.storedImage.create({
        data: {
          originalUrl: imageUrl,
          localPath: `/images/blog/webp/${filename}.webp`, // Web-accessible path
          filename: `${filename}.webp`,
          size,
          width,
          height,
          contentId,
          headingText,
        }
      })

      console.log(`✅ Image stored successfully: ${filename}.webp`)

      return {
        id: storedImage.id,
        originalUrl: imageUrl,
        localPath: `/images/blog/webp/${filename}.webp`,
        webpPath,
        filename: `${filename}.webp`,
        size,
        width,
        height,
        contentId,
        headingText,
        createdAt: storedImage.createdAt
      }

    } catch (error) {
      // Cleanup on error
      try {
        await fs.unlink(pngPath).catch(() => {}) // Ignore if file doesn't exist
        await fs.unlink(webpPath).catch(() => {}) // Ignore if file doesn't exist
      } catch (cleanupError) {
        console.warn('Failed to cleanup files on error:', cleanupError)
      }
      
      console.error('Failed to store image:', error)
      throw error
    }
  }

  /**
   * Get stored images for content
   */
  async getImagesForContent(contentId: string): Promise<StoredImage[]> {
    try {
      const images = await prisma.storedImage.findMany({
        where: { contentId },
        orderBy: { createdAt: 'asc' }
      })

      return images.map(img => ({
        id: img.id,
        originalUrl: img.originalUrl,
        localPath: img.localPath,
        webpPath: path.join(this.webpDir, img.filename),
        filename: img.filename,
        size: img.size,
        width: img.width,
        height: img.height,
        contentId: img.contentId || undefined,
        headingText: img.headingText || undefined,
        createdAt: img.createdAt
      }))
    } catch (error) {
      console.error('Failed to get images for content:', error)
      return []
    }
  }

  /**
   * Delete stored image and cleanup files
   */
  async deleteImage(imageId: string): Promise<boolean> {
    try {
      const image = await prisma.storedImage.findUnique({
        where: { id: imageId }
      })

      if (!image) {
        return false
      }

      // Delete physical file
      const fullPath = path.join(process.cwd(), 'public', image.localPath)
      await fs.unlink(fullPath).catch(() => {}) // Ignore if file doesn't exist

      // Delete database record
      await prisma.storedImage.delete({
        where: { id: imageId }
      })

      console.log(`🗑️ Deleted stored image: ${image.filename}`)
      return true

    } catch (error) {
      console.error('Failed to delete image:', error)
      return false
    }
  }

  /**
   * Cleanup old images (older than 30 days with no contentId)
   */
  async cleanupOldImages(): Promise<number> {
    try {
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const oldImages = await prisma.storedImage.findMany({
        where: {
          createdAt: { lt: thirtyDaysAgo },
          contentId: null // Only cleanup images not associated with content
        }
      })

      let deletedCount = 0
      for (const image of oldImages) {
        const success = await this.deleteImage(image.id)
        if (success) deletedCount++
      }

      console.log(`🧹 Cleaned up ${deletedCount} old images`)
      return deletedCount

    } catch (error) {
      console.error('Failed to cleanup old images:', error)
      return 0
    }
  }
}

// Export singleton instance
export const imageStorageService = new ImageStorageService()
