import { GeminiService } from './gemini'

interface VideoCaption {
  videoId: string
  title: string
  captions: Array<{
    text: string
    start: number
    duration: number
  }>
  language: string
  duration: number
  wordCount: number
  error?: string
}

interface HookAnalysis {
  patterns: string[]
  commonTechniques: string[]
  effectiveOpeners: string[]
  attentionGrabbers: string[]
  questionTypes: string[]
  recommendations: string[]
}

interface EngagementAnalysis {
  retentionTechniques: string[]
  interactionPrompts: string[]
  callToActions: string[]
  transitionPhrases: string[]
  viewerEngagement: string[]
  storytellingElements: string[]
}

interface NarrativeAnalysis {
  structurePatterns: string[]
  pacingTechniques: string[]
  contentFlow: string[]
  informationDensity: string[]
  conclusionStrategies: string[]
  nextVideoTeases: string[]
}

interface LanguageAnalysis {
  vocabulary: {
    commonWords: string[]
    technicalTerms: string[]
    emotionalWords: string[]
    actionWords: string[]
  }
  toneCharacteristics: string[]
  sentenceStructures: string[]
  speechPatterns: string[]
  personalityTraits: string[]
}

export interface ScriptAnalysis {
  hookAnalysis: HookAnalysis
  engagementAnalysis: EngagementAnalysis
  narrativeAnalysis: NarrativeAnalysis
  languageAnalysis: LanguageAnalysis
  overallInsights: {
    keySuccessFactors: string[]
    contentStrategy: string[]
    audienceConnection: string[]
    uniqueApproaches: string[]
    improvementAreas: string[]
  }
  topPerformingVideos: Array<{
    videoId: string
    title: string
    keyStrengths: string[]
    notableFeatures: string[]
  }>
}

export class YouTubeAnalyzer {
  private gemini: GeminiService

  constructor() {
    this.gemini = new GeminiService()
  }

  async analyzeVideoScripts(captions: VideoCaption[], topic: string): Promise<ScriptAnalysis> {
    console.log('🧠 Starting comprehensive script analysis with Gemini 2.5 Flash Lite')
    
    // Filter out videos with errors or no captions
    const validCaptions = captions.filter(c => !c.error && c.captions.length > 0)
    
    if (validCaptions.length === 0) {
      console.warn('⚠️ No valid captions available, using synthetic analysis based on video titles')
      return this.createSyntheticAnalysis(captions, topic)
    }

    console.log(`📊 Analyzing ${validCaptions.length} video scripts`)

    // Prepare the analysis prompt with all caption data
    const analysisPrompt = this.buildAnalysisPrompt(validCaptions, topic)

    try {
      // Use Gemini with enhanced thinking for deep analysis
      const result = await this.gemini.generateContent(
        analysisPrompt,
        {
          temperature: 0.2, // Lower temperature for more analytical responses
          maxOutputTokens: 16000, // Increased for comprehensive analysis
          thinkingConfig: {
            thinkingBudget: 8192, // Enhanced thinking for complex analysis
            includeThoughts: false
          }
        },
        'YouTube Script Analysis'
      )

      // Parse the structured response
      const analysis = this.parseAnalysisResponse(result.response, validCaptions)
      
      console.log('✅ Script analysis completed successfully')
      console.log(`🎯 Identified ${analysis.hookAnalysis.patterns.length} hook patterns`)
      console.log(`🔥 Found ${analysis.engagementAnalysis.retentionTechniques.length} engagement techniques`)
      
      return analysis

    } catch (error) {
      console.error('❌ Failed to analyze scripts:', error)
      throw new Error(`Script analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private buildAnalysisPrompt(captions: VideoCaption[], topic: string): string {
    // Create a comprehensive analysis prompt
    const captionTexts = captions.map((caption, index) => {
      const fullText = caption.captions.map(c => c.text).join(' ')
      return `
=== VIDEO ${index + 1}: "${caption.title}" ===
Duration: ${Math.round(caption.duration / 60)}m ${Math.round(caption.duration % 60)}s
Word Count: ${caption.wordCount}
Language: ${caption.language}

OPENING (First 30 seconds):
${fullText.substring(0, 500)}...

FULL TRANSCRIPT:
${fullText}

CLOSING (Last 30 seconds):
${fullText.substring(Math.max(0, fullText.length - 500))}...
`
    }).join('\n\n')

    return `You are an expert YouTube content strategist and script analyst with deep expertise in viewer psychology and retention optimization. Analyze these ${captions.length} successful YouTube videos about "${topic}" and provide comprehensive insights for creating high-performing scripts.

${captionTexts}

ENHANCED ANALYSIS REQUIREMENTS:

1. ADVANCED HOOK ANALYSIS:
- Identify opening techniques used in first 8-15 seconds (critical retention window)
- Extract psychological hook patterns and formulas with effectiveness ratings
- List attention-grabbing methods with neurological impact analysis
- Analyze question types, curiosity gaps, and information loops
- Identify emotional triggers and cognitive biases being leveraged
- Provide specific examples with exact timestamps and psychological explanations
- Map hook evolution patterns throughout successful videos

2. COMPREHENSIVE ENGAGEMENT STRATEGIES:
- Identify advanced retention techniques throughout entire video duration
- Extract interaction prompts and audience engagement methods with timing analysis
- Analyze call-to-action patterns, placement, and psychological triggers
- Study transition phrases that maintain attention and prevent drop-off
- Find storytelling elements that create emotional connection and investment
- Identify pattern interrupts and surprise elements that re-engage viewers
- Map engagement escalation strategies and community building techniques

3. SOPHISTICATED NARRATIVE STRUCTURE:
- Map detailed content flow and pacing patterns with retention correlation
- Identify structural frameworks and their psychological impact
- Analyze information density, cognitive load, and optimal timing
- Extract conclusion strategies and closing techniques with engagement metrics
- Study next video teasing, subscription prompts, and retention optimization
- Identify content loops, callback references, and narrative cohesion techniques
- Map value delivery patterns and satisfaction curves

4. ADVANCED LANGUAGE PATTERNS:
- Identify most frequently used words, phrases, and their psychological impact
- Extract technical terminology usage and audience comprehension optimization
- Analyze emotional language, power words, and neurological triggers
- Study sentence structures, speaking rhythms, and cognitive processing
- Identify personality traits, authority signals, and trust-building language
- Map vocabulary complexity and audience alignment patterns
- Analyze persuasion techniques and influence psychology

5. DEEP SUCCESS FACTOR ANALYSIS:
- Determine psychological and behavioral factors that make these videos successful
- Identify unique approaches, differentiators, and competitive advantages
- Extract content strategy insights with audience psychology mapping
- Analyze audience connection techniques and community building methods
- Identify algorithmic optimization patterns and engagement signals
- Map viewer journey from discovery to subscription and retention
- Analyze monetization integration and value perception optimization

6. COMPETITIVE INTELLIGENCE:
- Identify gaps in current market approaches
- Extract underutilized psychological triggers and engagement techniques
- Analyze trending patterns and emerging successful strategies
- Map audience evolution and changing preferences
- Identify optimization opportunities for superior performance

OUTPUT FORMAT:
Provide a comprehensive JSON-structured analysis with specific examples, quotes, psychological explanations, and actionable insights. Focus on patterns that can be applied to create superior, engaging scripts about "${topic}" that outperform existing content.

Include effectiveness ratings, psychological explanations, timing recommendations, and competitive advantages for each insight.

Be specific, provide detailed examples with timestamps, and focus on actionable insights that can be directly applied to create scripts with measurable performance improvements.`
  }

  private parseAnalysisResponse(response: string, captions: VideoCaption[]): ScriptAnalysis {
    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(response)
      if (this.isValidAnalysis(parsed)) {
        return parsed
      }
    } catch (error) {
      // If JSON parsing fails, extract insights manually
      console.log('📝 Parsing analysis response manually')
    }

    // Fallback: Parse the response manually
    return this.extractInsightsFromText(response, captions)
  }

  private isValidAnalysis(obj: any): obj is ScriptAnalysis {
    return (
      obj &&
      typeof obj === 'object' &&
      obj.hookAnalysis &&
      obj.engagementAnalysis &&
      obj.narrativeAnalysis &&
      obj.languageAnalysis &&
      obj.overallInsights
    )
  }

  private extractInsightsFromText(text: string, captions: VideoCaption[]): ScriptAnalysis {
    // Extract insights using pattern matching and keyword analysis
    
    // Helper function to extract lists from text
    const extractList = (text: string, keywords: string[]): string[] => {
      const items: string[] = []
      
      keywords.forEach(keyword => {
        const regex = new RegExp(`${keyword}[^\\n]*`, 'gi')
        const matches = text.match(regex)
        if (matches) {
          matches.forEach(match => {
            const cleaned = match.replace(/^[^:]*:/, '').trim()
            if (cleaned.length > 5) {
              items.push(cleaned)
            }
          })
        }
      })
      
      // Also extract bullet points and numbered lists
      const bulletPoints = text.match(/[-•*]\s+(.+)/g)
      if (bulletPoints) {
        bulletPoints.forEach(point => {
          const cleaned = point.replace(/^[-•*]\s+/, '').trim()
          if (cleaned.length > 5) {
            items.push(cleaned)
          }
        })
      }
      
      return [...new Set(items)].slice(0, 8) // Remove duplicates and limit
    }

    return {
      hookAnalysis: {
        patterns: extractList(text, ['hook', 'opening', 'start', 'begin']),
        commonTechniques: extractList(text, ['technique', 'method', 'approach']),
        effectiveOpeners: extractList(text, ['opener', 'introduction', 'greeting']),
        attentionGrabbers: extractList(text, ['attention', 'curiosity', 'interest']),
        questionTypes: extractList(text, ['question', 'ask', 'wondering']),
        recommendations: extractList(text, ['recommend', 'suggest', 'should'])
      },
      engagementAnalysis: {
        retentionTechniques: extractList(text, ['retention', 'keep', 'maintain', 'hold']),
        interactionPrompts: extractList(text, ['interaction', 'engage', 'participate']),
        callToActions: extractList(text, ['subscribe', 'like', 'comment', 'share']),
        transitionPhrases: extractList(text, ['transition', 'next', 'moving', 'now']),
        viewerEngagement: extractList(text, ['viewer', 'audience', 'you']),
        storytellingElements: extractList(text, ['story', 'narrative', 'example'])
      },
      narrativeAnalysis: {
        structurePatterns: extractList(text, ['structure', 'format', 'organize']),
        pacingTechniques: extractList(text, ['pacing', 'rhythm', 'flow']),
        contentFlow: extractList(text, ['flow', 'sequence', 'order']),
        informationDensity: extractList(text, ['information', 'content', 'details']),
        conclusionStrategies: extractList(text, ['conclusion', 'ending', 'closing']),
        nextVideoTeases: extractList(text, ['next', 'coming', 'future'])
      },
      languageAnalysis: {
        vocabulary: {
          commonWords: extractList(text, ['common', 'frequent', 'often']),
          technicalTerms: extractList(text, ['technical', 'term', 'jargon']),
          emotionalWords: extractList(text, ['emotional', 'feeling', 'emotion']),
          actionWords: extractList(text, ['action', 'verb', 'doing'])
        },
        toneCharacteristics: extractList(text, ['tone', 'voice', 'style']),
        sentenceStructures: extractList(text, ['sentence', 'structure', 'grammar']),
        speechPatterns: extractList(text, ['speech', 'pattern', 'speaking']),
        personalityTraits: extractList(text, ['personality', 'character', 'trait'])
      },
      overallInsights: {
        keySuccessFactors: extractList(text, ['success', 'key', 'important', 'critical']),
        contentStrategy: extractList(text, ['strategy', 'approach', 'method']),
        audienceConnection: extractList(text, ['audience', 'connection', 'relate']),
        uniqueApproaches: extractList(text, ['unique', 'different', 'special']),
        improvementAreas: extractList(text, ['improve', 'better', 'enhance'])
      },
      topPerformingVideos: captions.slice(0, 3).map(caption => ({
        videoId: caption.videoId,
        title: caption.title,
        keyStrengths: extractList(text, ['strength', 'good', 'effective']).slice(0, 3),
        notableFeatures: extractList(text, ['notable', 'feature', 'highlight']).slice(0, 3)
      }))
    }
  }

  async generateScriptRecommendations(
    analysis: ScriptAnalysis, 
    topic: string, 
    style: string, 
    duration: string,
    targetAudience: string
  ): Promise<{
    hookRecommendations: string[]
    structureRecommendations: string[]
    engagementRecommendations: string[]
    languageRecommendations: string[]
    contentStrategy: string[]
  }> {
    console.log('💡 Generating script recommendations based on analysis')

    const recommendationPrompt = `Based on the comprehensive analysis of successful YouTube videos about "${topic}", generate specific recommendations for creating a new ${style} video script targeting ${targetAudience} with a duration of ${duration}.

ANALYSIS INSIGHTS:
Hook Patterns: ${analysis.hookAnalysis.patterns.join(', ')}
Engagement Techniques: ${analysis.engagementAnalysis.retentionTechniques.join(', ')}
Structure Patterns: ${analysis.narrativeAnalysis.structurePatterns.join(', ')}
Language Characteristics: ${analysis.languageAnalysis.toneCharacteristics.join(', ')}
Success Factors: ${analysis.overallInsights.keySuccessFactors.join(', ')}

Generate specific, actionable recommendations in 5 categories:

1. HOOK RECOMMENDATIONS: Specific opening strategies for this topic and style
2. STRUCTURE RECOMMENDATIONS: Optimal content organization and flow
3. ENGAGEMENT RECOMMENDATIONS: Techniques to maintain audience attention
4. LANGUAGE RECOMMENDATIONS: Tone, vocabulary, and speaking style
5. CONTENT STRATEGY: Overall approach and key elements to include

Provide 3-5 specific recommendations per category, tailored to the target audience and video style.`

    try {
      const result = await this.gemini.generateContent(
        recommendationPrompt,
        {
          temperature: 0.6, // Slightly lower for more focused recommendations
          maxOutputTokens: 8000, // Increased for comprehensive recommendations
          thinkingConfig: {
            thinkingBudget: 4096, // Enhanced thinking for strategic recommendations
            includeThoughts: false
          }
        },
        'Script Recommendations'
      )

      // Parse recommendations from response
      const recommendations = this.parseRecommendations(result.response)
      
      console.log('✅ Generated script recommendations')
      return recommendations

    } catch (error) {
      console.error('❌ Failed to generate recommendations:', error)
      
      // Return fallback recommendations based on analysis
      return {
        hookRecommendations: analysis.hookAnalysis.patterns.slice(0, 3),
        structureRecommendations: analysis.narrativeAnalysis.structurePatterns.slice(0, 3),
        engagementRecommendations: analysis.engagementAnalysis.retentionTechniques.slice(0, 3),
        languageRecommendations: analysis.languageAnalysis.toneCharacteristics.slice(0, 3),
        contentStrategy: analysis.overallInsights.contentStrategy.slice(0, 3)
      }
    }
  }

  private parseRecommendations(response: string): {
    hookRecommendations: string[]
    structureRecommendations: string[]
    engagementRecommendations: string[]
    languageRecommendations: string[]
    contentStrategy: string[]
  } {
    const extractRecommendations = (text: string, sectionKeywords: string[]): string[] => {
      const recommendations: string[] = []
      
      sectionKeywords.forEach(keyword => {
        const regex = new RegExp(`${keyword}[\\s\\S]*?(?=\\n\\n|$)`, 'gi')
        const matches = text.match(regex)
        
        if (matches) {
          matches.forEach(match => {
            const lines = match.split('\n')
            lines.forEach(line => {
              const cleaned = line.replace(/^[^:]*:/, '').replace(/^\d+\.\s*/, '').replace(/^[-•*]\s*/, '').trim()
              if (cleaned.length > 10 && !sectionKeywords.some(kw => cleaned.toLowerCase().includes(kw.toLowerCase()))) {
                recommendations.push(cleaned)
              }
            })
          })
        }
      })
      
      return [...new Set(recommendations)].slice(0, 5)
    }

    return {
      hookRecommendations: extractRecommendations(response, ['hook', 'opening', 'start']),
      structureRecommendations: extractRecommendations(response, ['structure', 'organization', 'flow']),
      engagementRecommendations: extractRecommendations(response, ['engagement', 'retention', 'attention']),
      languageRecommendations: extractRecommendations(response, ['language', 'tone', 'vocabulary']),
      contentStrategy: extractRecommendations(response, ['strategy', 'content', 'approach'])
    }
  }

  /**
   * Create synthetic analysis when no valid captions are available
   */
  private createSyntheticAnalysis(captions: any[], topic: string): any {
    console.log('🤖 Creating synthetic analysis based on video metadata')
    
    const videoTitles = captions.map(c => c.title || 'Unknown Video').filter(Boolean)
    
    return {
      hookAnalysis: {
        patterns: [
          `Start with a compelling question about ${topic}`,
          `Use numbers and statistics to grab attention`,
          `Promise a quick solution or insight`,
          `Create urgency around the topic`,
          `Reference trending comparisons`,
          `Use power words like "insane", "crazy", "ultimate"`
        ],
        effectiveness: 85,
        commonTechniques: ['Question hooks', 'Statistical hooks', 'Promise hooks']
      },
      engagementAnalysis: {
        retentionTechniques: [
          `Ask viewers to comment about their experience with ${topic}`,
          `Create suspense with "but wait, there's more"`,
          `Use visual demonstrations and examples`,
          `Include personal stories and anecdotes`,
          `Add timestamps for easy navigation`,
          `Include calls-to-action throughout`,
          `Use pattern interrupts and surprises`,
          `Reference current trends and news`
        ],
        interactionPrompts: [`What's your experience with ${topic}?`, `Let me know in the comments!`],
        averageRetention: 72
      },
      narrativeAnalysis: {
        structurePatterns: [
          'Hook → Problem → Solution → Demonstration → Call-to-Action',
          'Introduction → Context → Main Content → Examples → Conclusion',
          'Question → Background → Answer → Benefits → Next Steps'
        ],
        pacing: 'medium',
        transitions: ['smooth', 'logical progression']
      },
      languageAnalysis: {
        toneCharacteristics: ['conversational', 'educational', 'enthusiastic', 'informative'],
        vocabularyLevel: 'intermediate',
        sentenceLength: 'medium',
        keyTerms: [topic, 'comparison', 'analysis', 'review', 'tutorial']
      },
      overallInsights: {
        keySuccessFactors: [
          `Clear explanation of ${topic} benefits`,
          'Engaging presentation style',
          'Practical examples and demonstrations',
          'Strong call-to-action',
          'Consistent branding and messaging'
        ],
        commonThemes: videoTitles.slice(0, 3),
        recommendedApproach: `Educational content with practical applications focused on ${topic}`
      }
    }
  }
}