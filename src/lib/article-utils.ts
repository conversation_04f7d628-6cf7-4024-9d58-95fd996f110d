/**
 * Article Utilities for Clean URL Management
 */

interface StoreArticleParams {
  title: string;
  content: string;
  type: 'blog' | 'email' | 'youtube_script' | 'social_media';
  metadata?: any;
  tone?: string;
  language?: string;
}

interface StoreArticleResult {
  success: boolean;
  article?: {
    id: string;
    title: string;
    type: string;
    wordCount: number;
    createdAt: string;
  };
  url?: string;
  error?: string;
}

/**
 * Store an article and get a clean URL
 */
export async function storeArticle(params: StoreArticleParams): Promise<StoreArticleResult> {
  try {
    const response = await fetch('/api/articles/store', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to store article');
    }

    return {
      success: true,
      article: data.article,
      url: data.url,
    };
  } catch (error) {
    console.error('Error storing article:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to store article',
    };
  }
}

/**
 * Store article and redirect to clean URL
 */
export async function storeAndRedirect(
  params: StoreArticleParams, 
  router: any
): Promise<void> {
  try {
    const result = await storeArticle(params);

    if (result.success && result.url) {
      // Clear old localStorage data
      localStorage.removeItem('generatedArticle');
      localStorage.removeItem('articleTitle');
      localStorage.removeItem('articleScores');
      
      // Redirect to clean URL
      router.push(result.url);
    } else {
      throw new Error(result.error || 'Failed to store article');
    }
  } catch (error) {
    console.error('Error in storeAndRedirect:', error);
    
    // Fallback: Store in localStorage and redirect to article-view without query params
    // The article-view page will handle migration from localStorage to clean URLs
    localStorage.setItem('generatedArticle', params.content);
    localStorage.setItem('articleTitle', params.title);
    if (params.metadata) {
      localStorage.setItem('articleScores', JSON.stringify(params.metadata));
    }
    
    // Redirect to the article view page without problematic query parameters
    router.push('/article-view');
  }
}