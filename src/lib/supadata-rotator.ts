/**
 * Supadata.ai API Key Rotation System
 * Manages multiple Supadata API keys with automatic rotation and quota management
 */

export class SupadataApiKeyRotator {
  private apiKeys: string[]
  private currentKeyIndex: number
  private keyQuotaStatus: Map<string, { hitLimit: boolean; resetTime?: Date; errorCount: number }>
  private lastRotationTime: Date

  constructor() {
    // Supadata API keys with fallbacks
    const fallbackKeys = [
      'sd_5f872ba7dcf51fc00fc258abaff006aa', // Original key
      'sd_7398534f0ba23be07bf5e47af32c8115',
      'sd_f910aa820b3235291c439ba0fed0faaf',
      'sd_211858172e68670ee607e143f11a9cd5',
      'sd_077f1d631c97d0c84ba0e33ffc4165aa',
      'sd_017aaee3312bd4d2285e2ce6f2f360c9',
      'sd_682a66ee222fb692abae8103f387951d'
    ];

    // Add environment key at the beginning if available, otherwise use all fallback keys
    const envKey = process.env.SUPADATA_API_KEY;
    const keys = envKey ? [envKey, ...fallbackKeys] : fallbackKeys;

    this.apiKeys = keys
    this.currentKeyIndex = 0
    this.keyQuotaStatus = new Map()
    this.lastRotationTime = new Date()

    console.log(`🔑 SupadataApiKeyRotator initialized with ${this.apiKeys.length} API keys`)

    // Initialize quota status for all keys
    this.apiKeys.forEach(key => {
      this.keyQuotaStatus.set(key, { hitLimit: false, errorCount: 0 })
    })
  }

  getCurrentApiKey(): string {
    const currentKey = this.apiKeys[this.currentKeyIndex]
    const status = this.keyQuotaStatus.get(currentKey)
    
    // Reset quota status if enough time has passed (24 hours)
    if (status?.hitLimit && status.resetTime && new Date() >= status.resetTime) {
      this.keyQuotaStatus.set(currentKey, { hitLimit: false, errorCount: 0 })
      console.log(`🔄 Supadata API key quota reset for key ending in ...${currentKey.slice(-4)}`)
      return currentKey
    }

    // Check if current key is still valid
    if (status?.hitLimit && status.resetTime && new Date() < status.resetTime) {
      console.log(`🔄 Current Supadata key has hit limit, auto-rotating...`)
      this.rotateToNextValidKey()
      // Return the new key after rotation without recursive call
      return this.apiKeys[this.currentKeyIndex]
    }

    return currentKey
  }

  private rotateToNextValidKey(): void {
    const startIndex = this.currentKeyIndex
    let rotationAttempts = 0
    const maxRotationAttempts = this.apiKeys.length * 2 // Allow double rotation
    
    do {
      this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length
      rotationAttempts++
      
      const key = this.apiKeys[this.currentKeyIndex]
      const status = this.keyQuotaStatus.get(key)
      
      // Check if this key is available (prioritize keys with fewer errors)
      if (!status?.hitLimit || (status.resetTime && new Date() >= status.resetTime)) {
        console.log(`🔄 SUPADATA ROTATION: Switched to API key ${this.currentKeyIndex + 1}/${this.apiKeys.length} (errors: ${status?.errorCount || 0})`)
        this.lastRotationTime = new Date()
        return
      }
      
      // Prevent infinite loops
      if (rotationAttempts >= maxRotationAttempts) {
        console.warn(`⚠️ Supadata rotation attempts exceeded, using current key anyway`)
        break
      }
    } while (this.currentKeyIndex !== startIndex)
    
    // If we've checked all keys and none are available
    console.warn('⚠️ All Supadata API keys have issues. Using least problematic key.')
    this.findLeastProblematicKey()
  }

  private findLeastProblematicKey(): void {
    // First, try to find any key that has expired reset time
    for (let i = 0; i < this.apiKeys.length; i++) {
      const key = this.apiKeys[i]
      const status = this.keyQuotaStatus.get(key)
      
      if (status?.hitLimit && status.resetTime && new Date() >= status.resetTime) {
        // Reset this key since its quota should be refreshed
        this.keyQuotaStatus.set(key, { hitLimit: false, errorCount: status.errorCount })
        this.currentKeyIndex = i
        console.log(`🔄 Found expired quota key: ${i + 1}/${this.apiKeys.length}, resetting and using`)
        return
      }
    }
    
    // If no expired keys, find key with lowest error count
    let bestKeyIndex = 0
    let bestScore = Infinity
    
    this.apiKeys.forEach((key, index) => {
      const status = this.keyQuotaStatus.get(key)
      let score = status?.errorCount || 0
      
      // Add penalty for active limits
      if (status?.hitLimit && status.resetTime && new Date() < status.resetTime) {
        score += 1000 // Heavy penalty for active limits
      }
      
      if (score < bestScore) {
        bestScore = score
        bestKeyIndex = index
      }
    })
    
    this.currentKeyIndex = bestKeyIndex
    console.log(`🔄 Selected least problematic Supadata key: ${bestKeyIndex + 1}/${this.apiKeys.length} (score: ${bestScore})`)
  }

  markKeyAsQuotaExceeded(apiKey: string): void {
    const resetTime = new Date()
    resetTime.setHours(resetTime.getHours() + 24) // Reset after 24 hours
    
    const currentStatus = this.keyQuotaStatus.get(apiKey) || { hitLimit: false, errorCount: 0 }
    
    this.keyQuotaStatus.set(apiKey, { 
      hitLimit: true, 
      resetTime,
      errorCount: currentStatus.errorCount + 1
    })
    
    console.warn(`⚠️ Supadata API key ending in ...${apiKey.slice(-4)} marked as exhausted (errors: ${currentStatus.errorCount + 1}). Will reset at ${resetTime.toISOString()}`)
    
    // Immediately rotate to next available key
    this.rotateToNextValidKey()
  }

  markKeyError(apiKey: string, errorType: string = 'generic'): void {
    const currentStatus = this.keyQuotaStatus.get(apiKey) || { hitLimit: false, errorCount: 0 }
    
    this.keyQuotaStatus.set(apiKey, {
      ...currentStatus,
      errorCount: currentStatus.errorCount + 1
    })
    
    console.warn(`⚠️ Supadata API key ending in ...${apiKey.slice(-4)} error count: ${currentStatus.errorCount + 1} (${errorType})`)
    
    // If error count is high, consider rotating
    if (currentStatus.errorCount >= 3) {
      console.log(`🔄 Supadata key has high error count, rotating to different key`)
      this.rotateToNextValidKey()
    }
  }

  forceRotate(): string {
    console.log(`🔄 SUPADATA FORCE ROTATION requested`)
    this.rotateToNextValidKey()
    const newKey = this.getCurrentApiKey()
    console.log(`🔄 Supadata force rotation complete: now using key ending in ...${newKey.slice(-4)}`)
    return newKey
  }

  instantRotate(reason: string): string {
    console.log(`⚡ SUPADATA INSTANT ROTATION: ${reason}`)
    const oldKey = this.getCurrentApiKey()
    this.rotateToNextValidKey()
    const newKey = this.getCurrentApiKey()
    console.log(`⚡ Supadata instant rotation: ...${oldKey.slice(-4)} → ...${newKey.slice(-4)}`)
    return newKey
  }

  getStatus(): {
    totalKeys: number
    currentKeyIndex: number
    availableKeys: number
    lastRotation: Date
    keyHealthReport: Array<{ keyId: string; status: string; errors: number }>
  } {
    const availableKeys = Array.from(this.keyQuotaStatus.values())
      .filter(status => !status.hitLimit || (status.resetTime && new Date() >= status.resetTime))
      .length

    const keyHealthReport = this.apiKeys.map((key, index) => {
      const status = this.keyQuotaStatus.get(key)
      const isActive = !status?.hitLimit || (status.resetTime && new Date() >= status.resetTime)
      
      return {
        keyId: `Supadata Key ${index + 1} (...${key.slice(-4)})`,
        status: isActive ? 'active' : 'limited',
        errors: status?.errorCount || 0
      }
    })

    return {
      totalKeys: this.apiKeys.length,
      currentKeyIndex: this.currentKeyIndex,
      availableKeys,
      lastRotation: this.lastRotationTime,
      keyHealthReport
    }
  }

  resetKeyErrors(apiKey: string): void {
    const currentStatus = this.keyQuotaStatus.get(apiKey)
    if (currentStatus) {
      this.keyQuotaStatus.set(apiKey, {
        ...currentStatus,
        errorCount: 0
      })
      console.log(`🔄 Reset error count for Supadata key ending in ...${apiKey.slice(-4)}`)
    }
  }

  resetAllKeys(): void {
    console.log(`🔄 SUPADATA EMERGENCY RESET: Resetting all API keys`)
    this.apiKeys.forEach(key => {
      this.keyQuotaStatus.set(key, { hitLimit: false, errorCount: 0 })
    })
    this.currentKeyIndex = 0
    console.log(`🔄 All Supadata keys reset, starting from key 1`)
  }

  // Method to handle specific Supadata error codes
  handleSupadataError(apiKey: string, error: any): boolean {
    if (!error.response) {
      this.markKeyError(apiKey, 'network_error')
      return false
    }

    const status = error.response.status
    const errorMessage = error.response.data?.error || error.message || 'Unknown error'

    switch (status) {
      case 401:
        console.warn(`🔑 Supadata API key invalid: ...${apiKey.slice(-4)}`)
        this.markKeyAsQuotaExceeded(apiKey)
        return true // Should retry with different key

      case 429:
        console.warn(`📈 Supadata API rate limit exceeded: ...${apiKey.slice(-4)}`)
        this.markKeyAsQuotaExceeded(apiKey)
        return true // Should retry with different key

      case 402:
        console.warn(`💳 Supadata API quota exceeded: ...${apiKey.slice(-4)}`)
        this.markKeyAsQuotaExceeded(apiKey)
        return true // Should retry with different key

      case 403:
        console.warn(`🚫 Supadata API access forbidden: ...${apiKey.slice(-4)}`)
        this.markKeyError(apiKey, 'forbidden')
        return true // Should retry with different key

      case 404:
        console.warn(`📹 Video not found or no transcripts available`)
        // Don't mark key as problematic for 404s - it's video-specific
        return false // Don't retry

      case 500:
      case 502:
      case 503:
        console.warn(`🔧 Supadata API server error (${status}): ...${apiKey.slice(-4)}`)
        this.markKeyError(apiKey, `server_error_${status}`)
        return true // Should retry with different key

      default:
        console.warn(`⚠️ Supadata API unexpected error (${status}): ${errorMessage}`)
        this.markKeyError(apiKey, `unknown_${status}`)
        return false // Don't retry by default
    }
  }
} 