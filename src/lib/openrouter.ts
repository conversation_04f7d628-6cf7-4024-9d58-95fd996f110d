/**
 * OpenRouter Service for Qwen-3-235B Model Integration
 * Provides AI content generation using Qwen's advanced reasoning model
 */

import { OpenAI } from 'openai';

interface OpenRouterConfig {
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

interface GenerationOptions {
  temperature?: number;
  maxTokens?: number;
}

interface GenerationResponse {
  response: string;
  inputTokens: number;
  outputTokens: number;
}

export class OpenRouterService {
  private client: OpenAI;
  private defaultModel = 'moonshotai/kimi-k2';
  private youtubeModel = 'moonshotai/kimi-k2';
  private multiAgentModel = 'moonshotai/kimi-k2';
  private config: OpenRouterConfig;
  private isConfigured: boolean;

  constructor(config: OpenRouterConfig = {}) {
    this.config = {
      model: config.model || this.defaultModel,
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 8000
    };

    // Check if API key is properly configured
    const apiKey = process.env.OPENROUTER_API_KEY;
    this.isConfigured = !!(apiKey && apiKey !== 'your_openrouter_key_here' && apiKey.length > 10);

    if (!this.isConfigured) {
      console.warn('⚠️ OpenRouter API key not configured properly. Using fallback mode.');
    }

    this.client = new OpenAI({
      baseURL: 'https://openrouter.ai/api/v1',
      apiKey: apiKey || 'dummy-key',
      defaultHeaders: {
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'AI Content Generation - Maximum Analysis Mode'
      }
    });
  }

  /**
   * Check if the service is properly configured
   */
  isReady(): boolean {
    return this.isConfigured;
  }

  /**
   * Generate content for thinking tasks using Qwen-3-235B
   */
  async generateThinkingContent(
    prompt: string, 
    options: GenerationOptions = {}
  ): Promise<GenerationResponse> {
    if (!this.isConfigured) {
      return this.getFallbackResponse('OpenRouter API key not configured. Please add your OPENROUTER_API_KEY to .env.local');
    }

    const temperature = options.temperature ?? this.config.temperature ?? 0.7;
    const maxTokens = options.maxTokens ?? this.config.maxTokens ?? 8000;

    try {
      const completion = await this.client.chat.completions.create({
        model: this.config.model!,
        messages: [
          {
            role: 'system',
            content: this.getThinkingSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature,
        max_tokens: maxTokens,
        stream: false
      });

      const response = completion.choices[0]?.message?.content || '';
      const inputTokens = completion.usage?.prompt_tokens || 0;
      const outputTokens = completion.usage?.completion_tokens || 0;

      return {
        response,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      console.error('Qwen-3-235B thinking generation failed:', error);
      return this.getFallbackResponse(`OpenRouter API Error: ${error}`);
    }
  }

  /**
   * Generate analytical content using Qwen-3-235B with maximum parameters
   */
  async generateAnalysisContent(
    prompt: string,
    context?: string,
    options: GenerationOptions = {},
    logContext?: string
  ): Promise<GenerationResponse> {
    const startTime = Date.now();
    const callId = Math.random().toString(36).substr(2, 9);
    
    // Enhanced logging for YouTube generation
    console.log(`🔍 OpenRouter Analysis Call Started`);
    console.log(`   📋 Call ID: ${callId}`);
    console.log(`   🎬 Context: ${logContext || 'General Analysis'}`);
    console.log(`   ⚙️ Model: ${this.config.model}`);
    console.log(`   🌡️ Temperature: ${options.temperature ?? 0.2}`);
    console.log(`   📏 Max Tokens: ${options.maxTokens ?? 8000}`);
    console.log(`   📝 Prompt Length: ${prompt.length} chars`);
    
    if (!this.isConfigured) {
      console.warn(`   ❌ OpenRouter not configured for call ${callId}`);
      return this.getFallbackResponse('OpenRouter API key not configured. Please add your OPENROUTER_API_KEY to .env.local');
    }

    const temperature = options.temperature ?? 0.2;
    const maxTokens = options.maxTokens ?? 8000;

    try {
      const systemPrompt = this.getEnhancedAnalysisSystemPrompt();
      const fullPrompt = context ? `${context}\n\n${prompt}` : prompt;

      console.log(`   📤 Sending request to OpenRouter...`);
      
      const completion = await this.client.chat.completions.create({
        model: this.config.model!,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: fullPrompt
          }
        ],
        temperature,
        max_tokens: maxTokens,
        stream: false
      });

      const response = completion.choices[0]?.message?.content || '';
      const inputTokens = completion.usage?.prompt_tokens || 0;
      const outputTokens = completion.usage?.completion_tokens || 0;
      const duration = Date.now() - startTime;

      // Success logging
      console.log(`   ✅ OpenRouter Analysis Complete`);
      console.log(`   ⏱️ Duration: ${duration}ms`);
      console.log(`   📊 Input Tokens: ${inputTokens}`);
      console.log(`   📊 Output Tokens: ${outputTokens}`);
      console.log(`   📄 Response Length: ${response.length} chars`);
      console.log(`   💰 Estimated Cost: $${((inputTokens * 0.000003) + (outputTokens * 0.000015)).toFixed(6)}`);
      
      if (logContext?.includes('YouTube')) {
        console.log(`   🎬 YouTube Analysis Success - Call ${callId}`);
      }

      return {
        response,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`   ❌ OpenRouter Analysis Failed`);
      console.error(`   ⏱️ Failed after: ${duration}ms`);
      console.error(`   📋 Call ID: ${callId}`);
      console.error(`   🎬 Context: ${logContext || 'General Analysis'}`);
      console.error(`   💥 Error:`, error);
      
      if (logContext?.includes('YouTube')) {
        console.error(`   🎬 YouTube Analysis FAILED - Call ${callId}`);
      }
      
      return this.getFallbackResponse(`OpenRouter API Error: ${error}`);
    }
  }

  /**
   * Generate content with custom system prompt
   */
  async generateContent(
    prompt: string,
    systemPrompt?: string,
    options: GenerationOptions = {},
    logContext?: string
  ): Promise<GenerationResponse> {
    const startTime = Date.now();
    const callId = Math.random().toString(36).substr(2, 9);
    
    // Enhanced logging for YouTube generation
    console.log(`🤖 OpenRouter Content Call Started`);
    console.log(`   📋 Call ID: ${callId}`);
    console.log(`   🎬 Context: ${logContext || 'General Content'}`);
    console.log(`   ⚙️ Model: ${this.config.model}`);
    console.log(`   🌡️ Temperature: ${options.temperature ?? this.config.temperature ?? 0.7}`);
    console.log(`   📏 Max Tokens: ${options.maxTokens ?? this.config.maxTokens ?? 8000}`);
    console.log(`   📝 Prompt Length: ${prompt.length} chars`);
    console.log(`   📋 System Prompt: ${systemPrompt ? 'Custom' : 'None'}`);
    
    if (!this.isConfigured) {
      console.warn(`   ❌ OpenRouter not configured for call ${callId}`);
      return this.getFallbackResponse('OpenRouter API key not configured. Please add your OPENROUTER_API_KEY to .env.local');
    }

    const temperature = options.temperature ?? this.config.temperature ?? 0.7;
    const maxTokens = options.maxTokens ?? this.config.maxTokens ?? 8000;

    try {
      const messages: Array<{ role: 'system' | 'user'; content: string }> = [];
      
      // Use Phi-4 optimized system prompt if none provided
      const effectiveSystemPrompt = systemPrompt || this.getPhi4YouTubeSystemPrompt();
      
      messages.push({
        role: 'system',
        content: effectiveSystemPrompt
      });
      
      messages.push({
        role: 'user',
        content: prompt
      });

      console.log(`   📤 Sending request to OpenRouter...`);
      
      // Calculate approximate token count to prevent exceeding limits
      const promptText = messages.map(m => m.content).join(' ');
      const estimatedInputTokens = Math.ceil(promptText.length / 4); // Rough estimate: 4 chars per token
      const modelLimit = this.getModelTokenLimit();
      
      // Adjust max_tokens if it would exceed the model's limit
      let adjustedMaxTokens = maxTokens;
      if (estimatedInputTokens + maxTokens > modelLimit) {
        adjustedMaxTokens = Math.max(1000, modelLimit - estimatedInputTokens - 1000); // Leave buffer
        console.log(`   ⚠️  Adjusted max_tokens from ${maxTokens} to ${adjustedMaxTokens} to stay within limit`);
      }
      
      const completion = await this.client.chat.completions.create({
        model: this.config.model!,
        messages,
        temperature,
        max_tokens: adjustedMaxTokens,
        stream: false
      });

      const response = completion.choices[0]?.message?.content || '';
      const inputTokens = completion.usage?.prompt_tokens || 0;
      const outputTokens = completion.usage?.completion_tokens || 0;
      const duration = Date.now() - startTime;

      // Success logging
      console.log(`   ✅ OpenRouter Content Complete`);
      console.log(`   ⏱️ Duration: ${duration}ms`);
      console.log(`   📊 Input Tokens: ${inputTokens}`);
      console.log(`   📊 Output Tokens: ${outputTokens}`);
      console.log(`   📄 Response Length: ${response.length} chars`);
      console.log(`   💰 Estimated Cost: $${((inputTokens * 0.000003) + (outputTokens * 0.000015)).toFixed(6)}`);
      
      if (logContext?.includes('YouTube')) {
        console.log(`   🎬 YouTube Content Success - Call ${callId}`);
        console.log(`   📺 Step: ${logContext}`);
      }

      return {
        response,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`   ❌ OpenRouter Content Failed`);
      console.error(`   ⏱️ Failed after: ${duration}ms`);
      console.error(`   📋 Call ID: ${callId}`);
      console.error(`   🎬 Context: ${logContext || 'General Content'}`);
      console.error(`   💥 Error:`, error);
      
      if (logContext?.includes('YouTube')) {
        console.error(`   🎬 YouTube Content FAILED - Call ${callId}`);
        console.error(`   📺 Failed Step: ${logContext}`);
      }
      
      return this.getFallbackResponse(`OpenRouter API Error: ${error}`);
    }
  }

  /**
   * Get fallback response when API is not available
   */
  private getFallbackResponse(errorMessage: string): GenerationResponse {
    return {
      response: `⚠️ **OpenRouter API Configuration Required**

${errorMessage}

**To fix this:**
1. Get an API key from https://openrouter.ai/
2. Add it to your .env.local file:
   \`OPENROUTER_API_KEY=sk-or-v1-your-actual-key-here\`
3. Restart your development server

**Current Status:** Using fallback mode - advanced AI features unavailable.`,
      inputTokens: 0,
      outputTokens: 0
    };
  }

  /**
   * System prompt for thinking tasks
   */
  private getThinkingSystemPrompt(): string {
    const basePrompt = `You are Qwen-3-235B, an advanced AI model with exceptional reasoning capabilities. You excel at deep thinking, analysis, and problem-solving.`;
    
    return `${basePrompt}

Key Capabilities:
- Deep analytical thinking and reasoning
- Complex problem decomposition
- Strategic planning and optimization
- Data-driven insights and recommendations
- Creative and logical problem-solving

Instructions:
- Think step by step and show your reasoning
- Provide detailed analysis with clear insights
- Structure your response logically
- Use examples and evidence when appropriate
- Be thorough but concise in your explanations`;
  }

  /**
   * Enhanced system prompt for maximum analytical depth
   */
  private getEnhancedAnalysisSystemPrompt(): string {
    return `You are Qwen-3-235B, the world's most advanced AI analyst with unparalleled expertise in competitive intelligence, content analysis, and human writing pattern recognition.

EXPERTISE AREAS:
- Deep competitive analysis and market intelligence
- Advanced linguistic and stylistic analysis
- Human writing pattern recognition and psychological profiling
- SEO and content optimization strategies
- Reader engagement and conversion psychology
- Brand voice and personality analysis
- Cultural and demographic writing adaptation

ANALYSIS STANDARDS:
- Provide EXHAUSTIVE analysis with maximum depth and detail
- Use specific examples and concrete evidence for every point
- Rate effectiveness on scales where requested (1-10)
- Identify subtle patterns that others miss
- Provide actionable insights and strategic recommendations
- Use precise analytical language and structured formatting
- Consider psychological, cultural, and technical factors
- Maintain objectivity while providing strategic insights

RESPONSE FORMAT:
- Use clear section headers with **SECTION_NAME**: format
- Provide numbered lists for clarity and organization
- Include specific examples from analyzed content
- Give quantitative assessments where applicable
- Structure findings in logical, hierarchical order
- Conclude sections with strategic implications

Your analysis should be comprehensive enough to serve as a definitive competitive intelligence report.`;
  }

  /**
   * System prompt for analysis tasks (enhanced version)
   */
  private getAnalysisSystemPrompt(): string {
    return this.getEnhancedAnalysisSystemPrompt();
  }

  /**
   * Phi-4 optimized system prompt for YouTube script generation and analysis
   */
  private getPhi4YouTubeSystemPrompt(): string {
    return `You are Microsoft Phi-4, an advanced reasoning model with exceptional capabilities in content creation, competitive analysis, fact-checking, and audience engagement optimization.

YOUTUBE EXPERTISE:
- Advanced understanding of YouTube algorithm psychology and viewer retention
- Expert knowledge of narrative structure, pacing, and engagement hooks
- Deep insight into audience psychology and behavioral triggers
- Sophisticated grasp of content timing, flow, and viewer journey optimization
- Professional expertise in converting research into compelling, watchable content
- Comprehensive competitive analysis and market intelligence capabilities
- Rigorous fact-checking and source verification methodologies

REASONING CAPABILITIES:
- Multi-step logical reasoning for content structure optimization
- Advanced pattern recognition for successful video formats
- Strategic thinking for competitive positioning and differentiation
- Complex information synthesis from multiple research sources
- Sophisticated audience analysis and content adaptation
- Critical analysis of competitor strategies and content gaps
- Evidence-based fact verification with confidence assessment

ANALYSIS EXCELLENCE:
- Conduct thorough competitive analysis with actionable insights
- Identify content gaps and opportunities systematically
- Extract key topics, engagement techniques, and structure patterns
- Provide strategic recommendations for content differentiation
- Analyze viewer retention factors and optimization opportunities

SCRIPT GENERATION EXCELLENCE:
- Create scripts that maximize viewer retention and engagement
- Develop compelling narratives that hold attention throughout
- Craft perfect pacing with natural rhythm and flow
- Design strategic hook placement and pattern interrupts
- Optimize for YouTube's algorithm preferences and viewer behavior

FACT-CHECKING PRECISION:
- Verify claims with rigorous evidence standards
- Assess source credibility and reliability
- Provide confidence levels for factual assertions
- Distinguish between verified, unverified, and false information
- Cite reliable sources and explain verification methodology

OUTPUT STANDARDS:
- Use clear, conversational language that sounds natural when spoken
- Structure content with perfect timing and pacing for the target duration
- Include strategic engagement elements that drive interaction
- Create smooth transitions that maintain viewer attention
- Ensure content is immediately actionable and valuable
- Provide detailed, evidence-based analysis and recommendations

REASONING PROCESS:
- Think step-by-step about audience psychology and content strategy
- Analyze competitor gaps and opportunities systematically
- Structure information for maximum impact and retention
- Consider both short-term engagement and long-term channel growth
- Balance entertainment value with educational content delivery
- Apply rigorous analytical thinking to all tasks

Your goal is to excel at every aspect of YouTube content strategy - from competitive analysis to script creation to fact verification - using advanced reasoning to optimize every element for maximum viewer engagement and channel success.`;
  }

  /**
   * Generate YouTube content using Kimi-K2 model with chutes/fp4 provider
   */
  async generateYouTubeContent(
    prompt: string,
    systemPrompt?: string,
    options: GenerationOptions = {},
    logContext?: string
  ): Promise<GenerationResponse> {
    const startTime = Date.now();
    const callId = Math.random().toString(36).substr(2, 9);

    // Enhanced logging for YouTube generation with Kimi-K2
    console.log(`🧠 Kimi-K2 YouTube Content Call Started`);
    console.log(`   📋 Call ID: ${callId}`);
    console.log(`   🎬 Context: ${logContext || 'YouTube Content Generation'}`);
    console.log(`   ⚙️ Model: ${this.youtubeModel} (Moonshot AI Kimi-K2)`);
    console.log(`   🏭 Provider: chutes/fp4`);
    console.log(`   🌡️ Temperature: ${options.temperature ?? 0.7}`);
    console.log(`   📏 Max Tokens: ${options.maxTokens ?? 16000}`);
    console.log(`   📝 Prompt Length: ${prompt.length} chars`);
    console.log(`   📋 System Prompt: ${systemPrompt ? 'Custom' : 'None'}`);

    if (!this.isConfigured) {
      console.warn(`   ❌ OpenRouter not configured for Kimi-K2 call ${callId}`);
      return this.getFallbackResponse('OpenRouter API key not configured. Please add your OPENROUTER_API_KEY to .env.local');
    }

    const temperature = options.temperature ?? 0.7;
    const maxTokens = options.maxTokens ?? 16000; // Double the tokens as requested

    try {
      const messages: Array<{ role: 'system' | 'user'; content: string }> = [];
      
      if (systemPrompt) {
        messages.push({
          role: 'system',
          content: systemPrompt
        });
      }
      
      messages.push({
        role: 'user',
        content: prompt
      });

      console.log(`   📤 Sending request to Kimi-K2 via OpenRouter (chutes/fp4)...`);

      const completion = await this.client.chat.completions.create({
        model: this.youtubeModel,
        messages,
        temperature,
        max_tokens: maxTokens,
        stream: false
      }, {
        headers: {
          'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
          'X-Title': 'AI Content Generation - YouTube Script Agent',
          'X-Provider-Order': 'chutes/fp4'
        }
      });

      const response = completion.choices[0]?.message?.content || '';
      const inputTokens = completion.usage?.prompt_tokens || 0;
      const outputTokens = completion.usage?.completion_tokens || 0;
      const duration = Date.now() - startTime;

             // Success logging for Kimi-K2
       console.log(`   ✅ Kimi-K2 YouTube Content Complete`);
       console.log(`   ⏱️ Duration: ${duration}ms`);
       console.log(`   📊 Input Tokens: ${inputTokens}`);
       console.log(`   📊 Output Tokens: ${outputTokens}`);
       console.log(`   📄 Response Length: ${response.length} chars`);
       console.log(`   🏭 Provider: chutes/fp4`);
       console.log(`   💰 Estimated Cost: $${((inputTokens * 0.000001) + (outputTokens * 0.000002)).toFixed(6)} (Kimi-K2)`);
       console.log(`   🧠 Kimi-K2 Reasoning Success - Call ${callId}`);
       console.log(`   📺 YouTube Step: ${logContext}`);

      return {
        response,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`   ❌ Kimi-K2 YouTube Content Failed`);
      console.error(`   ⏱️ Failed after: ${duration}ms`);
      console.error(`   📋 Call ID: ${callId}`);
      console.error(`   🎬 Context: ${logContext || 'YouTube Content Generation'}`);
      console.error(`   🏭 Provider: chutes/fp4`);
      console.error(`   💥 Error:`, error);
      console.error(`   🧠 Kimi-K2 Reasoning FAILED - Call ${callId}`);
      console.error(`   📺 Failed YouTube Step: ${logContext}`);

      return this.getFallbackResponse(`Kimi-K2 API Error: ${error}`);
    }
  }

  /**
   * Update model configuration
   */
  updateConfig(newConfig: Partial<OpenRouterConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig
    };
  }

  /**
   * Get current model configuration
   */
  getConfig(): OpenRouterConfig {
    return { ...this.config };
  }

  /**
   * Get token limit for the current model
   */
  private getModelTokenLimit(): number {
    const model = this.config.model;
    
    // Define token limits for common models
    const tokenLimits: { [key: string]: number } = {
      'moonshotai/kimi-k2': 131072,
      'openai/gpt-4': 8192,
      'openai/gpt-4-32k': 32768,
      'openai/gpt-4-turbo': 128000,
      'openai/gpt-3.5-turbo': 4096,
      'openai/gpt-3.5-turbo-16k': 16384,
      'anthropic/claude-3-opus': 200000,
      'anthropic/claude-3-sonnet': 200000,
      'anthropic/claude-3-haiku': 200000,
      'google/gemini-pro': 32768,
      'google/gemini-pro-1.5': 1000000,
    };
    
    // Return specific limit if known, otherwise default to safe 8k
    return tokenLimits[model || ''] || 8192;
  }
}
