// StructureComparisonIntegration.ts - Integration with Article Generation Workflow

import { 
  ArticleType, 
  NicheProfile, 
  ARTICLE_TYPES, 
  NICHES,
  ArticleStructureAnalyzer,
  CompetitiveAnalysisResult,
  StructureComparisonResult,
  compareAndSelectStructure
} from './ArticleNichePatterns';

import { nicheComponentConnector, NicheComponentMapping } from './NicheComponentConnector';

interface ContentGenerationRequest {
  title: string;
  topic: string;
  niche?: string;
  articleType?: string;
  competitiveAnalysis?: CompetitiveAnalysisResult;
  userPreferences?: {
    wordCount?: number;
    tone?: string;
    targetAudience?: string[];
  };
}

interface EnhancedContentStructure {
  originalRequest: ContentGenerationRequest;
  detectedNiche: NicheProfile;
  detectedArticleType: ArticleType;
  nicheComponentMapping: NicheComponentMapping;
  structureComparison?: StructureComparisonResult;
  finalStructure: {
    outline: string;
    estimatedWordCount: number;
    seoOptimization: any;
    contentGuidelines: string[];
    nicheSpecificComponents: {
      visualElements: string[];
      engagementTechniques: string[];
      vocabularyBank: any;
      contentPatterns: any;
    };
  };
  generationPrompt: string;
  performanceExpectations: {
    engagementRate: string;
    conversionPotential: string;
    viralityScore: string;
    retentionScore: string;
  };
}

export class ContentStructureOptimizer {
  private analyzer: ArticleStructureAnalyzer;

  constructor() {
    this.analyzer = new ArticleStructureAnalyzer(ARTICLE_TYPES, NICHES);
  }

  /**
   * Main integration point for the article generation workflow
   */
  async optimizeContentStructure(request: ContentGenerationRequest): Promise<EnhancedContentStructure> {
    // 1. Detect or validate niche
    const detectedNiche = this.detectNiche(request);
    
    // 2. Detect or validate article type
    const detectedArticleType = this.detectArticleType(request, detectedNiche);
    
    // 3. Get niche-component mapping for enhanced integration
    const nicheComponentMapping = nicheComponentConnector.getNicheComponentMapping(detectedNiche.id);
    if (!nicheComponentMapping) {
      throw new Error(`No component mapping found for niche: ${detectedNiche.id}`);
    }
    
    // 4. Get optimal content structure using the connector
    const optimalStructure = nicheComponentConnector.getOptimalContentStructure(
      detectedNiche.id, 
      detectedArticleType.id
    );
    
    // 5. Compare structures if competitive analysis is provided
    let structureComparison: StructureComparisonResult | undefined;
    let finalOutline: string;
    
    if (request.competitiveAnalysis) {
      const comparison = compareAndSelectStructure(
        request.competitiveAnalysis,
        detectedArticleType.id,
        detectedNiche.id
      );
      structureComparison = comparison.result;
      finalOutline = this.enhanceOutlineWithNicheComponents(comparison.outline, nicheComponentMapping);
    } else {
      // Generate outline based on niche-optimized structure
      finalOutline = this.generateNicheOptimizedOutline(
        detectedArticleType, 
        detectedNiche, 
        nicheComponentMapping,
        request
      );
    }
    
    // 6. Generate enhanced content guidelines with niche-specific insights
    const contentGuidelines = this.generateEnhancedContentGuidelines(
      detectedArticleType,
      detectedNiche,
      nicheComponentMapping,
      structureComparison
    );
    
    // 7. Create generation prompt with niche-specific components
    const generationPrompt = this.createEnhancedGenerationPrompt(
      request,
      detectedNiche,
      detectedArticleType,
      nicheComponentMapping,
      structureComparison,
      finalOutline
    );
    
    return {
      originalRequest: request,
      detectedNiche,
      detectedArticleType,
      nicheComponentMapping,
      structureComparison,
      finalStructure: {
        outline: finalOutline,
        estimatedWordCount: this.estimateWordCount(detectedArticleType, request.userPreferences?.wordCount),
        seoOptimization: this.generateEnhancedSEOGuidelines(detectedNiche, detectedArticleType, nicheComponentMapping),
        contentGuidelines,
        nicheSpecificComponents: {
          visualElements: nicheComponentMapping.preferredComponents.visualElements,
          engagementTechniques: nicheComponentMapping.preferredComponents.engagementTechniques,
          vocabularyBank: nicheComponentMapping.vocabularyBank,
          contentPatterns: nicheComponentMapping.contentPatterns
        }
      },
      generationPrompt,
      performanceExpectations: optimalStructure?.expectedPerformance || {
        engagementRate: '70%',
        conversionPotential: '4%',
        viralityScore: '75/100',
        retentionScore: '80/100'
      }
    };
  }

  /**
   * Enhanced niche detection with component mapping validation
   */
  private detectNiche(request: ContentGenerationRequest): NicheProfile {
    if (request.niche) {
      const niche = NICHES.find(n => n.id === request.niche);
      if (niche) return niche;
    }
    
    // Score each niche based on relevance and component compatibility
    const nicheScores = NICHES.map(niche => {
      let score = 0;
      const searchText = `${request.title} ${request.topic}`.toLowerCase();
      
      // Check primary keywords (higher weight)
      niche.keywords.primary.forEach(keyword => {
        if (searchText.includes(keyword.toLowerCase())) score += 5;
      });
      
      // Check secondary keywords
      niche.keywords.secondary.forEach(keyword => {
        if (searchText.includes(keyword.toLowerCase())) score += 3;
      });
      
      // Check content themes
      niche.contentThemes.forEach(theme => {
        if (searchText.includes(theme.toLowerCase())) score += 2;
      });
      
      // Boost score for niches with good component mappings
      const mapping = nicheComponentConnector.getNicheComponentMapping(niche.id);
      if (mapping && mapping.compatibleArticleTypes.length > 3) {
        score += 1;
      }
      
      return { niche, score };
    });
    
    // Sort by score and return the best match
    nicheScores.sort((a, b) => b.score - a.score);
    
    // If no clear match, default to a general niche with good component support
    if (nicheScores[0].score === 0) {
      console.warn('No niche detected, using default with component support');
      return NICHES.find(n => n.id === 'online-education') || NICHES[0];
    }
    
    return nicheScores[0].niche;
  }

  /**
   * Enhanced article type detection with niche compatibility scoring
   */
  private detectArticleType(request: ContentGenerationRequest, niche: NicheProfile): ArticleType {
    if (request.articleType) {
      const type = ARTICLE_TYPES.find(a => a.id === request.articleType);
      if (type) return type;
    }
    
    // Get compatible article types for this niche
    const compatibleTypes = nicheComponentConnector.getBestArticleTypesForNiche(niche.id);
    
    if (compatibleTypes.length === 0) {
      // Fallback to general detection
      return this.detectArticleTypeByPatterns(request);
    }
    
    // Score compatible types based on title patterns
    const typeScores = compatibleTypes.map(type => {
      let score = 10; // Base score for being compatible with niche
      const titleLower = request.title.toLowerCase();
      
      // Add compatibility score
      const compatibilityScore = nicheComponentConnector.getNicheCompatibilityScore(niche.id, type.id);
      score += compatibilityScore / 10;
      
      // Pattern matching for common article type indicators
      const patterns = this.getArticleTypePatterns();
      const typePatterns = patterns[type.id];
      
      if (typePatterns) {
        typePatterns.forEach(pattern => {
          if (pattern instanceof RegExp) {
            if (pattern.test(titleLower)) score += 5;
          } else if (titleLower.includes(pattern)) {
            score += 5;
          }
        });
      }
      
      return { type, score };
    });
    
    // Sort by score and return the best match
    typeScores.sort((a, b) => b.score - a.score);
    
    return typeScores[0]?.type || compatibleTypes[0] || this.detectArticleTypeByPatterns(request);
  }

  /**
   * Fallback article type detection by patterns
   */
  private detectArticleTypeByPatterns(request: ContentGenerationRequest): ArticleType {
    const patterns = this.getArticleTypePatterns();
    const titleLower = request.title.toLowerCase();
    
    for (const [typeId, keywords] of Object.entries(patterns)) {
      const hasPattern = keywords.some(keyword => {
        if (keyword instanceof RegExp) {
          return keyword.test(titleLower);
        }
        return titleLower.includes(keyword);
      });
      
      if (hasPattern) {
        const type = ARTICLE_TYPES.find(a => a.id === typeId);
        if (type) return type;
      }
    }
    
    // Default fallback
    return ARTICLE_TYPES.find(a => a.id === 'how-to-guide') || ARTICLE_TYPES[0];
  }

  /**
   * Get article type detection patterns
   */
  private getArticleTypePatterns(): { [key: string]: (string | RegExp)[] } {
    return {
      'how-to-guide': ['how to', 'guide to', 'steps to', 'tutorial'],
      'listicle': [/\d+\s+(ways|tips|reasons|things|best)/i, 'top', 'list of'],
      'comparison-post': ['vs', 'versus', 'compare', 'comparison', 'difference between'],
      'product-review': ['review', 'honest opinion', 'pros and cons', 'worth it'],
      'ultimate-guide': ['ultimate guide', 'complete guide', 'comprehensive', 'everything about'],
      'beginner-guide': ['beginner', 'getting started', 'introduction to', 'basics of'],
      'case-study': ['case study', 'success story', 'how we', 'results'],
      'tutorial': ['tutorial', 'learn', 'master', 'course'],
      'troubleshooting-guide': ['fix', 'solve', 'troubleshoot', 'problem', 'issue'],
      'buying-guide': ['best', 'buying guide', 'how to choose', 'which to buy']
    };
  }

  /**
   * Generate niche-optimized outline
   */
  private generateNicheOptimizedOutline(
    articleType: ArticleType, 
    niche: NicheProfile, 
    nicheMapping: NicheComponentMapping,
    request: ContentGenerationRequest
  ): string {
    let outline = `# ${request.title}\n\n`;
    outline += `**Article Type:** ${articleType.name}\n`;
    outline += `**Niche:** ${niche.name}\n`;
    outline += `**Target Word Count:** ${articleType.format.wordCountRange.join('-')}\n`;
    outline += `**Expected Engagement:** ${nicheMapping.performanceMetrics.avgEngagementRate.toFixed(1)}%\n\n`;
    
    outline += `## Introduction (${nicheMapping.contentPatterns.introPatterns[0]})\n`;
    articleType.format.introduction.forEach(point => {
      outline += `- ${point}\n`;
    });
    outline += `- Use niche vocabulary: ${nicheMapping.vocabularyBank.powerWords.slice(0, 3).join(', ')}\n\n`;
    
    outline += `## Main Content\n`;
    articleType.format.mainStructure.forEach((section, index) => {
      outline += `\n### ${index + 1}. ${section}\n`;
      outline += `- Include ${nicheMapping.preferredComponents.visualElements[index % nicheMapping.preferredComponents.visualElements.length]}\n`;
      outline += `- Use engagement technique: ${nicheMapping.preferredComponents.engagementTechniques[index % nicheMapping.preferredComponents.engagementTechniques.length]}\n`;
      outline += `- Address pain point: ${nicheMapping.audienceAlignment.painPoints[index % nicheMapping.audienceAlignment.painPoints.length]}\n`;
    });
    
    outline += `\n## Conclusion\n`;
    articleType.format.conclusion.forEach(point => {
      outline += `- ${point}\n`;
    });
    outline += `- End with: ${nicheMapping.contentPatterns.conclusionPatterns[0]}\n\n`;
    
    outline += `## SEO Elements\n`;
    outline += `- Primary Keywords: ${nicheMapping.vocabularyBank.nicheSpecificTerms.slice(0, 3).join(', ')}\n`;
    outline += `- Niche-Specific Terms: ${nicheMapping.vocabularyBank.technicalJargon.slice(0, 3).join(', ')}\n`;
    outline += `- Meta Description: ${articleType.seoOptimization.metaDescription}\n`;
    
    return outline;
  }

  /**
   * Enhance outline with niche components
   */
  private enhanceOutlineWithNicheComponents(outline: string, nicheMapping: NicheComponentMapping): string {
    let enhancedOutline = outline;
    
    // Add niche-specific enhancements
    enhancedOutline += `\n\n## Niche-Specific Enhancements\n`;
    enhancedOutline += `**Visual Elements:** ${nicheMapping.preferredComponents.visualElements.join(', ')}\n`;
    enhancedOutline += `**Engagement Techniques:** ${nicheMapping.preferredComponents.engagementTechniques.join(', ')}\n`;
    enhancedOutline += `**Vocabulary Focus:** ${nicheMapping.vocabularyBank.powerWords.join(', ')}\n`;
    enhancedOutline += `**Content Patterns:** Use ${nicheMapping.contentPatterns.introPatterns[0]} style\n`;
    
    return enhancedOutline;
  }

  /**
   * Generate enhanced content guidelines with niche-specific insights
   */
  private generateEnhancedContentGuidelines(
    articleType: ArticleType,
    niche: NicheProfile,
    nicheMapping: NicheComponentMapping,
    comparison?: StructureComparisonResult
  ): string[] {
    const guidelines: string[] = [];
    
    // Article type specific guidelines
    guidelines.push(`Write in ${articleType.format.toneOptions.join(' or ')} tone`);
    guidelines.push(`Structure content with ${articleType.structure.flow} flow`);
    
    // Niche-specific guidelines from component mapping
    guidelines.push(`Target ${nicheMapping.audienceAlignment.demographics.join(' and ')} demographics`);
    guidelines.push(`Address key pain points: ${nicheMapping.audienceAlignment.painPoints.slice(0, 2).join(', ')}`);
    guidelines.push(`Appeal to core desires: ${nicheMapping.audienceAlignment.desires.slice(0, 2).join(', ')}`);
    guidelines.push(`Use niche-specific vocabulary: ${nicheMapping.vocabularyBank.nicheSpecificTerms.slice(0, 3).join(', ')}`);
    guidelines.push(`Include visual elements: ${nicheMapping.preferredComponents.visualElements.slice(0, 2).join(', ')}`);
    guidelines.push(`Apply engagement techniques: ${nicheMapping.preferredComponents.engagementTechniques.slice(0, 2).join(', ')}`);
    
    // Performance-based guidelines
    const metrics = nicheMapping.performanceMetrics;
    guidelines.push(`Target ${metrics.avgEngagementRate.toFixed(1)}% engagement rate for this niche`);
    guidelines.push(`Optimize for ${metrics.viralityScore}/100 virality potential`);
    
    // Comparison-based guidelines
    if (comparison) {
      guidelines.push(`Recommendation: ${comparison.recommendation}`);
      comparison.reasoning.forEach(reason => guidelines.push(reason));
    }
    
    return guidelines;
  }

  /**
   * Create enhanced generation prompt with niche-specific components
   */
  private createEnhancedGenerationPrompt(
    request: ContentGenerationRequest,
    niche: NicheProfile,
    articleType: ArticleType,
    nicheMapping: NicheComponentMapping,
    comparison: StructureComparisonResult | undefined,
    outline: string
  ): string {
    let prompt = `Create a ${articleType.name} article for the ${niche.name} niche with comprehensive niche-component integration.\n\n`;
    
    prompt += `**Title:** ${request.title}\n`;
    prompt += `**Topic:** ${request.topic}\n\n`;
    
    prompt += `**Enhanced Target Audience Analysis:**\n`;
    prompt += `- Demographics: ${nicheMapping.audienceAlignment.demographics.join(', ')}\n`;
    prompt += `- Pain Points: ${nicheMapping.audienceAlignment.painPoints.join(', ')}\n`;
    prompt += `- Core Desires: ${nicheMapping.audienceAlignment.desires.join(', ')}\n`;
    prompt += `- Behavior Patterns: ${nicheMapping.audienceAlignment.behaviorPatterns.join(', ')}\n\n`;
    
    prompt += `**Niche-Specific Writing Guidelines:**\n`;
    prompt += `- Tone: ${articleType.format.toneOptions.join(' or ')}\n`;
    prompt += `- Paragraph Length: ${articleType.format.paragraphLength}\n`;
    prompt += `- Word Count Range: ${articleType.format.wordCountRange.join('-')}\n`;
    prompt += `- Content Patterns: Use "${nicheMapping.contentPatterns.introPatterns[0]}" style openings\n\n`;
    
    prompt += `**Enhanced Vocabulary Bank:**\n`;
    prompt += `- Niche-Specific Terms: ${nicheMapping.vocabularyBank.nicheSpecificTerms.join(', ')}\n`;
    prompt += `- Power Words: ${nicheMapping.vocabularyBank.powerWords.join(', ')}\n`;
    prompt += `- Technical Jargon: ${nicheMapping.vocabularyBank.technicalJargon.slice(0, 5).join(', ')}\n`;
    prompt += `- Common Phrases: ${nicheMapping.vocabularyBank.commonPhrases.join(', ')}\n\n`;
    
    prompt += `**Required Visual & Structural Components:**\n`;
    prompt += `- Visual Elements: ${nicheMapping.preferredComponents.visualElements.join(', ')}\n`;
    prompt += `- Engagement Techniques: ${nicheMapping.preferredComponents.engagementTechniques.join(', ')}\n`;
    prompt += `- Structural Components: ${nicheMapping.preferredComponents.structuralComponents.join(', ')}\n`;
    prompt += `- SEO Components: ${nicheMapping.preferredComponents.seoComponents.join(', ')}\n\n`;
    
    prompt += `**Performance Expectations:**\n`;
    prompt += `- Target Engagement Rate: ${nicheMapping.performanceMetrics.avgEngagementRate.toFixed(1)}%\n`;
    prompt += `- Conversion Potential: ${nicheMapping.performanceMetrics.conversionPotential.toFixed(1)}%\n`;
    prompt += `- Virality Score Target: ${nicheMapping.performanceMetrics.viralityScore}/100\n`;
    prompt += `- Retention Score Target: ${nicheMapping.performanceMetrics.retentionScore}/100\n\n`;
    
    if (comparison) {
      prompt += `**Structure Optimization:** ${comparison.recommendation}\n`;
      prompt += `Analysis: ${comparison.reasoning.join(' ')}\n\n`;
    }
    
    prompt += `**Content Pattern Templates:**\n`;
    prompt += `- Introduction Patterns: ${nicheMapping.contentPatterns.introPatterns.join(' OR ')}\n`;
    prompt += `- Body Patterns: ${nicheMapping.contentPatterns.bodyPatterns.join(' OR ')}\n`;
    prompt += `- Conclusion Patterns: ${nicheMapping.contentPatterns.conclusionPatterns.join(' OR ')}\n`;
    prompt += `- Transition Phrases: ${nicheMapping.contentPatterns.transitionPhrases.join(', ')}\n\n`;
    
    prompt += `**Article Outline:**\n${outline}\n\n`;
    
    prompt += `Write the article following this enhanced niche-component integration, ensuring maximum relevance to the ${niche.name} audience while leveraging proven ${articleType.name} structures for optimal performance.`;
    
    return prompt;
  }

  /**
   * Generate enhanced SEO guidelines with niche-specific components
   */
  private generateEnhancedSEOGuidelines(niche: NicheProfile, articleType: ArticleType, nicheMapping: NicheComponentMapping): any {
    return {
      primaryKeyword: nicheMapping.vocabularyBank.nicheSpecificTerms[0],
      secondaryKeywords: nicheMapping.vocabularyBank.nicheSpecificTerms.slice(1, 4),
      longtailKeywords: niche.keywords.longtail.slice(0, 2),
      nicheSpecificTerms: nicheMapping.vocabularyBank.technicalJargon.slice(0, 5),
      powerWords: nicheMapping.vocabularyBank.powerWords,
      keywordPlacement: articleType.seoOptimization.keywordPlacement,
      headingStructure: articleType.seoOptimization.headingStructure,
      internalLinking: articleType.seoOptimization.internalLinking,
      metaDescriptionTemplate: articleType.seoOptimization.metaDescription,
      targetKeywordDensity: '1-2%',
      semanticKeywords: this.generateEnhancedSemanticKeywords(niche, articleType, nicheMapping),
      seoComponents: nicheMapping.preferredComponents.seoComponents
    };
  }

  /**
   * Generate enhanced semantic keywords using niche mapping
   */
  private generateEnhancedSemanticKeywords(niche: NicheProfile, articleType: ArticleType, nicheMapping: NicheComponentMapping): string[] {
    const semanticKeywords: Set<string> = new Set();
    
    // Add from niche-specific terms
    nicheMapping.vocabularyBank.nicheSpecificTerms.forEach(term => {
      semanticKeywords.add(term);
      // Add variations
      const words = term.split(' ');
      words.forEach(word => {
        if (word.length > 3) semanticKeywords.add(word);
      });
    });
    
    // Add from technical jargon
    nicheMapping.vocabularyBank.technicalJargon.forEach(term => {
      if (term.length > 3) semanticKeywords.add(term);
    });
    
    // Add from content themes
    niche.contentThemes.forEach(theme => {
      const words = theme.toLowerCase().split(' ');
      words.forEach(word => {
        if (word.length > 3) semanticKeywords.add(word);
      });
    });
    
    // Add from article type best practices
    articleType.bestFor.forEach(useCase => {
      const words = useCase.toLowerCase().split(' ');
      words.forEach(word => {
        if (word.length > 3) semanticKeywords.add(word);
      });
    });
    
    return Array.from(semanticKeywords).slice(0, 15);
  }

  /**
   * Estimate word count based on article type and preferences
   */
  private estimateWordCount(articleType: ArticleType, preferredCount?: number): number {
    const [min, max] = articleType.format.wordCountRange;
    
    if (preferredCount) {
      if (preferredCount < min) return min;
      if (preferredCount > max) return max;
      return preferredCount;
    }
    
    return Math.floor((min + max) / 2);
  }

  /**
   * Get all available niches with component mappings
   */
  getAllNichesWithMappings(): Array<{ niche: NicheProfile; mapping: NicheComponentMapping }> {
    return NICHES.map(niche => ({
      niche,
      mapping: nicheComponentConnector.getNicheComponentMapping(niche.id)!
    })).filter(item => item.mapping);
  }

  /**
   * Get compatibility matrix for niche-article type combinations
   */
  getCompatibilityMatrix(): Array<{ nicheId: string; articleTypeId: string; score: number }> {
    const matrix: Array<{ nicheId: string; articleTypeId: string; score: number }> = [];
    
    NICHES.forEach(niche => {
      ARTICLE_TYPES.forEach(articleType => {
        const score = nicheComponentConnector.getNicheCompatibilityScore(niche.id, articleType.id);
        matrix.push({
          nicheId: niche.id,
          articleTypeId: articleType.id,
          score
        });
      });
    });
    
    return matrix.sort((a, b) => b.score - a.score);
  }
}

// Export enhanced optimizer
export const contentOptimizer = new ContentStructureOptimizer(); 