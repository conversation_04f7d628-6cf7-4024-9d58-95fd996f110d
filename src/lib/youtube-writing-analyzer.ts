/**
 * YouTube Writing Style Analyzer
 * Learns from extracted captions to understand authentic YouTube writing patterns
 */

import { KnowledgeBase } from './knowledge-base'

export interface YouTubeWritingPattern {
  pattern: string
  frequency: number
  examples: string[]
  category: 'hook' | 'transition' | 'engagement' | 'cta' | 'storytelling' | 'pacing'
  effectiveness: number // 1-10 based on view count/engagement
}

export interface YouTubeVoice {
  tone: 'casual' | 'professional' | 'energetic' | 'friendly' | 'authoritative'
  vocabulary: string[] // Common words and phrases
  sentenceStructure: 'simple' | 'complex' | 'varied'
  personalPronous: 'first' | 'second' | 'mixed' // I/me vs you vs we
  exclamationFreq: number // Excitement level
  questionFreq: number // How often they ask questions
}

export interface YouTubeStyleProfile {
  channel: string
  voice: YouTubeVoice
  commonHooks: string[]
  transitionPhrases: string[]
  engagementTactics: string[]
  callToActions: string[]
  storytellingElements: string[]
  pacingPatterns: string[]
  avgSentenceLength: number
  energyLevel: number // 1-10
  authenticityScore: number // How "YouTuber-like" they sound
}

export class YouTubeWritingAnalyzer {
  private static patterns: Map<string, YouTubeWritingPattern[]> = new Map()
  private static styleProfiles: Map<string, YouTubeStyleProfile> = new Map()
  
  /**
   * Analyze captions to extract YouTube writing patterns
   */
  static async analyzeCaption(
    caption: string, 
    videoTitle: string, 
    channelName: string, 
    viewCount: string,
    videoId: string
  ): Promise<YouTubeStyleProfile> {
    console.log(`🎬 Analyzing YouTube writing style for ${channelName}...`)
    
    // Clean and prepare caption text
    const cleanCaption = this.cleanCaptionText(caption)
    const sentences = this.splitIntoSentences(cleanCaption)
    
    // Analyze voice characteristics
    const voice = this.analyzeVoice(sentences)
    
    // Extract patterns
    const hooks = this.extractHooks(sentences)
    const transitions = this.extractTransitions(sentences)
    const engagement = this.extractEngagementTactics(sentences)
    const ctas = this.extractCallToActions(sentences)
    const storytelling = this.extractStorytellingElements(sentences)
    const pacing = this.analyzePacingPatterns(sentences)
    
    // Calculate metrics
    const avgSentenceLength = this.calculateAvgSentenceLength(sentences)
    const energyLevel = this.calculateEnergyLevel(sentences)
    const authenticityScore = this.calculateAuthenticityScore(voice, engagement, pacing)
    
    const styleProfile: YouTubeStyleProfile = {
      channel: channelName,
      voice,
      commonHooks: hooks,
      transitionPhrases: transitions,
      engagementTactics: engagement,
      callToActions: ctas,
      storytellingElements: storytelling,
      pacingPatterns: pacing,
      avgSentenceLength,
      energyLevel,
      authenticityScore
    }
    
    // Store in cache for learning
    this.styleProfiles.set(channelName, styleProfile)
    
    // Update global patterns
    this.updateGlobalPatterns(styleProfile, viewCount)
    
    console.log(`✅ Style analysis complete for ${channelName} (Authenticity: ${authenticityScore}/10)`)
    return styleProfile
  }
  
  /**
   * Get comprehensive YouTube writing guidelines learned from all channels
   */
  static getYouTubeWritingGuidelines(): {
    topHooks: string[]
    bestTransitions: string[]
    provenEngagement: string[]
    effectiveCTAs: string[]
    storytellingTips: string[]
    voiceGuidelines: string[]
    pacingAdvice: string[]
  } {
    const allProfiles = Array.from(this.styleProfiles.values())
    
    // Aggregate the most effective patterns
    const topHooks = this.aggregateTopPatterns(allProfiles, 'commonHooks', 10)
    const bestTransitions = this.aggregateTopPatterns(allProfiles, 'transitionPhrases', 8)
    const provenEngagement = this.aggregateTopPatterns(allProfiles, 'engagementTactics', 10)
    const effectiveCTAs = this.aggregateTopPatterns(allProfiles, 'callToActions', 6)
    const storytellingTips = this.aggregateTopPatterns(allProfiles, 'storytellingElements', 8)
    
    // Generate voice guidelines from most authentic channels
    const topChannels = allProfiles
      .filter(p => p.authenticityScore >= 7)
      .sort((a, b) => b.authenticityScore - a.authenticityScore)
      .slice(0, 5)
    
    const voiceGuidelines = this.generateVoiceGuidelines(topChannels)
    const pacingAdvice = this.generatePacingAdvice(topChannels)
    
    return {
      topHooks,
      bestTransitions,
      provenEngagement,
      effectiveCTAs,
      storytellingTips,
      voiceGuidelines,
      pacingAdvice
    }
  }
  
  /**
   * Generate a YouTuber-style script prompt based on learned patterns
   */
  static generateYouTuberPrompt(topic: string, style: string = 'energetic'): string {
    const guidelines = this.getYouTubeWritingGuidelines()
    const matchingProfiles = Array.from(this.styleProfiles.values())
      .filter(p => p.voice.tone === style || p.energyLevel >= 7)
      .slice(0, 3)
    
    return `You are a professional YouTube script writer who has studied thousands of successful YouTube videos. Write in an authentic YouTube style that feels natural and engaging.

YOUTUBE WRITING STYLE GUIDELINES:
${guidelines.voiceGuidelines.map(g => `• ${g}`).join('\n')}

PROVEN HOOK PATTERNS (use similar structure):
${guidelines.topHooks.slice(0, 5).map(h => `• "${h}"`).join('\n')}

NATURAL TRANSITIONS (use these styles):
${guidelines.bestTransitions.slice(0, 5).map(t => `• ${t}`).join('\n')}

ENGAGEMENT TECHNIQUES (incorporate naturally):
${guidelines.provenEngagement.slice(0, 5).map(e => `• ${e}`).join('\n')}

EFFECTIVE CALL-TO-ACTIONS (use similar language):
${guidelines.effectiveCTAs.slice(0, 3).map(c => `• ${c}`).join('\n')}

PACING GUIDELINES:
${guidelines.pacingAdvice.map(p => `• ${p}`).join('\n')}

AUTHENTIC YOUTUBER VOICE:
- Use "I", "you", and "we" naturally like real YouTubers
- Include excitement and energy in your writing
- Ask rhetorical questions to engage viewers
- Use casual language that feels conversational
- Include pattern interrupts and surprise elements
- Reference the viewer directly ("if you're watching this...")
- Use storytelling to make points memorable

Write the script as if you're a successful YouTuber who knows exactly how to connect with their audience. Make it feel authentic, engaging, and naturally YouTube-native.`
  }
  
  // PRIVATE ANALYSIS METHODS
  
  private static cleanCaptionText(caption: string): string {
    return caption
      .replace(/\[.*?\]/g, '') // Remove timestamp brackets
      .replace(/\(.*?\)/g, '') // Remove parenthetical notes
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
  }
  
  private static splitIntoSentences(text: string): string[] {
    return text
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 3)
  }
  
  private static analyzeVoice(sentences: string[]): YouTubeVoice {
    const text = sentences.join(' ').toLowerCase()
    
    // Count pronouns
    const firstPerson = (text.match(/\b(i|me|my|mine|myself)\b/g) || []).length
    const secondPerson = (text.match(/\b(you|your|yours|yourself)\b/g) || []).length
    const firstPersonPlural = (text.match(/\b(we|us|our|ours|ourselves)\b/g) || []).length
    
    const totalPronouns = firstPerson + secondPerson + firstPersonPlural
    let personalPronous: 'first' | 'second' | 'mixed' = 'mixed'
    
    if (totalPronouns > 0) {
      if (secondPerson / totalPronouns > 0.6) personalPronous = 'second'
      else if (firstPerson / totalPronouns > 0.4) personalPronous = 'first'
    }
    
    // Analyze tone
    const energyWords = ['amazing', 'incredible', 'awesome', 'fantastic', 'wow', 'crazy', 'insane', 'epic']
    const casualWords = ['like', 'so', 'basically', 'actually', 'literally', 'kinda', 'sorta', 'yeah']
    const professionalWords = ['however', 'therefore', 'furthermore', 'consequently', 'nevertheless']
    
    const energyCount = energyWords.reduce((sum, word) => sum + (text.match(new RegExp(`\\b${word}\\b`, 'g')) || []).length, 0)
    const casualCount = casualWords.reduce((sum, word) => sum + (text.match(new RegExp(`\\b${word}\\b`, 'g')) || []).length, 0)
    const professionalCount = professionalWords.reduce((sum, word) => sum + (text.match(new RegExp(`\\b${word}\\b`, 'g')) || []).length, 0)
    
    let tone: YouTubeVoice['tone'] = 'friendly'
    if (energyCount > casualCount && energyCount > professionalCount) tone = 'energetic'
    else if (professionalCount > casualCount) tone = 'professional'
    else if (casualCount > 0) tone = 'casual'
    
    return {
      tone,
      vocabulary: this.extractCommonVocabulary(text),
      sentenceStructure: this.analyzeSentenceStructure(sentences),
      personalPronous,
      exclamationFreq: (text.match(/!/g) || []).length / sentences.length,
      questionFreq: (text.match(/\?/g) || []).length / sentences.length
    }
  }
  
  private static extractHooks(sentences: string[]): string[] {
    const hooks: string[] = []
    
    // Look for opening patterns in first 3 sentences
    const openingSentences = sentences.slice(0, 3)
    
    const hookPatterns = [
      /^(what if|imagine if|have you ever|did you know)/i,
      /^(so|okay|alright|hey)/i,
      /(question|secret|mistake|reason|way)/i,
      /^(this is|here's|today)/i
    ]
    
    openingSentences.forEach(sentence => {
      if (hookPatterns.some(pattern => pattern.test(sentence))) {
        hooks.push(sentence.substring(0, 60) + '...')
      }
    })
    
    return hooks
  }
  
  private static extractTransitions(sentences: string[]): string[] {
    const transitions: string[] = []
    
    const transitionPatterns = [
      /^(now|next|so|but|however|meanwhile|speaking of)/i,
      /^(let's|here's|this is where)/i,
      /^(moving on|another|also)/i
    ]
    
    sentences.forEach(sentence => {
      if (transitionPatterns.some(pattern => pattern.test(sentence))) {
        transitions.push(sentence.substring(0, 40))
      }
    })
    
    return [...new Set(transitions)].slice(0, 10)
  }
  
  private static extractEngagementTactics(sentences: string[]): string[] {
    const tactics: string[] = []
    
    const engagementPatterns = [
      /(let me know|comment below|what do you think)/i,
      /(smash that|hit that|don't forget to)/i,
      /(if you|for those of you|anyone who)/i,
      /(make sure|be sure to|remember to)/i
    ]
    
    sentences.forEach(sentence => {
      if (engagementPatterns.some(pattern => pattern.test(sentence))) {
        tactics.push(sentence.substring(0, 50))
      }
    })
    
    return [...new Set(tactics)]
  }
  
  private static extractCallToActions(sentences: string[]): string[] {
    const ctas: string[] = []
    
    const ctaPatterns = [
      /(subscribe|like|comment|share|bell)/i,
      /(check out|watch|click)/i,
      /(follow|join|become)/i
    ]
    
    sentences.forEach(sentence => {
      if (ctaPatterns.some(pattern => pattern.test(sentence))) {
        ctas.push(sentence.substring(0, 60))
      }
    })
    
    return [...new Set(ctas)]
  }
  
  private static extractStorytellingElements(sentences: string[]): string[] {
    const elements: string[] = []
    
    const storyPatterns = [
      /(story|experience|happened|remember)/i,
      /(first time|last time|one day)/i,
      /(realized|discovered|learned)/i
    ]
    
    sentences.forEach(sentence => {
      if (storyPatterns.some(pattern => pattern.test(sentence))) {
        elements.push(sentence.substring(0, 50))
      }
    })
    
    return [...new Set(elements)]
  }
  
  private static analyzePacingPatterns(sentences: string[]): string[] {
    const patterns: string[] = []
    
    // Analyze sentence length variation
    const lengths = sentences.map(s => s.split(' ').length)
    const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length
    
    if (avgLength < 8) patterns.push('Quick, punchy sentences')
    else if (avgLength > 15) patterns.push('Longer, detailed explanations')
    else patterns.push('Mixed sentence lengths for good flow')
    
    // Check for pattern interrupts
    const shortSentences = lengths.filter(len => len <= 3).length
    if (shortSentences > lengths.length * 0.2) {
      patterns.push('Frequent pattern interrupts with short sentences')
    }
    
    return patterns
  }
  
  private static extractCommonVocabulary(text: string): string[] {
    const words = text.split(' ')
    const wordCount = new Map<string, number>()
    
    words.forEach(word => {
      const clean = word.replace(/[^\w]/g, '').toLowerCase()
      if (clean.length > 3) {
        wordCount.set(clean, (wordCount.get(clean) || 0) + 1)
      }
    })
    
    return Array.from(wordCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([word]) => word)
  }
  
  private static analyzeSentenceStructure(sentences: string[]): 'simple' | 'complex' | 'varied' {
    const complexSentences = sentences.filter(s => 
      s.includes(',') || s.includes(';') || s.includes(' because ') || s.includes(' although ')
    ).length
    
    const ratio = complexSentences / sentences.length
    
    if (ratio < 0.3) return 'simple'
    if (ratio > 0.7) return 'complex'
    return 'varied'
  }
  
  private static calculateAvgSentenceLength(sentences: string[]): number {
    const totalWords = sentences.reduce((sum, sentence) => 
      sum + sentence.split(' ').length, 0
    )
    return totalWords / sentences.length
  }
  
  private static calculateEnergyLevel(sentences: string[]): number {
    const text = sentences.join(' ').toLowerCase()
    
    const energyIndicators = [
      { pattern: /!/g, weight: 1 },
      { pattern: /\b(amazing|awesome|incredible|fantastic|wow|crazy|insane|epic)\b/g, weight: 2 },
      { pattern: /\b(love|excited|pumped|stoked)\b/g, weight: 1.5 },
      { pattern: /\ball caps\b/g, weight: 3 },
      { pattern: /\?\?+/g, weight: 1 }
    ]
    
    let energyScore = 0
    energyIndicators.forEach(({ pattern, weight }) => {
      const matches = text.match(pattern) || []
      energyScore += matches.length * weight
    })
    
    // Normalize to 1-10 scale
    return Math.min(10, Math.max(1, Math.round((energyScore / sentences.length) * 5 + 3)))
  }
  
  private static calculateAuthenticityScore(
    voice: YouTubeVoice, 
    engagement: string[], 
    pacing: string[]
  ): number {
    let score = 5 // Base score
    
    // Voice authenticity
    if (voice.personalPronous === 'second') score += 1.5 // Direct address is very YouTube
    if (voice.tone === 'energetic' || voice.tone === 'casual') score += 1
    if (voice.questionFreq > 0.1) score += 0.5 // Questions engage viewers
    if (voice.exclamationFreq > 0.05) score += 0.5 // Excitement
    
    // Engagement authenticity
    if (engagement.length > 2) score += 1
    if (engagement.some(e => e.includes('comment') || e.includes('like'))) score += 1
    
    // Pacing authenticity
    if (pacing.some(p => p.includes('pattern interrupts'))) score += 0.5
    
    return Math.min(10, Math.round(score * 10) / 10)
  }
  
  private static updateGlobalPatterns(profile: YouTubeStyleProfile, viewCount: string): void {
    const effectiveness = this.calculateEffectiveness(viewCount)
    
    // Update hook patterns
    profile.commonHooks.forEach(hook => {
      this.addPattern('hook', hook, effectiveness)
    })
    
    // Update other patterns
    profile.transitionPhrases.forEach(transition => {
      this.addPattern('transition', transition, effectiveness)
    })
    
    profile.engagementTactics.forEach(tactic => {
      this.addPattern('engagement', tactic, effectiveness)
    })
  }
  
  private static addPattern(category: string, pattern: string, effectiveness: number): void {
    if (!this.patterns.has(category)) {
      this.patterns.set(category, [])
    }
    
    const categoryPatterns = this.patterns.get(category)!
    const existing = categoryPatterns.find(p => p.pattern === pattern)
    
    if (existing) {
      existing.frequency++
      existing.effectiveness = (existing.effectiveness + effectiveness) / 2
    } else {
      categoryPatterns.push({
        pattern,
        frequency: 1,
        examples: [pattern],
        category: category as any,
        effectiveness
      })
    }
  }
  
  private static calculateEffectiveness(viewCount: string): number {
    const views = parseInt(viewCount.replace(/[^\d]/g, ''))
    if (views > 1000000) return 10
    if (views > 500000) return 8
    if (views > 100000) return 6
    if (views > 50000) return 4
    return 2
  }
  
  private static aggregateTopPatterns(
    profiles: YouTubeStyleProfile[], 
    field: keyof YouTubeStyleProfile, 
    limit: number
  ): string[] {
    const allPatterns = new Map<string, number>()
    
    profiles.forEach(profile => {
      const patterns = profile[field] as string[]
      patterns.forEach(pattern => {
        allPatterns.set(pattern, (allPatterns.get(pattern) || 0) + profile.authenticityScore)
      })
    })
    
    return Array.from(allPatterns.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([pattern]) => pattern)
  }
  
  private static generateVoiceGuidelines(topChannels: YouTubeStyleProfile[]): string[] {
    const guidelines: string[] = []
    
    const avgEnergy = topChannels.reduce((sum, p) => sum + p.energyLevel, 0) / topChannels.length
    const commonTone = topChannels[0]?.voice.tone || 'friendly'
    const avgSentenceLength = topChannels.reduce((sum, p) => sum + p.avgSentenceLength, 0) / topChannels.length
    
    guidelines.push(`Use a ${commonTone} tone with energy level around ${Math.round(avgEnergy)}/10`)
    guidelines.push(`Keep sentences around ${Math.round(avgSentenceLength)} words on average`)
    guidelines.push('Address viewers directly with "you" to create connection')
    guidelines.push('Include natural excitement and enthusiasm in your writing')
    guidelines.push('Use casual language that feels conversational, not scripted')
    
    return guidelines
  }
  
  private static generatePacingAdvice(topChannels: YouTubeStyleProfile[]): string[] {
    const advice: string[] = []
    
    advice.push('Vary sentence lengths - mix short punchy statements with longer explanations')
    advice.push('Use pattern interrupts every 30-60 seconds to maintain attention')
    advice.push('Include rhetorical questions to engage viewers mentally')
    advice.push('Build momentum with shorter sentences during exciting moments')
    advice.push('Use longer sentences for detailed explanations, shorter ones for impact')
    
    return advice
  }
} 