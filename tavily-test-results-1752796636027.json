{"timestamp": "2025-07-17T23:57:16.027Z", "summary": {"totalTested": 9, "validKeys": 6, "quotaExceeded": 3, "invalidKeys": 0, "networkErrors": 0}, "validKeys": [{"key": "...f9Vq", "full": "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"}, {"key": "...0Qna", "full": "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"}, {"key": "...FHXW", "full": "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"}, {"key": "...ncYf", "full": "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"}, {"key": "...JCgO", "full": "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"}, {"key": "...sSTM", "full": "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"}], "quotaExceededKeys": [{"key": "...s3a5", "full": "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"}, {"key": "...hQYR", "full": "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"}, {"key": "...gSay", "full": "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"}], "invalidKeys": [], "networkErrorKeys": [], "detailedResults": [{"key": "Key 3 (...s3a5)", "keyValue": "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", "status": 432, "success": false, "responseData": {"detail": {"error": "This request exceeds your plan's set usage limit. Please upgrade your plan <NAME_EMAIL>"}}, "timestamp": "2025-07-17T23:57:13.119Z", "category": "quota_exceeded"}, {"key": "Key 5 (...hQYR)", "keyValue": "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", "status": 432, "success": false, "responseData": {"detail": {"error": "This request exceeds your plan's set usage limit. Please upgrade your plan <NAME_EMAIL>"}}, "timestamp": "2025-07-17T23:57:13.121Z", "category": "quota_exceeded"}, {"key": "Key 1 (...gSay)", "keyValue": "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", "status": 432, "success": false, "responseData": {"detail": {"error": "This request exceeds your plan's set usage limit. Please upgrade your plan <NAME_EMAIL>"}}, "timestamp": "2025-07-17T23:57:13.122Z", "category": "quota_exceeded"}, {"key": "Key 2 (...f9Vq)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 0.81}, "timestamp": "2025-07-17T23:57:14.011Z", "category": "valid"}, {"key": "Key 7 (...0Qna)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 1.04}, "timestamp": "2025-07-17T23:57:14.219Z", "category": "valid"}, {"key": "Key 4 (...FHXW)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 1.05}, "timestamp": "2025-07-17T23:57:14.303Z", "category": "valid"}, {"key": "Key 8 (...ncYf)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 1.33}, "timestamp": "2025-07-17T23:57:14.515Z", "category": "valid"}, {"key": "Key 6 (...JCgO)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 1.4}, "timestamp": "2025-07-17T23:57:14.578Z", "category": "valid"}, {"key": "Key 9 (...sSTM)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 1.66}, "timestamp": "2025-07-17T23:57:14.855Z", "category": "valid"}], "recommendations": [{"type": "remove_keys", "priority": "high", "action": "Remove quota exceeded keys from TavilyApiKeyRotator", "keys": ["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", "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", "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"], "reason": "These keys have reached their quota limit and will continue to fail"}]}