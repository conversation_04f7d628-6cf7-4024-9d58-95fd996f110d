# AI Content Studio

## Overview

This project is a comprehensive AI-powered content creation platform built with Next.js 14 and TypeScript. It provides multiple specialized tools for creating high-quality content including blog posts, emails, YouTube scripts, and social media content.

## Key Features

- **Blog Post Generation**: AI-powered blog creation with SEO optimization
- **Email Generation**: Professional email content creation
- **YouTube Script Generation**: Video script creation with audience targeting
- **Social Media Content**: Multi-platform social media post generation
- **Video Alchemy**: AI video analysis and content extraction
- **Megatron Analysis**: YouTube video analysis and content ideation
- **User Authentication**: Google OAuth integration
- **Quota Management**: Usage tracking and subscription tiers
- **Content Management**: Save, edit, and organize generated content

## Technology Stack

- **Framework**: Next.js 14 with TypeScript
- **Authentication**: NextAuth.js with Google OAuth
- **Database**: Prisma ORM with SQLite/PostgreSQL
- **AI Services**: OpenRouter API, Gemini API
- **Search API**: Tavily for web research
- **UI Components**: Custom components with Tailwind CSS
- **Styling**: Tailwind CSS with Framer Motion animations

## Project Structure

```
src/
├── app/                          # Next.js app router
│   ├── api/                     # API routes
│   ├── blog-generator/          # Blog generation tool
│   ├── email-generator/         # Email generation tool
│   ├── youtube-script/          # YouTube script tool
│   ├── social-media-generator/  # Social media tool
│   ├── video-alchemy/           # Video analysis tool
│   ├── megatron/               # YouTube analysis tool
│   ├── dashboard/              # Main dashboard
│   └── content/                # Content management
├── lib/                        # Shared utilities
│   ├── agents/                 # AI agent implementations
│   ├── tools/                  # Shared tools (Tavily search, etc.)
│   ├── auth.ts                 # Authentication configuration
│   ├── quota.ts                # Quota management
│   └── gemini.ts              # Gemini API integration
├── components/                 # Reusable UI components
└── prisma/                    # Database schema and migrations
```

## Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Configuration
Copy `.env.local.example` to `.env.local` and add your API keys:

```bash
# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# AI Services
NEXT_PUBLIC_OPENROUTER_API_KEY=your_openrouter_api_key
GEMINI_API_KEY=your_gemini_api_key
NEXT_PUBLIC_TAVILY_API_KEY=your_tavily_api_key

# Database
DATABASE_URL="file:./dev.db"
```

### 3. Database Setup
```bash
npx prisma generate
npx prisma db push
```

### 4. Start Development Server
```bash
npm run dev
```

Navigate to `http://localhost:3000` to access the application.

## API Key Setup

**OpenRouter API Key:**
- Visit https://openrouter.ai/keys
- Create account and generate API key
- Add credit for model access

**Gemini API Key:**
- Visit https://makersuite.google.com/app/apikey
- Generate API key for Gemini models

**Tavily Search API Key:**
- Visit https://tavily.com/
- Sign up for free account (1000 searches/month)
- Generate API key from dashboard

**Google OAuth:**
- Visit https://console.developers.google.com/
- Create OAuth 2.0 credentials
- Configure authorized redirect URIs

## Usage Guide

### Dashboard Access
1. Navigate to the main dashboard at `/dashboard`
2. View available content generation tools
3. Monitor usage quotas and statistics

### Content Generation
1. Select desired content type from dashboard
2. Fill in topic, style, and targeting parameters
3. Generate content using AI models
4. Review, edit, and save generated content

### Content Management
- View generated content at `/content`
- Edit content with built-in editor
- Export content in various formats
- Track content performance metrics

## Quota System

The application implements a comprehensive quota system:

- **Free Tier**: Limited usage across all tools
- **Pro Tier**: Enhanced quotas for professional use
- **Enterprise**: Unlimited usage

Quota types:
- `blog_posts`: Blog post generation
- `emails`: Email content generation
- `social_media`: Social media posts
- `youtube_scripts`: YouTube script generation

## Development Notes

### Adding New Content Types
1. Create new API route in `src/app/api/generate/`
2. Add UI component in appropriate directory
3. Update quota system with new type
4. Add tool entry to dashboard configuration

### Customization
- Modify AI prompts in agent implementations
- Adjust UI styling in component files
- Configure quota limits in `src/lib/quota.ts`
- Update database schema in `prisma/schema.prisma`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review logs in browser console
3. Verify API key configuration
4. Test with minimal examples first

---

*AI Content Studio - Comprehensive Content Creation Platform*