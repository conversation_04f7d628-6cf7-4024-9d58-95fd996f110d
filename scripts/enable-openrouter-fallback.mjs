#!/usr/bin/env node

console.log('🔄 Enabling OpenRouter Fallback for Content Generation\n');

import { promises as fs } from 'fs';

async function enableOpenRouterFallback() {
  try {
    // Read the blog generator route file
    const routePath = 'src/app/api/blog-generator/generate/route.ts';
    let content = await fs.readFile(routePath, 'utf8');
    
    // Add fallback logic if Gemini fails
    const fallbackCode = `
    // Fallback to OpenRouter if Gemini fails
    if (error.message.includes('API key not valid') || error.message.includes('GEMINI_API_KEY')) {
      console.log('⚠️ Gemini API key not available, trying OpenRouter fallback...');
      
      try {
        const { OpenRouterService } = await import('@/lib/openrouter');
        const openRouter = new OpenRouterService();
        
        const fallbackResult = await openRouter.generateContent(
          \`Create a comprehensive blog post about "\${analysisData.topic || topic}".
          
Word count: \${analysisData.wordCount || 2000} words
Tone: \${analysisData.tone || 'professional'}

Research Data:
\${JSON.stringify(researchData, null, 2)}

Competitive Analysis:
\${JSON.stringify(competitiveData, null, 2)}

Create an engaging, well-structured blog post with proper headings, sections, and a compelling narrative.\`,
          'You are a professional content writer specializing in creating comprehensive, engaging blog posts.',
          { temperature: 0.7, maxTokens: 8000 },
          'Blog Generation Fallback'
        );
        
        if (fallbackResult.success && fallbackResult.content) {
          console.log('✅ OpenRouter fallback successful!');
          
          // Store the content
          await storeContent(userId, {
            title: analysisData.title || topic,
            content: fallbackResult.content,
            type: 'blog_post',
            wordCount: analysisData.wordCount || 2000,
            tone: analysisData.tone || 'professional'
          });
          
          return NextResponse.json({
            success: true,
            content: fallbackResult.content,
            usedFallback: true,
            message: 'Content generated using OpenRouter (Gemini API key needed for full functionality)'
          });
        }
      } catch (fallbackError) {
        console.error('❌ OpenRouter fallback also failed:', fallbackError);
      }
    }`;
    
    // Find the catch block and add fallback before the final error throw
    const catchBlockRegex = /(catch \(error\) \{[\s\S]*?)(throw new Error)/;
    const match = content.match(catchBlockRegex);
    
    if (match) {
      const beforeThrow = match[1];
      const throwStatement = match[2];
      
      // Add fallback logic before the throw
      const updatedCatchBlock = beforeThrow + fallbackCode + '\n    \n    ' + throwStatement;
      content = content.replace(catchBlockRegex, updatedCatchBlock);
      
      await fs.writeFile(routePath, content);
      console.log('✅ OpenRouter fallback enabled!');
      console.log('💡 Now you can generate content even without Gemini API key');
      console.log('⚠️ Note: Get Gemini API key for full functionality');
      
      return true;
    } else {
      console.log('❌ Could not find catch block to modify');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Failed to enable fallback:', error.message);
    return false;
  }
}

// Check if OpenRouter is configured
async function checkOpenRouterConfig() {
  try {
    const envContent = await fs.readFile('.env.local', 'utf8');
    const openrouterKey = envContent.match(/OPENROUTER_API_KEY=(.+)/)?.[1] || '';
    
    if (openrouterKey && !openrouterKey.includes('your_')) {
      console.log('✅ OpenRouter API key found - fallback ready');
      return true;
    } else {
      console.log('⚠️ No OpenRouter API key found');
      console.log('💡 Get one at: https://openrouter.ai/keys');
      return false;
    }
  } catch (error) {
    return false;
  }
}

const hasOpenRouter = await checkOpenRouterConfig();

if (hasOpenRouter) {
  const success = await enableOpenRouterFallback();
  if (success) {
    console.log('\n🎉 Fallback enabled! You can now:');
    console.log('1. Restart your dev server: npm run dev');
    console.log('2. Try content generation - it will use OpenRouter');
    console.log('3. Still get your Gemini key for optimal performance');
  }
} else {
  console.log('\n❌ OpenRouter API key needed for fallback');
  console.log('📋 Two options:');
  console.log('1. Get Gemini API key (recommended): https://aistudio.google.com/app/apikey');
  console.log('2. Get OpenRouter API key: https://openrouter.ai/keys');
} 