/**
 * Direct test of Enhanced Autonomous Supervisor 2025 fixes
 * Tests the supervisor directly without API authentication
 */

console.log('🔧 Direct Test of Enhanced Autonomous Supervisor 2025\n');

async function testSupervisorDirectly() {
  try {
    // Dynamic import the supervisor
    const { EnhancedIntelligentAutonomousSupervisor2025: EnhancedAutonomousSupervisor2025 } = await import('../src/lib/agents/autonomous/EnhancedIntelligentAutonomousSupervisor2025.js');
    
    console.log('✅ Successfully imported EnhancedAutonomousSupervisor2025');
    
    // Create supervisor instance
    const supervisor = new EnhancedAutonomousSupervisor2025({
      maxIterations: 3,
      maxRetries: 2,
      qualityThreshold: 70,
      verboseLogging: true
    });
    
    console.log('✅ Successfully created supervisor instance');
    
    // Test the supervisor with a simple goal
    const testGoal = "How to water plants effectively";
    
    console.log(`🎯 Testing with goal: "${testGoal}"`);
    console.log('⏳ Starting execution...\n');
    
    const startTime = Date.now();
    const result = await supervisor.executeAutonomous(testGoal);
    const executionTime = Date.now() - startTime;
    
    console.log('📊 Test Results:');
    console.log(`   - Execution Time: ${executionTime}ms`);
    console.log(`   - Success: ${result.success}`);
    console.log(`   - Quality Score: ${result.qualityScore || 0}`);
    
    if (result.insights) {
      console.log(`   - Total Decisions: ${result.insights.totalDecisions}`);
      console.log(`   - Successful Phases: ${result.insights.successfulPhases}`);
      console.log(`   - Error Count: ${result.insights.errorCount}`);
    }
    
    if (result.error) {
      console.log(`   - Error: ${result.error}`);
      
      // Check specifically for the push error
      if (result.error.includes("Cannot read properties of undefined (reading 'push')")) {
        console.log('❌ PUSH ERROR STILL EXISTS!');
        return false;
      }
    }
    
    console.log('✅ PUSH ERROR APPEARS TO BE RESOLVED!');
    return true;
    
  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
    
    // Check if the error is the push error we're trying to fix
    if (error.message.includes("Cannot read properties of undefined (reading 'push')")) {
      console.log('❌ PUSH ERROR DETECTED IN CATCH BLOCK!');
      return false;
    }
    
    console.log('🔧 Error may be unrelated to push issue');
    return false;
  }
}

// Run the test
testSupervisorDirectly()
  .then(success => {
    if (success) {
      console.log('\n🎉 Test completed successfully!');
      process.exit(0);
    } else {
      console.log('\n💥 Test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n❌ Test crashed:', error);
    process.exit(1);
  }); 