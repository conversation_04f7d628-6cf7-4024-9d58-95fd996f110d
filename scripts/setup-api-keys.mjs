#!/usr/bin/env node

/**
 * API Keys Setup Script
 * Helps configure the required API keys for the Invincible AI platform
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');
const envLocalPath = path.join(rootDir, '.env.local');

console.log('🔧 Invincible AI - API Keys Setup\n');

// Check current .env.local file
let currentEnv = '';
try {
  currentEnv = fs.readFileSync(envLocalPath, 'utf8');
  console.log('✅ Found existing .env.local file');
} catch (error) {
  console.log('❌ No .env.local file found');
  process.exit(1);
}

// Check API key status
function checkApiKey(keyName, currentValue) {
  const isConfigured = currentValue && 
                      currentValue !== `your_${keyName.toLowerCase()}_key_here` && 
                      currentValue.length > 10;
  
  return {
    name: keyName,
    configured: isConfigured,
    value: currentValue,
    status: isConfigured ? '✅ Configured' : '❌ Not configured'
  };
}

// Parse current environment variables
const envLines = currentEnv.split('\n');
const envVars = {};

envLines.forEach(line => {
  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length > 0) {
    envVars[key.trim()] = valueParts.join('=').trim();
  }
});

// Check all API keys
const apiKeys = [
  checkApiKey('OPENROUTER', envVars.OPENROUTER_API_KEY),
  checkApiKey('TAVILY', envVars.TAVILY_API_KEY),
  checkApiKey('GEMINI', envVars.GEMINI_API_KEY)
];

console.log('📊 Current API Keys Status:\n');
apiKeys.forEach(key => {
  console.log(`${key.status} ${key.name}_API_KEY`);
  if (!key.configured) {
    console.log(`   Current: ${key.value || 'Not set'}`);
  }
});

console.log('\n🎯 Priority Setup Order:\n');

if (!apiKeys[0].configured) {
  console.log('1. 🔴 CRITICAL: OpenRouter API Key');
  console.log('   • Required for Invincible V.1 AI agent');
  console.log('   • Get it from: https://openrouter.ai/');
  console.log('   • Sign up → API Keys → Create new key');
  console.log('   • Should start with: sk-or-v1-...\n');
}

if (!apiKeys[1].configured) {
  console.log('2. ✅ TAVILY: Already Has Fallback Keys');
  console.log('   • Built-in rotation system with 9+ fallback keys');
  console.log('   • Works automatically without manual setup');
  console.log('   • Optional: Add your own key for higher quotas');
  console.log('   • Get personal key from: https://tavily.com/\n');
} else {
  console.log('2. ✅ TAVILY: Personal Key + Fallback Keys');
  console.log('   • Your personal key will be used first');
  console.log('   • Automatic fallback to 9+ backup keys when needed');
  console.log('   • Excellent reliability and quota coverage\n');
}

if (!apiKeys[2].configured) {
  console.log('3. 🟢 OPTIONAL: Gemini API Key');
  console.log('   • Additional AI capabilities');
  console.log('   • Get it from: https://aistudio.google.com/');
  console.log('   • Create project → Get API key\n');
}

console.log('📝 How to add API keys:\n');
console.log('1. Open .env.local in your text editor');
console.log('2. Replace the placeholder values:');
console.log('   OPENROUTER_API_KEY=your_actual_openrouter_key_here');
console.log('   TAVILY_API_KEY=your_actual_tavily_key_here');
console.log('   GEMINI_API_KEY=your_actual_gemini_key_here');
console.log('3. Save the file');
console.log('4. Restart your development server: npm run dev\n');

const configuredCount = apiKeys.filter(k => k.configured).length;
const totalCount = apiKeys.length;

// Tavily effectively works even without manual configuration due to fallback keys
const effectivelyConfigured = configuredCount + (!apiKeys[1].configured ? 1 : 0);

if (configuredCount === 0) {
  console.log('🚨 No API keys configured - AI features will not work');
  console.log('🔧 However, Tavily research will work via fallback keys');
} else if (effectivelyConfigured >= 2) {
  console.log('🎉 Core functionality ready!');
  console.log('   ✅ Tavily Research: Automatic fallback keys active');
  if (apiKeys[2].configured) {
    console.log('   ✅ Gemini: Additional AI capabilities available');
  }
} else {
  console.log(`⚠️  ${configuredCount}/${totalCount} API keys configured manually`);
  console.log('   ✅ Tavily works automatically with built-in fallback keys');
}

console.log('\n💡 Need help? Check the documentation or create an issue on GitHub.\n'); 