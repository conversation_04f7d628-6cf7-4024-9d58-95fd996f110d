#!/usr/bin/env node

/**
 * Tavily API Key Update Script
 * Automatically updates search.ts to remove quota exceeded and invalid keys
 */

import fs from 'fs';
import path from 'path';
import { TavilyKeyTester } from './test-tavily-api-keys.mjs';

class TavilyKeyUpdater {
  constructor() {
    this.searchFilePath = path.join(process.cwd(), 'src', 'lib', 'search.ts');
    this.backupDir = path.join(process.cwd(), 'backups');
  }

  /**
   * Create backup directory if it doesn't exist
   */
  async ensureBackupDir() {
    try {
      await fs.promises.mkdir(this.backupDir, { recursive: true });
    } catch (error) {
      console.error(`❌ Failed to create backup directory: ${error.message}`);
    }
  }

  /**
   * Create backup of search.ts file
   */
  async createBackup() {
    await this.ensureBackupDir();
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.backupDir, `search.ts.backup.${timestamp}`);
    
    try {
      const originalContent = await fs.promises.readFile(this.searchFilePath, 'utf8');
      await fs.promises.writeFile(backupPath, originalContent);
      console.log(`📁 Backup created: ${backupPath}`);
      return backupPath;
    } catch (error) {
      console.error(`❌ Failed to create backup: ${error.message}`);
      throw error;
    }
  }

  /**
   * Update the search.ts file to remove bad keys
   */
  async updateSearchFile(keysToRemove) {
    if (keysToRemove.length === 0) {
      console.log('✅ No keys need to be removed - search.ts is up to date');
      return;
    }

    try {
      // Create backup first
      const backupPath = await this.createBackup();
      
      // Read current file content
      const fileContent = await fs.promises.readFile(this.searchFilePath, 'utf8');
      let updatedContent = fileContent;
      
      console.log(`🔧 Removing ${keysToRemove.length} keys from search.ts...`);
      
      // Remove each bad key
      keysToRemove.forEach((key, index) => {
        console.log(`  ${index + 1}. Removing key ending in ...${key.slice(-4)}`);
        
        // Create a regex pattern to match the key line (with or without comma)
        const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const patterns = [
          new RegExp(`\\s*'${escapedKey}',?\\s*\n`, 'g'), // Key with newline
          new RegExp(`\\s*'${escapedKey}',?\\s*`, 'g'),   // Key without newline
        ];
        
        for (const pattern of patterns) {
          const matches = updatedContent.match(pattern);
          if (matches) {
            updatedContent = updatedContent.replace(pattern, '');
            break;
          }
        }
      });
      
      // Clean up formatting issues
      updatedContent = updatedContent
        .replace(/,(\s*\])/g, '$1')           // Remove trailing commas before closing bracket
        .replace(/,(\s*),/g, ',')            // Remove double commas
        .replace(/\n\s*\n\s*\n/g, '\n\n')    // Remove multiple empty lines
        .replace(/(\[\s*),/g, '$1')          // Remove comma after opening bracket
        .trim();
      
      // Write updated content
      await fs.promises.writeFile(this.searchFilePath, updatedContent);
      
      console.log(`✅ Successfully updated search.ts`);
      console.log(`📁 Original backed up to: ${backupPath}`);
      
      return true;
      
    } catch (error) {
      console.error(`❌ Failed to update search.ts: ${error.message}`);
      throw error;
    }
  }

  /**
   * Validate the updated file by checking syntax
   */
  async validateUpdatedFile() {
    try {
      // Simple syntax check - try to read and parse the structure
      const content = await fs.promises.readFile(this.searchFilePath, 'utf8');
      
      // Check if the fallbackKeys array is still valid
      const fallbackKeysMatch = content.match(/fallbackKeys\s*=\s*\[([\s\S]*?)\]/);
      
      if (!fallbackKeysMatch) {
        throw new Error('Could not find fallbackKeys array in updated file');
      }
      
      const keysArrayContent = fallbackKeysMatch[1];
      const keyMatches = keysArrayContent.match(/'[^']+'/g);
      
      if (!keyMatches || keyMatches.length === 0) {
        console.log('⚠️  WARNING: No keys found in fallbackKeys array after update');
      } else {
        console.log(`✅ Validation passed: ${keyMatches.length} keys remain in fallbackKeys array`);
      }
      
      return true;
      
    } catch (error) {
      console.error(`❌ Validation failed: ${error.message}`);
      return false;
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting Tavily API Key Update Process');
  
  try {
    // Step 1: Test all keys
    console.log('\n📊 Step 1: Testing all API keys...');
    const tester = new TavilyKeyTester();
    await tester.testAllKeys();
    
    // Step 2: Generate report
    console.log('\n📋 Step 2: Generating test report...');
    tester.generateReport();
    
    // Step 3: Identify keys to remove
    const keysToRemove = [...tester.quotaExceededKeys, ...tester.invalidKeys];
    
    if (keysToRemove.length === 0) {
      console.log('\n✅ All keys are working - no updates needed!');
      return;
    }
    
    // Step 4: Confirm update with user
    console.log(`\n🔧 Step 3: Ready to remove ${keysToRemove.length} non-working keys:`);
    keysToRemove.forEach((key, index) => {
      const isQuotaExceeded = tester.quotaExceededKeys.includes(key);
      const reason = isQuotaExceeded ? 'QUOTA EXCEEDED' : 'INVALID KEY';
      console.log(`  ${index + 1}. ...${key.slice(-4)} (${reason})`);
    });
    
    // For automation, you might want to add a confirmation prompt here
    // For now, we'll proceed automatically
    
    // Step 5: Update the file
    console.log('\n🔄 Step 4: Updating search.ts...');
    const updater = new TavilyKeyUpdater();
    await updater.updateSearchFile(keysToRemove);
    
    // Step 6: Validate the update
    console.log('\n✅ Step 5: Validating updated file...');
    const isValid = await updater.validateUpdatedFile();
    
    if (isValid) {
      console.log('\n🎉 Update completed successfully!');
      console.log(`📊 Remaining valid keys: ${tester.validKeys.length}`);
      
      if (tester.validKeys.length < 3) {
        console.log('⚠️  WARNING: Consider adding new API keys for better redundancy');
      }
    } else {
      console.log('\n❌ Update validation failed - please check the file manually');
    }
    
    // Save detailed results
    await tester.saveResults();
    
  } catch (error) {
    console.error('❌ Update process failed:', error);
    process.exit(1);
  }
}

// Add confirmation option
const args = process.argv.slice(2);
const autoConfirm = args.includes('--auto') || args.includes('-y');

if (!autoConfirm) {
  console.log('🤖 Tavily API Key Updater');
  console.log('This script will:');
  console.log('  1. Test all Tavily API keys');
  console.log('  2. Identify quota exceeded and invalid keys');
  console.log('  3. Update search.ts to remove bad keys');
  console.log('  4. Create a backup of the original file');
  console.log('');
  console.log('To run with auto-confirmation: node scripts/update-tavily-keys.mjs --auto');
  console.log('');
}

// Run the script
console.log('🚀 Starting Tavily API Key Update Script...');

// Check if this is the main module being executed
const isMainModule = process.argv[1] && process.argv[1].endsWith('update-tavily-keys.mjs');

if (isMainModule) {
  if (autoConfirm) {
    main().catch((error) => {
      console.error('❌ Script execution failed:', error);
      process.exit(1);
    });
  } else {
    // Simple confirmation for interactive mode
    console.log('Press Ctrl+C to cancel, or any key to continue...');
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.on('data', () => {
      process.stdin.setRawMode(false);
      main().catch((error) => {
        console.error('❌ Script execution failed:', error);
        process.exit(1);
      });
    });
  }
} else {
  console.log('✅ Module loaded for import');
}

export { TavilyKeyUpdater }; 