#!/usr/bin/env node

console.log('🔧 Gemini API Key Setup Assistant\n');

import { promises as fs } from 'fs';

async function getCurrentConfig() {
  try {
    const envContent = await fs.readFile('.env.local', 'utf8');
    const geminiKey = envContent.match(/GEMINI_API_KEY=(.+)/)?.[1] || '';
    const googleKey = envContent.match(/GOOGLE_API_KEY=(.+)/)?.[1] || '';
    
    return {
      geminiKey: geminiKey && !geminiKey.includes('your_') ? geminiKey : null,
      googleKey: googleKey && !googleKey.includes('your_') ? googleKey : null,
      envContent
    };
  } catch (error) {
    console.error('❌ Could not read .env.local file');
    return null;
  }
}

async function updateApiKey(apiKey) {
  try {
    const config = await getCurrentConfig();
    if (!config) return false;
    
    let updatedContent = config.envContent;
    
    // Update GEMINI_API_KEY
    updatedContent = updatedContent.replace(
      /GEMINI_API_KEY=.+/,
      `GEMINI_API_KEY=${apiKey}`
    );
    
    // Also update GOOGLE_API_KEY as backup
    updatedContent = updatedContent.replace(
      /GOOGLE_API_KEY=.+/,
      `GOOGLE_API_KEY=${apiKey}`
    );
    
    await fs.writeFile('.env.local', updatedContent);
    return true;
  } catch (error) {
    console.error('❌ Failed to update .env.local:', error.message);
    return false;
  }
}

async function testApiKey(apiKey) {
  try {
    console.log('🧪 Testing API key...');
    
    // Set environment variable for this test
    process.env.GEMINI_API_KEY = apiKey;
    
    const { GoogleGenerativeAI } = await import('@google/generative-ai');
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash-lite-preview-06-17' });
    
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: 'Say "Hello! API key is working correctly."' }] }],
      generationConfig: {
        temperature: 0.1,
        maxOutputTokens: 50
      }
    });
    
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ API key test successful!');
    console.log(`📝 Test response: ${text.trim()}`);
    return true;
  } catch (error) {
    console.error('❌ API key test failed:', error.message);
    return false;
  }
}

// Main execution
const config = await getCurrentConfig();

if (!config) {
  console.log('❌ Could not read configuration. Please ensure .env.local exists.');
  process.exit(1);
}

if (config.geminiKey || config.googleKey) {
  console.log('✅ API key found! Testing...');
  const keyToTest = config.geminiKey || config.googleKey;
  const isWorking = await testApiKey(keyToTest);
  
  if (isWorking) {
    console.log('\n🎉 Your Gemini API key is working correctly!');
    console.log('💡 Content generation should work now.');
    console.log('\n🚀 Next steps:');
    console.log('1. Restart your dev server: npm run dev');
    console.log('2. Try generating content in your app');
  } else {
    console.log('\n❌ API key is set but not working. Please check if it\'s valid.');
  }
} else {
  console.log('🚨 No valid Gemini API key found.\n');
  
  console.log('📋 Step-by-Step Setup:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  console.log('\n1️⃣ Get Your FREE API Key:');
  console.log('   🌐 Go to: https://aistudio.google.com/app/apikey');
  console.log('   📱 Sign in with your Google account');
  console.log('   🔑 Click "Create API Key"');
  console.log('   📋 Copy the generated key (starts with AIza...)');
  
  console.log('\n2️⃣ Add Your API Key:');
  console.log('   Run this command (replace YOUR_KEY with your actual key):');
  console.log('   📝 node scripts/setup-gemini-api.mjs YOUR_KEY');
  
  console.log('\n3️⃣ Or Manual Setup:');
  console.log('   📝 Edit .env.local and replace:');
  console.log('      GEMINI_API_KEY=your_gemini_api_key_here');
  console.log('   📝 With:');
  console.log('      GEMINI_API_KEY=AIza... (your actual key)');
  
  console.log('\n💡 Need Help?');
  console.log('   • Google AI Studio: https://aistudio.google.com/');
  console.log('   • Documentation: https://ai.google.dev/docs');
}

// Handle command line argument for API key
const apiKeyArg = process.argv[2];
if (apiKeyArg && apiKeyArg.startsWith('AIza')) {
  console.log('\n🔧 Setting up API key...');
  
  const isValidFormat = apiKeyArg.startsWith('AIza') && apiKeyArg.length > 20;
  if (!isValidFormat) {
    console.log('❌ Invalid API key format. Should start with "AIza" and be longer than 20 characters.');
    process.exit(1);
  }
  
  console.log('📝 Updating .env.local...');
  const success = await updateApiKey(apiKeyArg);
  
  if (success) {
    console.log('✅ API key updated in .env.local');
    
    console.log('🧪 Testing API key...');
    const isWorking = await testApiKey(apiKeyArg);
    
    if (isWorking) {
      console.log('\n🎉 Setup complete! Your Gemini API key is working.');
      console.log('\n🚀 Next steps:');
      console.log('1. Restart your dev server: npm run dev');
      console.log('2. Try generating content in your app');
      console.log('3. Content generation should now work perfectly!');
    } else {
      console.log('\n❌ API key updated but test failed. Please verify the key is correct.');
    }
  } else {
    console.log('❌ Failed to update .env.local file');
  }
} 