#!/usr/bin/env node

/**
 * Demo: Enhanced Invincible Cost Calculation
 * Shows the actual costs with Gemini 2.5 Flash-Lite Preview pricing
 */

// Cost calculation function based on the updated GeminiService
function calculateEnhancedInvincibleCost(options) {
  // Estimate input tokens (research data + prompts + context)
  const basePromptTokens = 2000; // Enhanced prompts are substantial
  const researchTokens = options.researchSources * 800; // ~800 tokens per source
  const competitorTokens = 5 * 1200; // 5 competitors at ~1200 tokens each
  const estimatedInputTokens = basePromptTokens + researchTokens + competitorTokens;

  // Estimate output tokens based on word count (1 token ≈ 0.75 words)
  const estimatedOutputTokens = Math.ceil(options.wordCount / 0.75);

  // Estimate thinking tokens (dynamic thinking uses 10-30% of output tokens)
  const estimatedThinkingTokens = options.withThinking ? Math.ceil(estimatedOutputTokens * 0.2) : 0;

  // Calculate costs (Gemini 2.5 Flash-Lite Preview 2025 pricing)
  const inputCost = estimatedInputTokens * 0.0000001; // $0.10 per million
  const outputCost = estimatedOutputTokens * 0.0000004; // $0.40 per million
  const thinkingCost = estimatedThinkingTokens * 0.0000004; // Included in output rate

  const totalCost = inputCost + outputCost + thinkingCost;

  return {
    estimatedInputTokens,
    estimatedOutputTokens,
    estimatedThinkingTokens,
    totalCost,
    breakdown: {
      inputCost,
      outputCost,
      thinkingCost
    }
  };
}

console.log('💰 Enhanced Invincible Cost Calculator');
console.log('🔧 Using Gemini 2.5 Flash-Lite Preview (2025) Pricing');
console.log('');

// Pricing reference
console.log('📋 Official Gemini 2.5 Flash-Lite Preview Pricing:');
console.log('   Input (text/image/video): $0.10 per 1M tokens');
console.log('   Output (including thinking): $0.40 per 1M tokens');
console.log('   Context caching: $0.025 per 1M tokens');
console.log('');

// Test different article scenarios
const scenarios = [
  {
    name: '🚀 Standard Blog Post',
    wordCount: 2000,
    researchSources: 7,
    withThinking: true
  },
  {
    name: '📚 Comprehensive Guide',
    wordCount: 5000,
    researchSources: 12,
    withThinking: true
  },
  {
    name: '💼 Enterprise Article',
    wordCount: 3500,
    researchSources: 15,
    withThinking: true
  },
  {
    name: '⚡ Quick Article (No Thinking)',
    wordCount: 1500,
    researchSources: 5,
    withThinking: false
  }
];

scenarios.forEach(scenario => {
  console.log(`${scenario.name}:`);
  console.log(`   Word Count: ${scenario.wordCount.toLocaleString()} words`);
  console.log(`   Research Sources: ${scenario.researchSources}`);
  console.log(`   Dynamic Thinking: ${scenario.withThinking ? 'Enabled (-1 budget)' : 'Disabled'}`);
  
  const cost = calculateEnhancedInvincibleCost(scenario);
  
  console.log(`   Estimated Tokens:`);
  console.log(`     Input: ${cost.estimatedInputTokens.toLocaleString()}`);
  console.log(`     Output: ${cost.estimatedOutputTokens.toLocaleString()}`);
  if (cost.estimatedThinkingTokens > 0) {
    console.log(`     Thinking: ${cost.estimatedThinkingTokens.toLocaleString()}`);
  }
  
  console.log(`   Cost Breakdown:`);
  console.log(`     Input: $${cost.breakdown.inputCost.toFixed(4)}`);
  console.log(`     Output: $${cost.breakdown.outputCost.toFixed(4)}`);
  if (cost.breakdown.thinkingCost > 0) {
    console.log(`     Thinking: $${cost.breakdown.thinkingCost.toFixed(4)}`);
  }
  console.log(`     TOTAL: $${cost.totalCost.toFixed(4)}`);
  console.log('');
});

// Show cost per article comparison
console.log('💡 Cost Efficiency Analysis:');
console.log('');

const costPer1000Words = scenarios.slice(0, 3).map(scenario => {
  const cost = calculateEnhancedInvincibleCost(scenario);
  return {
    name: scenario.name,
    costPer1000: (cost.totalCost / scenario.wordCount) * 1000
  };
});

costPer1000Words.forEach(item => {
  console.log(`${item.name}: $${item.costPer1000.toFixed(4)} per 1,000 words`);
});

console.log('');
console.log('🎯 Key Benefits with Dynamic Thinking (-1 budget):');
console.log('   ✅ Optimal reasoning automatically applied');
console.log('   ✅ Cost-efficient - only uses thinking when needed');
console.log('   ✅ Superior content quality with competitive analysis');
console.log('   ✅ Fact-checking and statistical validation included');
console.log('   ✅ 1M token context window utilization');
console.log('');

// Calculate articles per dollar
const standardCost = calculateEnhancedInvincibleCost(scenarios[0]);
const articlesPerDollar = Math.floor(1 / standardCost.totalCost);

console.log(`💰 Value Proposition:`);
console.log(`   Standard 2,000-word article: $${standardCost.totalCost.toFixed(4)}`);
console.log(`   Articles per $1: ~${articlesPerDollar} articles`);
console.log(`   Cost per article: Ultra-affordable with premium quality`);
console.log('');

console.log('🔥 Enhanced Invincible with Dynamic Thinking = Maximum ROI!'); 