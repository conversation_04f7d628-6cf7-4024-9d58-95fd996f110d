#!/usr/bin/env node

console.log('🧪 JSON Parsing Fix Verification\n');

// Test JSON parsing logic
function testJsonCleaning() {
  console.log('🔧 Testing JSON cleaning logic...');
  
  // Test cases
  const testCases = [
    '```json\n{"test": "value"}\n```',
    '```\n{"test": "value"}\n```',
    'Some text before\n{"test": "value"}\nSome text after',
    '{\n  "test": "value",\n  "array": ["item1", "item2"]\n}',
    ''  // Empty response case
  ];
  
  let passCount = 0;
  
  testCases.forEach((testCase, index) => {
    try {
      console.log(`\n📝 Test ${index + 1}:`);
      console.log(`   Input: "${testCase.substring(0, 50)}${testCase.length > 50 ? '...' : ''}"`);
      
      // Apply the same cleaning logic as the fixed code
      let cleanResponse = testCase.trim();
      
      // Handle empty response
      if (!cleanResponse || cleanResponse.length === 0) {
        console.log('   ⚠️ Empty response detected (expected for test 5)');
        if (index === 4) passCount++; // This is expected for the empty test
        return;
      }
      
      // Remove code block markers
      cleanResponse = cleanResponse.replace(/^```json\s*/g, '').replace(/^```\s*/g, '').replace(/\s*```$/g, '');
      cleanResponse = cleanResponse.trim();
      
      // Extract JSON - find first brace
      const firstBrace = cleanResponse.indexOf('{');
      if (firstBrace > 0) {
        cleanResponse = cleanResponse.substring(firstBrace);
      }
      
      // Find last complete closing brace
      let braceCount = 0;
      let lastValidIndex = -1;
      
      for (let i = 0; i < cleanResponse.length; i++) {
        if (cleanResponse[i] === '{') {
          braceCount++;
        } else if (cleanResponse[i] === '}') {
          braceCount--;
          if (braceCount === 0) {
            lastValidIndex = i;
          }
        }
      }
      
      if (lastValidIndex > -1) {
        cleanResponse = cleanResponse.substring(0, lastValidIndex + 1);
      }
      
      console.log(`   Cleaned: "${cleanResponse}"`);
      
      // Test JSON parsing
      const parsed = JSON.parse(cleanResponse);
      console.log('   ✅ JSON parsing successful!');
      console.log(`   📋 Result: ${JSON.stringify(parsed)}`);
      passCount++;
      
    } catch (error) {
      console.log(`   ❌ Test failed: ${error.message}`);
    }
  });
  
  console.log(`\n📊 JSON Cleaning Tests: ${passCount}/4 passed (excluding empty test)`);
  return passCount >= 3; // Should pass at least 3 out of 4 real tests
}

// Check environment setup
function checkEnvironment() {
  console.log('🔍 Checking environment setup...');
  
  const hasGeminiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY);
  const hasValidKey = (process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || '').startsWith('AIza');
  
  console.log(`   Gemini API Key: ${hasGeminiKey ? '✅ Present' : '❌ Missing'}`);
  console.log(`   Key Format: ${hasValidKey ? '✅ Valid' : '❌ Invalid'}`);
  
  return hasGeminiKey && hasValidKey;
}

// Run tests
const jsonTest = testJsonCleaning();
const envTest = checkEnvironment();

console.log('\n🎯 Summary:');
console.log(`   JSON Parsing Logic: ${jsonTest ? '✅ WORKING' : '❌ BROKEN'}`);
console.log(`   Environment Setup: ${envTest ? '✅ READY' : '❌ NEEDS CONFIG'}`);

if (jsonTest && envTest) {
  console.log('\n🎉 All systems ready!');
  console.log('💡 The JSON parsing fix should resolve the competitive analysis issues.');
  console.log('🚀 Try running the blog generator now - both analysis and generation should work!');
} else {
  console.log('\n⚠️ Issues detected:');
  if (!jsonTest) console.log('   - JSON parsing logic needs review');
  if (!envTest) console.log('   - Environment configuration incomplete');
} 