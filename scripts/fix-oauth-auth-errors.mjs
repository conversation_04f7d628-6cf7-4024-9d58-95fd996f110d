#!/usr/bin/env node

import { config } from 'dotenv'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Load environment variables
config({ path: join(__dirname, '..', '.env.local') })

console.log('🔧 OAuth Authentication Error Fix\n')

// Check current configuration
console.log('📋 Current Configuration:')
console.log(`   GOOGLE_CLIENT_ID: ${process.env.GOOGLE_CLIENT_ID ? '✅ Set' : '❌ Missing'}`)
console.log(`   GOOGLE_CLIENT_SECRET: ${process.env.GOOGLE_CLIENT_SECRET ? '✅ Set' : '❌ Missing'}`)
console.log(`   NEXTAUTH_URL: ${process.env.NEXTAUTH_URL || 'http://localhost:3000'}`)
console.log(`   NEXTAUTH_SECRET: ${process.env.NEXTAUTH_SECRET ? '✅ Set' : '❌ Missing'}\n`)

// Provide solutions
console.log('🚨 Gmail OAuth Error: "Access blocked: N8N has not completed the Google verification process"\n')

console.log('🎯 SOLUTION OPTIONS:\n')

console.log('1️⃣  QUICK FIX - Add Test Users (Recommended for Development):')
console.log('   • Go to Google Cloud Console → APIs & Services → OAuth consent screen')
console.log('   • Scroll to "Test users" section')
console.log('   • Click "ADD USERS" and add your email address')
console.log('   • Save changes')
console.log('   • This allows you to use the app immediately\n')

console.log('2️⃣  TEMPORARY FIX - Use Basic Scopes (Already Applied):')
console.log('   • Modified auth.ts to use only basic scopes')
console.log('   • Gmail features will be limited but authentication will work')
console.log('   • No Google verification required\n')

console.log('3️⃣  PRODUCTION SOLUTION - Complete Google App Verification:')
console.log('   • Submit your app for Google verification')
console.log('   • Required for production Gmail API access')
console.log('   • Process takes 1-2 weeks')
console.log('   • Documentation: https://developers.google.com/workspace/guides/configure-oauth-consent\n')

console.log('🔧 IMMEDIATE STEPS TO FIX:')
console.log('1. Add yourself as a test user in Google Cloud Console')
console.log('2. Clear your browser cookies/cache')
console.log('3. Try signing in again')
console.log('4. If still issues, restart your development server\n')

console.log('📝 Google Cloud Console URLs:')
console.log('   • OAuth Consent Screen: https://console.cloud.google.com/apis/credentials/consent')
console.log('   • Credentials: https://console.cloud.google.com/apis/credentials')
console.log('   • API Library: https://console.cloud.google.com/apis/library\n')

console.log('🧪 Test Authentication:')
console.log(`   Visit: ${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/login`)
console.log('   Click "Continue with Google" to test\n')

console.log('✅ OAuth scopes have been temporarily reduced to basic scopes only.')
console.log('   This should resolve the immediate authentication error.')
console.log('   To restore Gmail functionality, you\'ll need to complete Option 1 or 3 above.') 