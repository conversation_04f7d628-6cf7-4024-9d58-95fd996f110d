#!/usr/bin/env node

/**
 * Test Enhanced Intelligent Autonomous Supervisor 2025
 * 
 * Demonstrates the revolutionary research pipeline:
 * 1. <PERSON> Tavily search for exact topic
 * 2. Parallel web scraping of 10 URLs
 * 3. Three-stage analysis (topic understanding, data extraction, query generation)
 * 4. Intelligent planning based on analysis
 * 5. Traditional workflow execution with intelligence
 */

console.log('🧠 Testing Enhanced Intelligent Autonomous Supervisor 2025');
console.log('=' .repeat(70));

const testCases = [
  {
    name: "Alternatives/Comparison Test",
    goal: "top 5 gemini cli alternatives",
    description: "Testing with alternatives/comparison article type - the exact example from user"
  },
  {
    name: "Guide/Tutorial Test", 
    goal: "how to build a sustainable garden for beginners",
    description: "Testing with guide/tutorial article type"
  },
  {
    name: "Listicle Test",
    goal: "10 best AI writing tools for content creators 2025",
    description: "Testing with listicle article type"
  },
  {
    name: "Technical Tutorial Test",
    goal: "complete guide to setting up Docker for development",
    description: "Testing with technical tutorial content"
  }
];

async function testEnhancedIntelligentSupervisor(testCase) {
  console.log(`\n🚀 Running Test: ${testCase.name}`);
  console.log(`📝 Goal: "${testCase.goal}"`);
  console.log(`📋 Description: ${testCase.description}`);
  console.log('-'.repeat(50));

  const startTime = Date.now();

  try {
    const response = await fetch('http://localhost:3000/api/autonomous-intelligence', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        goal: testCase.goal,
        config: {
          maxRetries: 2,
          qualityThreshold: 85,
          timeoutMinutes: 15,
          parallelScrapingCount: 10
        }
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`HTTP ${response.status}: ${errorData.error || 'Unknown error'}`);
    }

    const result = await response.json();
    const executionTime = Date.now() - startTime;

    if (result.success) {
      console.log('✅ Enhanced Intelligence Pipeline Test PASSED');
      console.log(`⏱️  Total Execution Time: ${Math.round(executionTime / 1000)}s`);
      console.log(`📊 Quality Score: ${result.data.qualityScore}%`);
      
      if (result.data.insights) {
        console.log('\n🔍 Intelligence Insights:');
        console.log(`  📄 Pages Scraped: ${result.data.insights.scrapedPagesCount}`);
        console.log(`  🧠 Analysis Completed: ${result.data.insights.analysisCompleted ? 'Yes' : 'No'}`);
        console.log(`  🎯 Intelligent Queries Generated: ${result.data.insights.intelligentQueriesGenerated}`);
        console.log(`  ⭐ Final Quality: ${result.data.insights.finalQuality}%`);
      }

      if (result.data.pipeline) {
        console.log('\n🏗️ Pipeline Executed:');
        result.data.pipeline.forEach((phase, index) => {
          console.log(`  ${index + 1}. ${phase}`);
        });
      }

      if (result.data.result && result.data.result.content) {
        const contentLength = result.data.result.content.length;
        const wordCount = result.data.result.content.split(/\s+/).length;
        console.log(`\n📝 Generated Content: ${wordCount} words (${contentLength} characters)`);
        console.log(`📖 Content Preview: ${result.data.result.content.substring(0, 200)}...`);
      }

      return {
        success: true,
        testCase: testCase.name,
        executionTime,
        qualityScore: result.data.qualityScore,
        insights: result.data.insights
      };

    } else {
      console.log('❌ Enhanced Intelligence Pipeline Test FAILED');
      console.log(`Error: ${result.error || 'Unknown error'}`);
      return {
        success: false,
        testCase: testCase.name,
        error: result.error
      };
    }

  } catch (error) {
    console.log('❌ Enhanced Intelligence Pipeline Test FAILED');
    console.log(`Error: ${error.message}`);
    return {
      success: false,
      testCase: testCase.name,
      error: error.message
    };
  }
}

async function runAllTests() {
  console.log('🔬 Starting Enhanced Intelligence Pipeline Tests');
  console.log(`📅 Test Date: ${new Date().toISOString()}`);
  console.log(`🎯 Testing ${testCases.length} different article types\n`);

  const results = [];
  let passedTests = 0;

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n${'='.repeat(70)}`);
    console.log(`TEST ${i + 1}/${testCases.length}: ${testCase.name.toUpperCase()}`);
    console.log(`${'='.repeat(70)}`);

    const result = await testAllTests(testCase);
    results.push(result);

    if (result.success) {
      passedTests++;
    }

    // Wait between tests to avoid overwhelming the system
    if (i < testCases.length - 1) {
      console.log('\n⏳ Waiting 10 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 10000));
    }
  }

  // Final results summary
  console.log('\n' + '='.repeat(70));
  console.log('📊 ENHANCED INTELLIGENCE PIPELINE TEST SUMMARY');
  console.log('='.repeat(70));
  
  console.log(`✅ Tests Passed: ${passedTests}/${testCases.length}`);
  console.log(`❌ Tests Failed: ${testCases.length - passedTests}/${testCases.length}`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / testCases.length) * 100)}%`);

  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    const time = result.executionTime ? `${Math.round(result.executionTime / 1000)}s` : 'N/A';
    const quality = result.qualityScore ? `${result.qualityScore}%` : 'N/A';
    
    console.log(`\n${status} Test ${index + 1}: ${result.testCase}`);
    console.log(`   ⏱️  Time: ${time}`);
    console.log(`   📊 Quality: ${quality}`);
    
    if (result.insights) {
      console.log(`   🔍 Pages Scraped: ${result.insights.scrapedPagesCount}`);
      console.log(`   🎯 Queries Generated: ${result.insights.intelligentQueriesGenerated}`);
    }
    
    if (result.error) {
      console.log(`   ❌ Error: ${result.error}`);
    }
  });

  console.log('\n🎉 Enhanced Intelligence Pipeline Testing Complete!');
  
  if (passedTests === testCases.length) {
    console.log('🏆 ALL TESTS PASSED - Revolutionary research pipeline is working perfectly!');
  } else if (passedTests > 0) {
    console.log('⚠️  PARTIAL SUCCESS - Some tests passed, review failed tests for improvements');
  } else {
    console.log('🚨 ALL TESTS FAILED - Check system configuration and try again');
  }
}

// Helper function name fix
async function testAllTests(testCase) {
  return await testEnhancedIntelligentSupervisor(testCase);
}

// Check if this script is being run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🧠 Enhanced Intelligent Autonomous Supervisor 2025 - Test Suite');
  console.log('🎯 Testing revolutionary research pipeline with comprehensive competitive intelligence\n');
  
  runAllTests().catch(error => {
    console.error('🚨 Test suite failed:', error);
    process.exit(1);
  });
} 