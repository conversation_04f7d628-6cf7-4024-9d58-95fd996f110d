#!/usr/bin/env node

/**
 * Test Script for Qwen Model Integration
 * Verifies that the system is correctly configured to use Qwen 3-235B-A22B
 */

import fetch from 'node-fetch';

const API_URL = 'http://localhost:3001/api/generate/blog';

async function testQwenModel() {
  console.log('🧪 Testing Qwen 3-235B-A22B Model Integration...\n');

  const testPayload = {
    topic: "AI coding assistants comparison test",
    wordCount: 800,
    customInstructions: "Quick test to verify Qwen model is working",
    options: {
      maxDepth: 1,
      skipExternalLinking: true
    }
  };

  try {
    console.log('📤 Sending test request...');
    console.log(`Topic: ${testPayload.topic}`);
    console.log(`Target Length: ${testPayload.wordCount} words\n`);

    const startTime = Date.now();

    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPayload)
    });

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    console.log('✅ Test completed successfully!\n');

    // Display key metrics
    console.log('📊 Test Results:');
    console.log(`⏱️  Duration: ${duration.toFixed(1)}s`);
    
    if (result.costTracking) {
      console.log(`💰 Total Cost: $${result.costTracking.totalCost.toFixed(4)}`);
      console.log(`🔢 Total Tokens: ${(result.costTracking.totalInputTokens + result.costTracking.totalOutputTokens).toLocaleString()}`);
      console.log(`🧠 Qwen Cost: $${result.costTracking.qwenCost.toFixed(4)}`);
      console.log(`💎 Gemini Cost: $${result.costTracking.geminiCost.toFixed(4)}`);
      
      // Verify Qwen is being used
      const qwenSteps = result.costTracking.breakdown.filter(step => step.model === 'qwen');
      console.log(`✨ Qwen Steps: ${qwenSteps.length} (${qwenSteps.map(s => s.step).join(', ')})`);
    }

    if (result.contentScoring) {
      console.log(`📈 Content Score: ${result.contentScoring.overallScore}/100`);
    }

    // Verify model usage in breakdown
    if (result.costTracking?.breakdown) {
      console.log('\n🔍 Model Usage Verification:');
      const modelCounts = result.costTracking.breakdown.reduce((acc, step) => {
        acc[step.model] = (acc[step.model] || 0) + 1;
        return acc;
      }, {});
      
      Object.entries(modelCounts).forEach(([model, count]) => {
        console.log(`  ${model.toUpperCase()}: ${count} steps`);
      });
    }

    console.log('\n🎉 Qwen 3-235B-A22B integration is working correctly!');
    
    // Quick content preview
    if (result.article) {
      const preview = result.article.substring(0, 200) + '...';
      console.log('\n📝 Article Preview:');
      console.log(preview);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Make sure the development server is running:');
      console.log('   npm run dev');
    } else if (error.message.includes('API key')) {
      console.log('\n💡 Check your environment variables:');
      console.log('   OPENROUTER_API_KEY');
      console.log('   GEMINI_API_KEY');
      console.log('   TAVILY_API_KEY');
    }
    
    process.exit(1);
  }
}

// Run the test
testQwenModel(); 