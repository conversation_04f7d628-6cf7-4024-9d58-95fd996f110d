#!/usr/bin/env node

console.log('🚀 Debug: <PERSON>ript started');

try {
  console.log('🔍 Debug: Testing basic functionality');
  
  // Test basic functionality
  console.log('📊 Testing basic operations...');
  
  // Test environment variable
  const envKey = process.env.TAVILY_API_KEY;
  console.log(`🔑 Environment key: ${envKey ? 'Present' : 'Not found'}`);
  
  // Test hardcoded keys array
  const fallbackKeys = [
    '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',
    '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',
    '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'
  ];
  
  console.log(`📋 Fallback keys: ${fallbackKeys.length} keys loaded`);
  
  // Test fetch availability
  const fetchAvailable = typeof fetch !== 'undefined';
  console.log(`🌐 Fetch available: ${fetchAvailable}`);
  
  if (!fetchAvailable) {
    console.log('❌ fetch is not available - importing node-fetch...');
    const fetch = await import('node-fetch');
    console.log('✅ node-fetch imported successfully');
  }
  
  console.log('✅ Debug: Basic checks completed successfully');
  
} catch (error) {
  console.error('❌ Debug: Error occurred:', error);
} 