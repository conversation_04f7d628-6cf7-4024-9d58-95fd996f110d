#!/usr/bin/env node

/**
 * Tavily API Key Testing and Quota Management Script
 * Tests all available API keys and removes those that have reached quota limits
 */

import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';

class TavilyKeyTester {
  constructor() {
    // All available API keys (including environment and fallback keys)
    this.allKeys = [
      process.env.TAVILY_API_KEY, // Environment key
      '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',
      '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',
      '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',
      '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',
      '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',
      '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',
      '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',
      '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',
      '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'
    ].filter(key => key && key.trim()); // Remove null/undefined/empty keys

    this.validKeys = [];
    this.quotaExceededKeys = [];
    this.invalidKeys = [];
    this.networkErrorKeys = [];
    this.testResults = [];
  }

  /**
   * Test a single API key with a simple query
   */
  async testApiKey(apiKey, index) {
    const testQuery = 'test search query';
    const keyLabel = `Key ${index + 1} (...${apiKey.slice(-4)})`;
    
    console.log(`🔍 Testing ${keyLabel}`);

    try {
      const response = await fetch('https://api.tavily.com/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          api_key: apiKey,
          query: testQuery,
          max_results: 1, // Minimal request to save quota
          search_depth: 'basic',
          include_answer: false,
          include_raw_content: false
        }),
        timeout: 15000 // 15 second timeout
      });

      const responseText = await response.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch (parseError) {
        responseData = { raw: responseText };
      }

      const result = {
        key: keyLabel,
        keyValue: apiKey,
        status: response.status,
        success: response.ok,
        responseData: responseData,
        timestamp: new Date().toISOString()
      };

      if (response.ok) {
        console.log(`  ✅ ${keyLabel}: SUCCESS - ${responseData.results?.length || 0} results`);
        this.validKeys.push(apiKey);
        result.category = 'valid';
      } else {
        // Analyze different error types
        let errorMessage = responseData.detail || responseData.error || responseText || 'Unknown error';
        
        // Handle object error messages
        if (typeof errorMessage === 'object') {
          errorMessage = JSON.stringify(errorMessage);
        } else {
          errorMessage = String(errorMessage);
        }
        
        const errorLower = errorMessage.toLowerCase();
        
        if (response.status === 429 || errorLower.includes('quota') || 
            errorLower.includes('limit') || errorLower.includes('exceeded')) {
          console.log(`  ❌ ${keyLabel}: QUOTA EXCEEDED - ${errorMessage}`);
          this.quotaExceededKeys.push(apiKey);
          result.category = 'quota_exceeded';
        } else if (response.status === 401 || response.status === 403 || 
                   errorLower.includes('unauthorized') || 
                   errorLower.includes('invalid') ||
                   errorLower.includes('forbidden')) {
          console.log(`  ❌ ${keyLabel}: INVALID KEY - ${errorMessage}`);
          this.invalidKeys.push(apiKey);
          result.category = 'invalid';
        } else {
          console.log(`  ⚠️  ${keyLabel}: OTHER ERROR (${response.status}) - ${errorMessage}`);
          result.category = 'other_error';
        }
      }

      this.testResults.push(result);
      return result;

    } catch (error) {
      console.log(`  ❌ ${keyLabel}: NETWORK ERROR - ${error.message}`);
      this.networkErrorKeys.push(apiKey);
      
      const result = {
        key: keyLabel,
        keyValue: apiKey,
        status: 'network_error',
        success: false,
        error: error.message,
        category: 'network_error',
        timestamp: new Date().toISOString()
      };
      
      this.testResults.push(result);
      return result;
    }
  }

  /**
   * Test all API keys
   */
  async testAllKeys() {
    console.log('🚀 Starting Tavily API Key Testing');
    console.log(`📊 Testing ${this.allKeys.length} API keys...\n`);

    const testPromises = this.allKeys.map((key, index) => 
      this.testApiKey(key, index)
    );

    // Test keys in parallel but with some delay to avoid rate limiting
    const results = [];
    for (let i = 0; i < testPromises.length; i += 3) {
      const batch = testPromises.slice(i, i + 3);
      const batchResults = await Promise.all(batch);
      results.push(...batchResults);
      
      // Small delay between batches
      if (i + 3 < testPromises.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  /**
   * Generate a comprehensive report
   */
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 TAVILY API KEY TEST REPORT');
    console.log('='.repeat(60));
    
    console.log(`\n📈 SUMMARY:`);
    console.log(`  ✅ Valid Keys:           ${this.validKeys.length}`);
    console.log(`  ❌ Quota Exceeded:       ${this.quotaExceededKeys.length}`);
    console.log(`  🚫 Invalid Keys:         ${this.invalidKeys.length}`);
    console.log(`  🌐 Network Errors:       ${this.networkErrorKeys.length}`);
    console.log(`  📊 Total Tested:         ${this.allKeys.length}`);

    if (this.validKeys.length > 0) {
      console.log(`\n✅ VALID KEYS (${this.validKeys.length}):`);
      this.validKeys.forEach((key, index) => {
        console.log(`  ${index + 1}. ...${key.slice(-4)}`);
      });
    }

    if (this.quotaExceededKeys.length > 0) {
      console.log(`\n❌ QUOTA EXCEEDED KEYS (${this.quotaExceededKeys.length}):`);
      this.quotaExceededKeys.forEach((key, index) => {
        console.log(`  ${index + 1}. ...${key.slice(-4)} - REMOVE THIS KEY`);
      });
    }

    if (this.invalidKeys.length > 0) {
      console.log(`\n🚫 INVALID KEYS (${this.invalidKeys.length}):`);
      this.invalidKeys.forEach((key, index) => {
        console.log(`  ${index + 1}. ...${key.slice(-4)} - REMOVE THIS KEY`);
      });
    }

    if (this.networkErrorKeys.length > 0) {
      console.log(`\n🌐 NETWORK ERROR KEYS (${this.networkErrorKeys.length}) - May be temporary:`);
      this.networkErrorKeys.forEach((key, index) => {
        console.log(`  ${index + 1}. ...${key.slice(-4)} - CHECK MANUALLY`);
      });
    }

    // Recommendations
    console.log(`\n🎯 RECOMMENDATIONS:`);
    
    if (this.quotaExceededKeys.length > 0 || this.invalidKeys.length > 0) {
      console.log(`  1. Remove ${this.quotaExceededKeys.length + this.invalidKeys.length} non-working keys from the rotation system`);
    }
    
    if (this.validKeys.length < 3) {
      console.log(`  2. ⚠️  WARNING: Only ${this.validKeys.length} valid keys remaining - consider adding new keys`);
    }
    
    if (this.validKeys.length === 0) {
      console.log(`  3. 🚨 CRITICAL: No valid keys available - service will fail!`);
    } else {
      console.log(`  3. ✅ Service can continue with ${this.validKeys.length} valid keys`);
    }
  }

  /**
   * Save detailed test results to JSON file
   */
  async saveResults() {
    const resultsFile = `tavily-test-results-${Date.now()}.json`;
    const resultsPath = path.join(process.cwd(), resultsFile);
    
    const reportData = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTested: this.allKeys.length,
        validKeys: this.validKeys.length,
        quotaExceeded: this.quotaExceededKeys.length,
        invalidKeys: this.invalidKeys.length,
        networkErrors: this.networkErrorKeys.length
      },
      validKeys: this.validKeys.map(key => ({ key: `...${key.slice(-4)}`, full: key })),
      quotaExceededKeys: this.quotaExceededKeys.map(key => ({ key: `...${key.slice(-4)}`, full: key })),
      invalidKeys: this.invalidKeys.map(key => ({ key: `...${key.slice(-4)}`, full: key })),
      networkErrorKeys: this.networkErrorKeys.map(key => ({ key: `...${key.slice(-4)}`, full: key })),
      detailedResults: this.testResults,
      recommendations: this.generateRecommendations()
    };

    try {
      await fs.promises.writeFile(resultsPath, JSON.stringify(reportData, null, 2));
      console.log(`\n💾 Detailed results saved to: ${resultsFile}`);
    } catch (error) {
      console.error(`❌ Failed to save results: ${error.message}`);
    }
  }

  /**
   * Generate automated recommendations
   */
  generateRecommendations() {
    const recommendations = [];
    
    if (this.quotaExceededKeys.length > 0) {
      recommendations.push({
        type: 'remove_keys',
        priority: 'high',
        action: 'Remove quota exceeded keys from TavilyApiKeyRotator',
        keys: this.quotaExceededKeys,
        reason: 'These keys have reached their quota limit and will continue to fail'
      });
    }
    
    if (this.invalidKeys.length > 0) {
      recommendations.push({
        type: 'remove_keys',
        priority: 'high', 
        action: 'Remove invalid keys from TavilyApiKeyRotator',
        keys: this.invalidKeys,
        reason: 'These keys are invalid and will never work'
      });
    }
    
    if (this.validKeys.length < 3) {
      recommendations.push({
        type: 'add_keys',
        priority: 'medium',
        action: 'Add new API keys to maintain redundancy',
        reason: `Only ${this.validKeys.length} valid keys remaining`
      });
    }
    
    if (this.validKeys.length === 0) {
      recommendations.push({
        type: 'critical',
        priority: 'critical',
        action: 'Service will fail - immediate action required',
        reason: 'No valid API keys available'
      });
    }
    
    return recommendations;
  }

  /**
   * Update the search.ts file to remove bad keys (optional feature)
   */
  async updateSearchFile() {
    if (this.quotaExceededKeys.length === 0 && this.invalidKeys.length === 0) {
      console.log('\n✅ No keys need to be removed - search.ts is up to date');
      return;
    }

    const searchFilePath = path.join(process.cwd(), 'src', 'lib', 'search.ts');
    
    try {
      const fileContent = await fs.promises.readFile(searchFilePath, 'utf8');
      let updatedContent = fileContent;
      
      // Remove quota exceeded and invalid keys
      const keysToRemove = [...this.quotaExceededKeys, ...this.invalidKeys];
      
      keysToRemove.forEach(key => {
        // Remove the key line from the fallbackKeys array
        const keyPattern = new RegExp(`\\s*'${key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}',?\\s*`, 'g');
        updatedContent = updatedContent.replace(keyPattern, '');
      });
      
      // Clean up any trailing commas
      updatedContent = updatedContent.replace(/,(\s*\])/g, '$1');
      
      console.log(`\n🔧 Would remove ${keysToRemove.length} keys from search.ts`);
      console.log('⚠️  Manual update recommended - review the changes before applying');
      
      // Save backup and updated file
      const backupPath = `${searchFilePath}.backup.${Date.now()}`;
      await fs.promises.writeFile(backupPath, fileContent);
      console.log(`📁 Backup created: ${backupPath}`);
      
    } catch (error) {
      console.error(`❌ Error updating search.ts: ${error.message}`);
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  const tester = new TavilyKeyTester();
  
  try {
    // Test all keys
    await tester.testAllKeys();
    
    // Generate and display report
    tester.generateReport();
    
    // Save detailed results
    await tester.saveResults();
    
    // Optional: Update search file (commented out for safety)
    // await tester.updateSearchFile();
    
  } catch (error) {
    console.error('❌ Script execution failed:', error);
    process.exit(1);
  }
}

// Run the script
console.log('🚀 Starting Tavily API Key Test Script...');

// Check if this is the main module being executed
const isMainModule = process.argv[1] && process.argv[1].endsWith('test-tavily-api-keys.mjs');

if (isMainModule) {
  main().catch((error) => {
    console.error('❌ Script execution failed:', error);
    process.exit(1);
  });
} else {
  console.log('✅ Module loaded for import');
}

export { TavilyKeyTester }; 