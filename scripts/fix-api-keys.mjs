#!/usr/bin/env node

console.log('🔧 API Key Configuration Helper\n');

// Check current API key status
function checkApiKeys() {
  console.log('📋 Current API Key Status:');
  
  const geminiKey = process.env.GEMINI_API_KEY;
  const googleKey = process.env.GOOGLE_API_KEY;
  const openrouterKey = process.env.OPENROUTER_API_KEY;
  
  console.log(`   GEMINI_API_KEY: ${geminiKey && !geminiKey.includes('your_') ? '✅ Configured' : '❌ Missing/Placeholder'}`);
  console.log(`   GOOGLE_API_KEY: ${googleKey && !googleKey.includes('your_') ? '✅ Configured' : '❌ Missing/Placeholder'}`);
  console.log(`   OPENROUTER_API_KEY: ${openrouterKey && !openrouterKey.includes('your_') ? '✅ Configured' : '❌ Missing/Placeholder'}`);
  
  return {
    gemini: geminiKey && !geminiKey.includes('your_'),
    google: googleKey && !googleKey.includes('your_'),
    openrouter: openrouterKey && !openrouterKey.includes('your_')
  };
}

const status = checkApiKeys();

if (!status.gemini && !status.google) {
  console.log('\n🚨 CRITICAL: Gemini API Key Required for Content Generation\n');
  
  console.log('🔑 How to Get a FREE Gemini API Key:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('1. Go to: https://aistudio.google.com/app/apikey');
  console.log('2. Sign in with your Google account');
  console.log('3. Click "Create API Key"');
  console.log('4. Copy the generated API key');
  console.log('5. Add it to your .env.local file');
  
  console.log('\n📝 Update your .env.local file:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('Replace this line:');
  console.log('   GEMINI_API_KEY=your_gemini_api_key_here');
  console.log('With:');
  console.log('   GEMINI_API_KEY=your_actual_api_key_from_google');
  
  console.log('\n💡 Alternative: Use GOOGLE_API_KEY');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('You can also set:');
  console.log('   GOOGLE_API_KEY=your_actual_api_key_from_google');
  console.log('(The system will use either variable)');
  
  console.log('\n⚡ Quick Fix Commands:');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('# Option 1: Edit .env.local directly');
  console.log('nano .env.local');
  console.log('');
  console.log('# Option 2: Use sed command (replace YOUR_KEY with actual key)');
  console.log('sed -i "" "s/GEMINI_API_KEY=your_gemini_api_key_here/GEMINI_API_KEY=YOUR_KEY/" .env.local');
  
} else {
  console.log('\n✅ Gemini API key is configured!');
}

if (!status.openrouter) {
  console.log('\n⚠️ Optional: OpenRouter API Key');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  console.log('For additional AI models, get an OpenRouter key at:');
  console.log('https://openrouter.ai/keys');
}

console.log('\n🔄 After updating API keys:');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('1. Save the .env.local file');
console.log('2. Restart your development server');
console.log('3. Try content generation again');

console.log('\n📞 Need Help?');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('• Google AI Studio: https://aistudio.google.com/');
console.log('• Gemini API Docs: https://ai.google.dev/docs');
console.log('• OpenRouter: https://openrouter.ai/'); 