#!/usr/bin/env node

import { LangGraphAutonomousSupervisor } from '../src/lib/agents/autonomous/LangGraphAutonomousSupervisor.js';

async function testContentValidationFix() {
  console.log('🧪 Testing Content Validation Fix');
  console.log('=====================================');

  const testCases = [
    {
      name: 'Normal Case',
      goal: 'How to start a blog',
      expectedOutcome: 'Should generate content successfully'
    },
    {
      name: 'Complex Topic',
      goal: 'Artificial Intelligence in Healthcare',
      expectedOutcome: 'Should generate content successfully'
    },
    {
      name: 'Simple Topic',
      goal: 'Coffee',
      expectedOutcome: 'Should generate content successfully'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name}`);
    console.log(`   Goal: ${testCase.goal}`);
    console.log(`   Expected: ${testCase.expectedOutcome}`);
    
    try {
      const supervisor = new LangGraphAutonomousSupervisor({
        maxRetries: 2,
        qualityThreshold: 70,
        enableWebAccess: true,
        enableSelfImprovement: true,
        cooperationMode: 'collaborative'
      });

      const startTime = Date.now();
      const result = await supervisor.executeAutonomous(testCase.goal);
      const executionTime = Date.now() - startTime;

      console.log(`   ✅ Success in ${executionTime}ms`);
      console.log(`   📊 Result structure:`);
      console.log(`      - success: ${result.success}`);
      console.log(`      - hasResult: ${!!result.result}`);
      console.log(`      - hasTitle: ${!!result.result?.title}`);
      console.log(`      - hasContent: ${!!result.result?.content}`);
      console.log(`      - titleLength: ${result.result?.title?.length || 0}`);
      console.log(`      - contentLength: ${result.result?.content?.length || 0}`);
      console.log(`      - wordCount: ${result.result?.wordCount || 0}`);
      console.log(`      - qualityScore: ${result.qualityScore || 0}`);

      // Validate content
      if (!result.result?.content || result.result.content.trim().length === 0) {
        console.log(`   ❌ FAILURE: Empty content generated`);
      } else if (result.result.content.length < 100) {
        console.log(`   ⚠️  WARNING: Content too short (${result.result.content.length} chars)`);
      } else {
        console.log(`   ✅ Content validation passed`);
      }

      // Show content preview
      const contentPreview = result.result?.content?.substring(0, 200) || 'NO CONTENT';
      console.log(`   📄 Content preview: ${contentPreview}...`);

    } catch (error) {
      console.log(`   ❌ FAILED: ${error.message}`);
      console.log(`   📋 Error details:`, error);
    }
  }

  console.log('\n🎯 Testing Complete');
  console.log('====================');
}

// Test the API endpoint structure
async function testAPIEndpoint() {
  console.log('\n🌐 Testing API Endpoint Structure');
  console.log('=================================');

  try {
    const supervisor = new LangGraphAutonomousSupervisor();
    const result = await supervisor.executeAutonomous('Test topic');

    // Simulate the API response structure
    const apiResponse = {
      success: true,
      result: result.result,
      insights: {
        totalSources: result.insights?.totalSources || 0,
        competitorsAnalyzed: result.insights?.competitorsAnalyzed || 0,
        iterationsCompleted: result.insights?.iterationsCompleted || 1,
        finalQuality: result.qualityScore || 85,
        totalDecisions: result.insights?.totalDecisions || 1
      },
      message: 'Autonomous execution completed successfully'
    };

    console.log('📊 API Response Structure:');
    console.log(`   - success: ${apiResponse.success}`);
    console.log(`   - result: ${typeof apiResponse.result}`);
    console.log(`   - result.title: ${!!apiResponse.result?.title}`);
    console.log(`   - result.content: ${!!apiResponse.result?.content}`);
    console.log(`   - insights: ${typeof apiResponse.insights}`);

    // Test frontend extraction logic
    console.log('\n🔍 Testing Frontend Extraction Logic:');
    
    // Simulate autonomous mode extraction
    let articleTitle = '';
    let articleContent = '';
    
    if (apiResponse.result) {
      articleTitle = apiResponse.result.title || '';
      articleContent = apiResponse.result.content || '';
    }

    console.log(`   - Extracted title: ${!!articleTitle} (${articleTitle.length} chars)`);
    console.log(`   - Extracted content: ${!!articleContent} (${articleContent.length} chars)`);
    
    // Validation
    if (!articleContent || articleContent.trim().length === 0) {
      console.log('   ❌ Content validation would fail');
    } else if (articleContent.trim().length < 50) {
      console.log('   ⚠️  Content too short');
    } else {
      console.log('   ✅ Content validation would pass');
    }

  } catch (error) {
    console.log(`   ❌ API Test failed: ${error.message}`);
  }
}

// Run all tests
async function runAllTests() {
  try {
    await testContentValidationFix();
    await testAPIEndpoint();
    console.log('\n🎉 All tests completed!');
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

runAllTests(); 