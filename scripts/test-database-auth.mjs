#!/usr/bin/env node

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function testDatabase() {
  console.log('🧪 Testing Database and Authentication System\n')

  try {
    // Test database connection
    console.log('1. Testing database connection...')
    await prisma.$connect()
    console.log('✅ Database connected successfully\n')

    // Test user creation (simulate Google OAuth signup)
    console.log('2. Testing user creation...')
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
        firstName: 'Test',
        lastName: 'User',
        settings: {
          create: {
            // Default settings will be applied
          }
        },
        subscription: {
          create: {
            plan: 'free',
            status: 'active'
          }
        },
        quotas: {
          create: [
            {
              quotaType: 'blog_posts',
              totalLimit: 5,
              resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
            },
            {
              quotaType: 'emails',
              totalLimit: 10,
              resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
            },
            {
              quotaType: 'social_media',
              totalLimit: 20,
              resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
            },
            {
              quotaType: 'youtube_scripts',
              totalLimit: 3,
              resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
            },
            {
              quotaType: 'invincible_research',
              totalLimit: 2,
              resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
            }
          ]
        }
      },
      include: {
        settings: true,
        subscription: true,
        quotas: true
      }
    })
    console.log('✅ Test user created:', testUser.email)
    console.log(`   - Plan: ${testUser.subscription?.plan}`)
    console.log(`   - Quotas: ${testUser.quotas.length} types configured\n`)

    // Test quota checking
    console.log('3. Testing quota system...')
    const blogQuota = testUser.quotas.find(q => q.quotaType === 'blog_posts')
    console.log(`   - Blog posts: ${blogQuota?.used}/${blogQuota?.totalLimit}`)
    
    // Simulate quota usage
    await prisma.userQuota.update({
      where: { id: blogQuota?.id },
      data: { used: { increment: 1 } }
    })
    console.log('   - Used 1 blog post quota')

    // Test content creation
    console.log('4. Testing content storage...')
    const content = await prisma.content.create({
      data: {
        userId: testUser.id,
        type: 'blog',
        title: 'Test Blog Post',
        content: 'This is a test blog post content...',
        wordCount: 100,
        tone: 'professional',
        metadata: JSON.stringify({
          targetKeyword: 'test',
          includeResearch: false
        })
      }
    })
    console.log('✅ Test content created:', content.title)

    // Test usage history
    console.log('5. Testing usage tracking...')
    await prisma.usageHistory.create({
      data: {
        userId: testUser.id,
        action: 'content_generated',
        type: 'blog_posts',
        metadata: JSON.stringify({
          contentId: content.id,
          quotaUsed: 1
        })
      }
    })
    console.log('✅ Usage history recorded\n')

    // Display final user stats
    console.log('6. Final user statistics:')
    const finalUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      include: {
        settings: true,
        subscription: true,
        quotas: true,
        content: true,
        usageHistory: true,
        _count: {
          select: {
            content: true,
            usageHistory: true
          }
        }
      }
    })

    console.log(`   - Total content pieces: ${finalUser?._count.content}`)
    console.log(`   - Total usage events: ${finalUser?._count.usageHistory}`)
    console.log(`   - Account created: ${finalUser?.createdAt.toISOString()}`)
    
    // Clean up test data
    console.log('\n7. Cleaning up test data...')
    await prisma.user.delete({
      where: { id: testUser.id }
    })
    console.log('✅ Test data cleaned up')

    console.log('\n🎉 All database and authentication tests passed!')

  } catch (error) {
    console.error('❌ Test failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

testDatabase() 