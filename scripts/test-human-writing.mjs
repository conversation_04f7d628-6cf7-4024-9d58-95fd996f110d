#!/usr/bin/env node

/**
 * Test Human Writing Enhancement
 * Verifies that the agent generates human-like content instead of AI-like writing
 */

console.log('✍️ Testing Human Writing Enhancement\n');

const COLORS = {
  GREEN: '\x1b[32m',
  RED: '\x1b[31m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  BRIGHT: '\x1b[1m',
  RESET: '\x1b[0m'
};

function colorize(text, color) {
  return `${color}${text}${COLORS.RESET}`;
}

// Human writing indicators to look for
const HUMAN_INDICATORS = [
  // Personal pronouns and direct address
  /\bI['']?(?:ve|ll|m|d)?\b/gi,
  /\byou['']?(?:re|ll|ve|d)?\b/gi,
  /\bwe['']?(?:re|ll|ve|d)?\b/gi,
  
  // Conversational elements
  /(?:look|listen|here['']s the thing|let me tell you)/gi,
  /(?:you know what|guess what|speaking of which)/gi,
  /(?:but wait|hold up|hang on)/gi,
  
  // Personal experience markers
  /(?:in my experience|i['']?ve found|after years of)/gi,
  /(?:i learned|i discovered|i realized)/gi,
  /(?:what really excites me|what drives me crazy)/gi,
  
  // Emotional expressions
  /(?:honestly|frankly|to be honest)/gi,
  /(?:i love|i hate|i can['']t stand)/gi,
  /(?:this is where it gets interesting|this blew my mind)/gi,
  
  // Casual connectors
  /(?:anyway|by the way|oh and)/gi,
  /(?:plus|also|another thing)/gi,
  
  // Questions and engagement
  /\?[^"]/g, // Questions not in quotes
  /(?:sound familiar|know what i mean|make sense)/gi,
  /(?:wondering|curious about)/gi
];

// AI-like writing indicators to avoid
const AI_INDICATORS = [
  /(?:it is important to note|it should be noted)/gi,
  /(?:in conclusion|to summarize|in summary)/gi,
  /(?:furthermore|moreover|additionally)/gi,
  /(?:according to research|studies have shown)/gi,
  /(?:it is worth mentioning|it is essential to)/gi,
  /(?:one must consider|one should note)/gi,
  /(?:in the realm of|in the context of|in terms of)/gi
];

async function testHumanWriting() {
  console.log(colorize('🚀 Testing Human Writing Enhancement...', COLORS.BRIGHT));
  
  const testTopic = 'best ai writing tools for content creators';
  
  try {
    console.log(colorize(`\n📝 Testing with topic: "${testTopic}"`, COLORS.CYAN));
    
    const response = await fetch('http://localhost:3000/api/invincible', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        topic: testTopic,
        contentLength: 1000,
        searchDepth: 3,
        competitorCount: 3,
        deepSearchQueriesPerTopic: 3,
        targetAudience: 'content creators',
        tone: 'conversational'
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.log(colorize(`❌ API request failed: ${response.status}`, COLORS.RED));
      console.log(colorize(`Error: ${errorText.substring(0, 200)}...`, COLORS.RED));
      return;
    }

    const result = await response.json();
    
    if (!result.success || !result.article?.content) {
      console.log(colorize('❌ No article content generated', COLORS.RED));
      return;
    }

    console.log(colorize('\n✅ Article generated successfully!', COLORS.GREEN));
    
    const content = result.article.content;
    const wordCount = result.article.wordCount;
    
    console.log(colorize(`📊 Word count: ${wordCount}`, COLORS.WHITE));
    console.log(colorize(`📝 Title: ${result.article.title}`, COLORS.WHITE));
    
    // Analyze human writing patterns
    console.log(colorize('\n🧠 HUMAN WRITING ANALYSIS:', COLORS.MAGENTA));
    
    let humanScore = 0;
    let totalHumanMatches = 0;
    
    HUMAN_INDICATORS.forEach((pattern, index) => {
      const matches = (content.match(pattern) || []).length;
      if (matches > 0) {
        totalHumanMatches += matches;
        humanScore += matches;
        const description = getPatternDescription(index);
        console.log(colorize(`   ✅ ${description}: ${matches} instances`, COLORS.GREEN));
      }
    });
    
    // Check for AI-like patterns
    console.log(colorize('\n🤖 AI-LIKE PATTERN CHECK:', COLORS.YELLOW));
    
    let aiScore = 0;
    let totalAiMatches = 0;
    
    AI_INDICATORS.forEach((pattern, index) => {
      const matches = (content.match(pattern) || []).length;
      if (matches > 0) {
        totalAiMatches += matches;
        aiScore += matches;
        const description = getAiPatternDescription(index);
        console.log(colorize(`   ⚠️ ${description}: ${matches} instances`, COLORS.RED));
      }
    });
    
    if (totalAiMatches === 0) {
      console.log(colorize('   ✅ No AI-like patterns detected!', COLORS.GREEN));
    }
    
    // Calculate scores
    const humanness = Math.min(100, (humanScore / Math.max(1, wordCount / 100)) * 100);
    const aiRatio = (aiScore / Math.max(1, wordCount / 100)) * 100;
    
    console.log(colorize('\n📊 WRITING ANALYSIS RESULTS:', COLORS.BRIGHT));
    console.log(colorize(`   Human Writing Score: ${humanness.toFixed(1)}%`, 
      humanness > 70 ? COLORS.GREEN : humanness > 40 ? COLORS.YELLOW : COLORS.RED));
    console.log(colorize(`   AI Pattern Ratio: ${aiRatio.toFixed(1)}%`, 
      aiRatio < 10 ? COLORS.GREEN : aiRatio < 25 ? COLORS.YELLOW : COLORS.RED));
    
    // Sample content preview
    console.log(colorize('\n📖 CONTENT PREVIEW (First 300 characters):', COLORS.CYAN));
    console.log(colorize(content.substring(0, 300) + '...', COLORS.WHITE));
    
    // Overall assessment
    console.log(colorize('\n🎯 OVERALL ASSESSMENT:', COLORS.BRIGHT));
    
    if (humanness > 70 && aiRatio < 15) {
      console.log(colorize('   ✅ EXCELLENT: Content sounds genuinely human-written!', COLORS.GREEN));
    } else if (humanness > 50 && aiRatio < 25) {
      console.log(colorize('   ⚡ GOOD: Content has human elements but could be more conversational', COLORS.YELLOW));
    } else if (humanness > 30) {
      console.log(colorize('   ⚠️ NEEDS IMPROVEMENT: Content still sounds somewhat AI-generated', COLORS.YELLOW));
    } else {
      console.log(colorize('   ❌ POOR: Content sounds very AI-like, needs significant enhancement', COLORS.RED));
    }
    
    // Specific recommendations
    console.log(colorize('\n💡 ENHANCEMENT RECOMMENDATIONS:', COLORS.BLUE));
    
    if (humanScore < 10) {
      console.log(colorize('   • Add more personal experience markers ("I\'ve found", "In my experience")', COLORS.WHITE));
      console.log(colorize('   • Include conversational elements ("Look", "Here\'s the thing")', COLORS.WHITE));
    }
    
    if (aiScore > 5) {
      console.log(colorize('   • Reduce formal academic language', COLORS.WHITE));
      console.log(colorize('   • Replace "it is important to note" with conversational transitions', COLORS.WHITE));
    }
    
    if (!content.includes('?')) {
      console.log(colorize('   • Add rhetorical questions to engage readers', COLORS.WHITE));
    }
    
    console.log(colorize('\n🎉 Human writing test completed!', COLORS.GREEN));
    
  } catch (error) {
    console.error(colorize('❌ Test failed:', COLORS.RED), error.message);
  }
}

function getPatternDescription(index) {
  const descriptions = [
    'Personal pronouns (I, you, we)',
    'Conversational starters',
    'Attention grabbers',
    'Personal experience markers',
    'Discovery language',
    'Emotional expressions',
    'Honest expressions',
    'Emotional preferences',
    'Excitement markers',
    'Casual connectors',
    'Additional connectors',
    'Rhetorical questions',
    'Familiarity checks',
    'Engagement questions'
  ];
  return descriptions[index] || 'Human pattern';
}

function getAiPatternDescription(index) {
  const descriptions = [
    'Formal notation phrases',
    'Academic conclusions',
    'Formal connectors',
    'Research references',
    'Academic mentions',
    'Formal considerations',
    'Academic contexts'
  ];
  return descriptions[index] || 'AI pattern';
}

async function checkServerHealth() {
  try {
    const response = await fetch('http://localhost:3000/api/invincible', {
      method: 'GET'
    });
    
    if (response.ok) {
      console.log(colorize('✅ Server is running', COLORS.GREEN));
      return true;
    } else {
      console.log(colorize(`❌ Server responded with status: ${response.status}`, COLORS.RED));
      return false;
    }
  } catch (error) {
    console.log(colorize(`❌ Server not accessible: ${error.message}`, COLORS.RED));
    console.log(colorize('💡 Please start the development server with: npm run dev', COLORS.YELLOW));
    return false;
  }
}

async function main() {
  console.log(colorize('✍️ Human Writing Enhancement Test\n', COLORS.BRIGHT));
  
  const serverHealthy = await checkServerHealth();
  
  if (!serverHealthy) {
    process.exit(1);
  }
  
  await testHumanWriting();
}

main().catch(error => {
  console.error(colorize('❌ Test script failed:', COLORS.RED), error);
  process.exit(1);
}); 