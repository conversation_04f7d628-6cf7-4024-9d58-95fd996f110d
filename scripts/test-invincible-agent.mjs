#!/usr/bin/env node

/**
 * Test script for Invincible .1V Super Agent
 * Tests the agent architecture and basic functionality
 */

import { InvincibleSupervisor } from '../src/lib/agents/invincible/InvincibleSupervisor.js'

async function testInvincibleAgent() {
  console.log('🚀 Testing Invincible .1V Super Agent')
  console.log('=' .repeat(50))

  try {
    // Test 1: Agent Info
    console.log('\n📋 Test 1: Agent Information')
    const supervisor = new InvincibleSupervisor()
    const agentInfo = supervisor.getAgentInfo()
    
    console.log('✅ Agent Name:', agentInfo.name)
    console.log('✅ Version:', agentInfo.version)
    console.log('✅ Capabilities:', agentInfo.capabilities.length)
    console.log('✅ Sub-agents:', agentInfo.agents.length)

    // Test 2: Basic Content Generation (with mock data)
    console.log('\n📝 Test 2: Content Generation Test')
    const testRequest = {
      topic: 'Artificial Intelligence in 2025',
      wordCount: 500,
      tone: 'Professional and informative',
      targetAudience: 'Tech professionals',
      includeYouTube: false, // Disable for faster testing
      additionalInstructions: 'Focus on practical applications'
    }

    console.log('📤 Request:', testRequest)
    console.log('⏳ Starting generation...')

    const startTime = Date.now()
    const result = await supervisor.generateContent(
      testRequest,
      (progress) => {
        console.log(`📊 Progress: ${progress.stage} - ${progress.progress}% - ${progress.message}`)
      }
    )

    const endTime = Date.now()
    const duration = (endTime - startTime) / 1000

    if (result.success) {
      console.log('\n✅ Content Generation Successful!')
      console.log('📊 Results:')
      console.log(`   - Title: ${result.content?.title || 'N/A'}`)
      console.log(`   - Word Count: ${result.content?.metadata?.wordCount || 0}`)
      console.log(`   - Web Sources: ${result.research?.webResults?.length || 0}`)
      console.log(`   - YouTube Videos: ${result.research?.youtubeVideos?.length || 0}`)
      console.log(`   - Total Sources: ${result.totalSources}`)
      console.log(`   - Processing Time: ${duration}s`)
      console.log(`   - Content Preview: ${result.content?.content?.substring(0, 200) || 'N/A'}...`)
    } else {
      console.log('❌ Content Generation Failed:', result.error)
    }

  } catch (error) {
    console.error('❌ Test Failed:', error.message)
    console.error('Stack:', error.stack)
  }

  console.log('\n' + '=' .repeat(50))
  console.log('🏁 Test Complete')
}

// Run the test
testInvincibleAgent().catch(console.error)
