#!/usr/bin/env node

console.log('🔍 Authentication Issue Diagnosis\n');

// Check 1: Environment Variables
console.log('1. 📋 Environment Variables Check:');
const requiredEnvVars = [
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET', 
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL',
  'DATABASE_URL'
];

let allEnvVarsPresent = true;
requiredEnvVars.forEach(envVar => {
  const value = process.env[envVar];
  if (value) {
    if (envVar.includes('SECRET') || envVar.includes('CLIENT_SECRET')) {
      console.log(`   ✅ ${envVar}: ${'*'.repeat(10)}...`);
    } else {
      console.log(`   ✅ ${envVar}: ${value}`);
    }
  } else {
    console.log(`   ❌ ${envVar}: Missing`);
    allEnvVarsPresent = false;
  }
});

// Check 2: Google OAuth Configuration
console.log('\n2. 🔧 Google OAuth Configuration:');
const clientId = process.env.GOOGLE_CLIENT_ID;
if (clientId) {
  console.log(`   ✅ Client ID format: ${clientId.includes('.apps.googleusercontent.com') ? 'Valid' : 'Invalid'}`);
} else {
  console.log('   ❌ Client ID: Missing');
}

const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
if (clientSecret) {
  console.log(`   ✅ Client Secret format: ${clientSecret.startsWith('GOCSPX-') ? 'Valid' : 'Invalid'}`);
} else {
  console.log('   ❌ Client Secret: Missing');
}

// Check 3: Google Cloud Console Requirements
console.log('\n3. 🌐 Google Cloud Console Requirements:');
console.log('   Your Google Cloud Console OAuth client MUST have these EXACT settings:');
console.log(`   📍 Authorized JavaScript origins: ${process.env.NEXTAUTH_URL}`);
console.log(`   📍 Authorized redirect URIs: ${process.env.NEXTAUTH_URL}/api/auth/callback/google`);

// Check 4: Database Connection
console.log('\n4. 💾 Database Status:');
try {
  const fs = await import('fs');
  const path = './prisma/dev.db';
  if (fs.existsSync(path)) {
    console.log('   ✅ Database file exists');
  } else {
    console.log('   ❌ Database file missing - run: npx prisma db push');
  }
} catch (error) {
  console.log('   ⚠️ Could not check database file');
}

// Solutions and Fixes
console.log('\n🔧 SOLUTIONS TO TRY:\n');

console.log('SOLUTION 1: Google Cloud Console Configuration');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('1. Go to: https://console.cloud.google.com/');
console.log('2. Navigate to APIs & Services > Credentials');
console.log('3. Find your OAuth 2.0 Client ID');
console.log('4. Click Edit (pencil icon)');
console.log('5. Under "Authorized JavaScript origins" add:');
console.log(`   http://localhost:3000`);
console.log('6. Under "Authorized redirect URIs" add:');
console.log(`   http://localhost:3000/api/auth/callback/google`);
console.log('7. Click SAVE');

console.log('\nSOLUTION 2: Fix NextAuth Configuration');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('The issue might be with session strategy. Try switching to JWT:');
console.log('Edit src/lib/auth.ts and change:');
console.log('   session: { strategy: "database" }');
console.log('   to:');
console.log('   session: { strategy: "jwt" }');

console.log('\nSOLUTION 3: Clear Browser Data');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('1. Open browser dev tools (F12)');
console.log('2. Go to Application/Storage tab');
console.log('3. Clear all data for localhost:3000');
console.log('4. Or use incognito/private mode');

console.log('\nSOLUTION 4: Check Browser Console');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('1. Open browser dev tools (F12)');
console.log('2. Go to Console tab');
console.log('3. Try signing in and look for error messages');
console.log('4. Check Network tab for failed requests');

console.log('\nSOLUTION 5: Database Reset (if needed)');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('If database issues persist:');
console.log('1. Stop the dev server');
console.log('2. Run: rm prisma/dev.db');
console.log('3. Run: npx prisma db push');
console.log('4. Restart dev server');

console.log('\n📋 QUICK TEST STEPS:');
console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
console.log('1. Start dev server: npm run dev');
console.log('2. Open: http://localhost:3000/login');
console.log('3. Open browser dev tools (F12) -> Console');
console.log('4. Click "Continue with Google"');
console.log('5. Watch for error messages in console');
console.log('6. If redirected to Google, sign in');
console.log('7. Check if you get redirected back properly');

if (!allEnvVarsPresent) {
  console.log('\n❌ CRITICAL: Fix missing environment variables first!');
} else {
  console.log('\n✅ Environment looks good. Most likely cause: Google Cloud Console configuration.');
}

console.log('\n💡 TIP: Most authentication failures are due to incorrect redirect URIs in Google Cloud Console.');
console.log('Make sure the URIs match EXACTLY (including http/https and port).'); 