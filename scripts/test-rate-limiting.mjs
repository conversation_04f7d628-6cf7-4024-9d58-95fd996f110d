#!/usr/bin/env node

import { YouTubeService } from '../src/lib/youtube-service.ts'

async function testRateLimiting() {
  console.log('🧪 Testing YouTube Service Rate Limiting System\n')
  
  const youtubeService = new YouTubeService()
  
  // Test 1: Test rapid API calls to trigger rate limiting
  console.log('📊 Test 1: Testing rapid API calls to YouTube Data API')
  console.log('Making 5 rapid calls to getCaptionTracks...\n')
  
  const testVideoId = 'dQw4w9WgXcQ' // Rick Roll video ID for testing
  
  for (let i = 0; i < 5; i++) {
    console.log(`🔄 Attempt ${i + 1}:`)
    const start = Date.now()
    
    try {
      const tracks = await youtubeService.getCaptionTracks(testVideoId)
      const duration = Date.now() - start
      console.log(`  ✅ Success: ${tracks.length} tracks found in ${duration}ms`)
    } catch (error) {
      const duration = Date.now() - start
      console.log(`  ❌ Error in ${duration}ms:`, error.message)
    }
    
    console.log()
    
    // Small delay between attempts
    if (i < 4) {
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }
  
  console.log('⏱️ Waiting 2 seconds before next test...\n')
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  // Test 2: Test with a video that should work with Supadata
  console.log('📊 Test 2: Testing Supadata API with rate limiting')
  console.log('Making 3 calls to test Supadata rate limiting...\n')
  
  for (let i = 0; i < 3; i++) {
    console.log(`🔄 Supadata Attempt ${i + 1}:`)
    const start = Date.now()
    
    try {
      const captions = await youtubeService.extractCaptions(testVideoId)
      const duration = Date.now() - start
      console.log(`  ✅ Success: ${captions.length} captions extracted in ${duration}ms`)
    } catch (error) {
      const duration = Date.now() - start
      console.log(`  ❌ Error in ${duration}ms:`, error.message)
    }
    
    console.log()
    
    // Small delay between attempts
    if (i < 2) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }
  
  console.log('✅ Rate limiting test completed!')
  console.log('\n📋 Expected behavior:')
  console.log('  - Initial calls should succeed (if API keys are valid)')
  console.log('  - After failures, cooling periods should activate')
  console.log('  - Rate limiting messages should appear')
  console.log('  - API calls should be skipped during cooling periods')
}

testRateLimiting().catch(console.error) 