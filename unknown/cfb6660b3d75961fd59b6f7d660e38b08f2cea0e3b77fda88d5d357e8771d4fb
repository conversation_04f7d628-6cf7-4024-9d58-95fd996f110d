import { NextRequest, NextResponse } from 'next/server'
import { GeminiService } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
    const { topic, style, includeHashtags } = await request.json()

    if (!topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      )
    }

    const gemini = new GeminiService()
    
    console.log('🐦 Generating tweet...')
    const tweet = await gemini.generateTweet(
      topic,
      style || 'engaging',
      includeHashtags !== false
    )

    console.log('✅ Tweet generated successfully')

    return NextResponse.json({
      success: true,
      content: tweet
    })

  } catch (error) {
    console.error('Tweet generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate tweet' },
      { status: 500 }
    )
  }
}
