import { NextRequest, NextResponse } from 'next/server'
import { GoogleSearchService } from '@/lib/search'
import { GeminiService } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
    const { query } = await request.json()

    if (!query) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      )
    }

    console.log('🔍 Searching for competition websites...')
    const searchService = new GoogleSearchService()
    
    // Extract keywords from query using Gemini
    const gemini = new GeminiService()
    const keywords = await gemini.extractKeywords(query)
    
    console.log(`🎯 Extracted keywords: ${keywords.trim()}`)
    
    // Search for competition websites
    const searchResponse = await searchService.search(keywords.trim(), 10)
    
    // Format results for frontend
    const competitionSites = searchResponse.items.map(item => ({
      title: item.title,
      url: item.link,
      snippet: item.snippet,
      displayLink: item.displayLink
    }))

    console.log(`📊 Found ${competitionSites.length} competition websites`)

    return NextResponse.json({
      success: true,
      competitionSites,
      searchQuery: keywords.trim()
    })

  } catch (error) {
    console.error('Competition search error:', error)
    return NextResponse.json(
      { error: 'Failed to search for competition websites' },
      { status: 500 }
    )
  }
}
