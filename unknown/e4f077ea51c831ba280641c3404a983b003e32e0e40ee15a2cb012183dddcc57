import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Safely decode URI component with fallback handling
 * @param encodedString - The URI encoded string to decode
 * @param fallback - Optional fallback value if decoding fails
 * @returns Decoded string or fallback/original string if decoding fails
 */
export function safeDecodeURIComponent(encodedString: string, fallback?: string): string {
  if (!encodedString) {
    return fallback || '';
  }
  
  try {
    return decodeURIComponent(encodedString);
  } catch (error) {
    console.warn('URI decode failed, using fallback:', error);
    return fallback || encodedString;
  }
}

/**
 * Safely encode URI component with error handling
 * @param str - The string to encode
 * @returns Encoded string or empty string if encoding fails
 */
export function safeEncodeURIComponent(str: string): string {
  if (!str) return '';
  
  try {
    return encodeURIComponent(str);
  } catch (error) {
    console.warn('URI encode failed:', error);
    return '';
  }
} 