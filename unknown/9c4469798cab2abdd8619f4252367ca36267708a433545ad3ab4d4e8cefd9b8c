'use client'

import { useRef, useState, useEffect } from 'react'

// Blog Writer Animation
function BlogWriterIcon() {
  const [currentText, setCurrentText] = useState('')
  const [showCursor, setShowCursor] = useState(true)
  const [wordCount, setWordCount] = useState(0)
  const [readabilityScore, setReadabilityScore] = useState(85)
  const typewriterRef = useRef<NodeJS.Timeout | null>(null)
  const cursorRef = useRef<NodeJS.Timeout | null>(null)

  const blogTexts = [
    "The Future of AI in Content Creation: Transforming Digital Marketing",
    "10 Proven Strategies for Building Engaging Blog Content That Converts",
    "How Machine Learning is Revolutionizing Content Strategy in 2024",
    "The Complete Guide to SEO-Optimized Blog Writing with AI Tools"
  ]

  useEffect(() => {
    let textIndex = 0
    let charIndex = 0
    let isDeleting = false

    const typeText = () => {
      const currentFullText = blogTexts[textIndex]
      
      if (!isDeleting) {
        setCurrentText(currentFullText.substring(0, charIndex + 1))
        charIndex++
        setWordCount(Math.floor(charIndex / 5))
        
        if (charIndex === currentFullText.length) {
          setTimeout(() => {
            isDeleting = true
            setReadabilityScore(Math.floor(Math.random() * 15) + 85)
          }, 2000)
        }
      } else {
        setCurrentText(currentFullText.substring(0, charIndex - 1))
        charIndex--
        setWordCount(Math.floor(charIndex / 5))
        
        if (charIndex === 0) {
          isDeleting = false
          textIndex = (textIndex + 1) % blogTexts.length
        }
      }
    }

    typewriterRef.current = setInterval(typeText, isDeleting ? 30 : 120)

    return () => {
      if (typewriterRef.current) clearInterval(typewriterRef.current)
    }
  }, [])

  useEffect(() => {
    cursorRef.current = setInterval(() => {
      setShowCursor(prev => !prev)
    }, 500)

    return () => {
      if (cursorRef.current) clearInterval(cursorRef.current)
    }
  }, [])

  return (
    <div className="relative w-full h-full flex items-center justify-center p-4">
      {/* Darker Glass Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900/40 via-black/60 to-gray-900/40 backdrop-blur-2xl"></div>
      
      {/* Main Content */}
      <div className="relative z-10 w-full max-w-sm">
        {/* Blog Editor Interface */}
        <div className="w-full h-[280px] bg-black/30 backdrop-blur-2xl rounded-xl border border-white/10 shadow-2xl relative overflow-hidden">
          {/* Header Bar */}
          <div className="h-12 bg-black/50 backdrop-blur-2xl border-b border-white/5 flex items-center justify-between px-4">
            <div className="flex items-center space-x-2">
                              <div className="w-8 h-8 bg-pink-500/20 backdrop-blur-xl rounded-lg flex items-center justify-center border border-pink-500/30">
                  <span className="text-pink-400 font-bold text-sm">✍️</span>
                </div>
                <div>
                  <h3 className="text-white/90 font-semibold text-sm">AI Blog Writer</h3>
                  <p className="text-white/60 text-xs">Professional Content Creation</p>
                </div>
            </div>
                          <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-400/60 rounded-full animate-pulse shadow-lg"></div>
                <span className="text-white/80 text-xs font-medium">LIVE</span>
              </div>
          </div>

          {/* Content Area */}
          <div className="p-4 h-full">
            {/* Title Input */}
            <div className="mb-4">
              <label className="text-white/50 text-xs font-medium mb-1 block">Blog Title</label>
              <div className="bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 min-h-[60px] relative">
                <div className="text-white/90 font-semibold text-sm leading-relaxed">
                  {currentText}
                  <span className={`ml-1 ${showCursor ? 'opacity-100' : 'opacity-0'} transition-opacity text-pink-400/80`}>|</span>
                </div>
              </div>
            </div>

            {/* Content Preview Lines */}
            <div className="space-y-2">
              <div className="h-1.5 bg-white/5 rounded-full w-full animate-pulse"></div>
              <div className="h-1.5 bg-white/5 rounded-full w-5/6 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="h-1.5 bg-white/5 rounded-full w-4/5 animate-pulse" style={{ animationDelay: '0.4s' }}></div>
              <div className="h-1.5 bg-white/5 rounded-full w-full animate-pulse" style={{ animationDelay: '0.6s' }}></div>
              <div className="h-1.5 bg-white/5 rounded-full w-3/4 animate-pulse" style={{ animationDelay: '0.8s' }}></div>
            </div>
          </div>

          {/* Glass Overlay Effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"></div>
        </div>

        {/* Stats Panel */}
        <div className="mt-4 flex space-x-2">
          <div className="bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 flex-1">
            <div className="text-center">
              <div className="text-lg font-bold text-pink-400/90 mb-1">{wordCount}</div>
              <div className="text-white/50 text-xs">Words</div>
            </div>
          </div>
          <div className="bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 flex-1">
            <div className="text-center">
              <div className="text-lg font-bold text-rose-400/90 mb-1">{readabilityScore}%</div>
              <div className="text-white/50 text-xs">SEO Score</div>
            </div>
          </div>
          <div className="bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 flex-1">
            <div className="text-center">
              <div className="text-lg font-bold text-pink-500/90 mb-1">A+</div>
              <div className="text-white/50 text-xs">Quality</div>
            </div>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute -top-2 -left-2 bg-pink-500/20 backdrop-blur-2xl text-pink-400 px-3 py-1 rounded-full text-xs font-semibold border border-pink-500/20 animate-float">
          📝 Writing
        </div>
        <div className="absolute -top-1 -right-3 bg-rose-500/20 backdrop-blur-2xl text-rose-400 px-3 py-1 rounded-full text-xs font-semibold border border-rose-500/20 animate-float" style={{ animationDelay: '0.5s' }}>
          🎯 SEO
        </div>
        <div className="absolute -bottom-2 -left-3 bg-pink-600/20 backdrop-blur-2xl text-pink-400 px-3 py-1 rounded-full text-xs font-semibold border border-pink-600/20 animate-float" style={{ animationDelay: '1s' }}>
          📊 Analytics
        </div>
        <div className="absolute -bottom-1 -right-2 bg-rose-600/20 backdrop-blur-2xl text-rose-400 px-3 py-1 rounded-full text-xs font-semibold border border-rose-600/20 animate-float" style={{ animationDelay: '1.5s' }}>
          ✨ AI
        </div>
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-5px);
          }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}

export default function BlogPreview() {
  return (
    <div className="w-full h-full bg-gradient-to-br from-gray-900 via-pink-900 to-rose-900">
      <BlogWriterIcon />
    </div>
  )
} 