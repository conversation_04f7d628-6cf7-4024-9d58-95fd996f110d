#!/usr/bin/env node

/**
 * Test Caption Extraction with Specific Video
 */

import { YoutubeTranscript } from 'youtube-transcript';
import axios from 'axios';

console.log('🎯 Testing Caption Extraction with Specific Video');
console.log('=================================================');

// Let's test with a recent video that should have captions
async function testWithSearchedVideo() {
  console.log('🔍 Step 1: Finding a video with captions using YouTube API...');
  
  const apiKey = 'AIzaSyCHK5v6aCrIXQAakOtAbJjPA1MEpDWEEMo';
  
  try {
    // Search for educational videos that likely have captions
    const searchResponse = await axios.get('https://www.googleapis.com/youtube/v3/search', {
      params: {
        key: apiKey,
        q: 'python tutorial for beginners',
        part: 'snippet',
        type: 'video',
        maxResults: 5,
        videoCaption: 'closedCaption' // Only videos with captions
      }
    });

    console.log(`✅ Found ${searchResponse.data.items.length} videos with captions`);
    
    for (const item of searchResponse.data.items) {
      const videoId = item.id.videoId;
      const title = item.snippet.title;
      
      console.log(`\n🎥 Testing: ${title}`);
      console.log(`📺 Video ID: ${videoId}`);
      console.log(`🔗 URL: https://youtube.com/watch?v=${videoId}`);
      
      // Test Method 1: youtube-transcript library
      console.log('\n📝 Method 1: youtube-transcript library');
      try {
        const transcript = await YoutubeTranscript.fetchTranscript(videoId);
        console.log(`✅ Success! Extracted ${transcript.length} segments`);
        
        if (transcript.length > 0) {
          console.log('📋 First 3 segments:');
          transcript.slice(0, 3).forEach((segment, index) => {
            const minutes = Math.floor(segment.offset / 60);
            const seconds = Math.floor(segment.offset % 60);
            const timestamp = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            console.log(`   ${index + 1}. [${timestamp}] ${segment.text}`);
          });
          
          // We found a working video, let's test our custom method too
          await testCustomExtractionDetailed(videoId);
          return; // Exit after first successful extraction
        }
      } catch (error) {
        console.log(`❌ youtube-transcript failed: ${error.message}`);
      }
      
      // Test Method 2: Custom extraction
      console.log('\n🔧 Method 2: Custom extraction');
      const customSuccess = await testCustomExtractionDetailed(videoId);
      
      if (customSuccess) {
        return; // Exit after first successful extraction
      }
      
      console.log('\n' + '─'.repeat(50));
    }
    
  } catch (error) {
    console.error('❌ Search failed:', error.message);
  }
}

async function testCustomExtractionDetailed(videoId) {
  try {
    console.log(`🔧 Testing custom extraction for ${videoId}...`);
    
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
    const response = await axios.get(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      },
      timeout: 10000
    });

    const html = response.data;
    
    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?})\s*;\s*(?:var\s+(?:meta|head)|<\/script|\n)/);
    
    if (!playerResponseMatch) {
      console.log('❌ Could not find ytInitialPlayerResponse');
      return false;
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    
    // Check video info
    console.log(`📊 Video Title: ${playerResponse.videoDetails?.title || 'Unknown'}`);
    console.log(`⏱️  Duration: ${playerResponse.videoDetails?.lengthSeconds || 'Unknown'} seconds`);
    
    // Check for captions
    if (!playerResponse.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
      console.log('⚠️ No captions available');
      return false;
    }

    const tracks = playerResponse.captions.playerCaptionsTracklistRenderer.captionTracks;
    console.log(`✅ Found ${tracks.length} caption tracks:`);
    
    tracks.forEach((track, index) => {
      console.log(`   ${index + 1}. Language: ${track.languageCode || 'unknown'}, Kind: ${track.kind || 'manual'}, Name: ${track.name?.simpleText || 'N/A'}`);
    });

    // Try to get the best track (English manual first, then English auto, then any)
    let bestTrack = tracks.find(track => track.languageCode === 'en' && track.kind !== 'asr');
    if (!bestTrack) {
      bestTrack = tracks.find(track => track.languageCode === 'en');
    }
    if (!bestTrack) {
      bestTrack = tracks[0];
    }
    
    if (!bestTrack?.baseUrl) {
      console.log('❌ No suitable caption track found');
      return false;
    }

    console.log(`🎯 Using track: ${bestTrack.name?.simpleText || bestTrack.languageCode} (${bestTrack.kind || 'manual'})`);
    console.log('🌍 Fetching caption data...');
    
    const captionResponse = await axios.get(bestTrack.baseUrl + '&fmt=json3', {
      timeout: 10000
    });
    const captionData = captionResponse.data;

    if (!captionData.events) {
      console.log('❌ Invalid caption format');
      return false;
    }

    const captions = [];
    
    for (const event of captionData.events) {
      if (event.segs) {
        const text = event.segs
          .map(seg => seg.utf8 || '')
          .join(' ')
          .replace(/[\u200B-\u200D\uFEFF]/g, '') // Remove invisible chars
          .replace(/\s+/g, ' ') // Normalize whitespace
          .trim();

        if (text && text !== '' && !text.match(/^\s*$/)) {
          captions.push({
            text,
            start: parseFloat(event.tStartMs) / 1000 || 0,
            duration: parseFloat(event.dDurationMs) / 1000 || 2.0
          });
        }
      }
    }

    console.log(`✅ Custom extraction successful! Got ${captions.length} caption segments`);
    
    if (captions.length > 0) {
      console.log('📋 First 5 segments:');
      captions.slice(0, 5).forEach((caption, index) => {
        const minutes = Math.floor(caption.start / 60);
        const seconds = Math.floor(caption.start % 60);
        const timestamp = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        console.log(`   ${index + 1}. [${timestamp}] ${caption.text}`);
      });
      
      // Create full transcript
      const fullTranscript = captions.map(c => c.text).join(' ');
      console.log(`\n📄 Full transcript: ${fullTranscript.length} characters`);
      console.log(`📄 Preview: ${fullTranscript.substring(0, 200)}...`);
      
      return true;
    }
    
    return false;
    
  } catch (error) {
    console.log(`❌ Custom extraction failed: ${error.message}`);
    return false;
  }
}

// Test with some known educational channels that usually have captions
async function testWithKnownChannels() {
  console.log('\n🎓 Testing with known educational channels...');
  
  const knownVideos = [
    'W6NZfCO5SIk', // Programming with Mosh - JavaScript Course
    'EerdGm-ehJQ', // SuperSimpleDev - JavaScript Tutorial
    'PkZNo7MFNFg', // freeCodeCamp - Learn JavaScript
  ];
  
  for (const videoId of knownVideos) {
    console.log(`\n🎯 Testing video: ${videoId}`);
    
    // Try youtube-transcript first
    try {
      const transcript = await YoutubeTranscript.fetchTranscript(videoId);
      if (transcript && transcript.length > 0) {
        console.log(`✅ youtube-transcript: ${transcript.length} segments`);
        console.log(`📄 Sample: ${transcript[0].text}`);
        break; // Found working video
      }
    } catch (error) {
      console.log(`❌ youtube-transcript failed: ${error.message}`);
    }
    
    // Try custom extraction
    const customWorked = await testCustomExtractionDetailed(videoId);
    if (customWorked) break;
  }
}

// Run the tests
async function runFocusedTest() {
  try {
    await testWithSearchedVideo();
    await testWithKnownChannels();
    
    console.log('\n🎉 Focused caption extraction test completed!');
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

runFocusedTest(); 