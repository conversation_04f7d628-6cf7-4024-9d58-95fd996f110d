// Progress stream management for SSE
const progressStreams = new Map<string, ReadableStreamDefaultController>()

// Store a progress stream controller
export function storeProgressStream(progressId: string, controller: ReadableStreamDefaultController) {
  progressStreams.set(progressId, controller)
}

// Remove a progress stream controller
export function removeProgressStream(progressId: string) {
  progressStreams.delete(progressId)
}

// Helper function to send progress updates
export function sendProgressUpdate(progressId: string, data: any) {
  const controller = progressStreams.get(progressId)
  if (controller) {
    const encoder = new TextEncoder()
    try {
      controller.enqueue(
        encoder.encode(`data: ${JSON.stringify(data)}\n\n`)
      )
    } catch (error) {
      // Stream might be closed
      progressStreams.delete(progressId)
    }
  }
}

// Create a progress manager for a specific task
export function createProgressManager(progressId: string) {
  return {
    updateProgress: (progress: number, message: string, status?: string) => {
      sendProgressUpdate(progressId, {
        progress,
        message,
        status: status || 'processing',
        timestamp: new Date().toISOString()
      })
    },
    complete: (message: string) => {
      sendProgressUpdate(progressId, {
        progress: 100,
        message,
        status: 'completed',
        timestamp: new Date().toISOString()
      })
      removeProgressStream(progressId)
    },
    error: (message: string, error?: any) => {
      sendProgressUpdate(progressId, {
        progress: 100,
        message,
        status: 'error',
        error: error?.toString(),
        timestamp: new Date().toISOString()
      })
      removeProgressStream(progressId)
    }
  }
} 