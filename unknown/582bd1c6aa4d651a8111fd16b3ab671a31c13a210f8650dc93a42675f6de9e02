#!/usr/bin/env node

/**
 * Test script to demonstrate tech vs non-tech topic classification
 * Usage: npm run test:topics
 */

// Mock the topic classification logic from AayushAgent
function isTechRelatedTopic(topic, customInstructions = '') {
  const topicLower = topic.toLowerCase();
  const instructionsLower = customInstructions.toLowerCase();
  
  // Tech keywords that indicate a technical topic
  const techKeywords = [
    // Programming & Development
    'code', 'coding', 'programming', 'developer', 'software', 'app', 'api', 'framework',
    'javascript', 'python', 'react', 'node', 'typescript', 'css', 'html', 'php', 'java',
    'database', 'sql', 'mongodb', 'mysql', 'postgresql', 'redis', 'backend', 'frontend',
    
    // Tools & Platforms
    'github', 'git', 'vscode', 'cursor', 'ide', 'editor', 'terminal', 'cli', 'bash',
    'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'cloud', 'server', 'hosting',
    
    // AI & Machine Learning
    'ai', 'machine learning', 'ml', 'artificial intelligence', 'neural', 'gpt', 'llm',
    'openai', 'anthropic', 'claude', 'chatgpt', 'copilot', 'model', 'training',
    
    // Technology General
    'tech', 'technology', 'digital', 'cyber', 'internet', 'web', 'mobile', 'ios', 'android',
    'saas', 'paas', 'iaas', 'devops', 'ci/cd', 'automation', 'security', 'blockchain',
    
    // Hardware & Systems
    'computer', 'laptop', 'hardware', 'cpu', 'gpu', 'ram', 'ssd', 'motherboard',
    'network', 'router', 'wifi', 'ethernet', 'protocol', 'http', 'https', 'tcp', 'ip',
    'gaming laptop', 'gaming pc', 'workstation', 'server hardware', 'graphics card',
    
    // Marketing Tech & Tools
    'marketing tools', 'social media tools', 'automation tools', 'analytics tools',
    'crm software', 'email marketing platform', 'seo tools', 'content management system',
    
    // Data & Analytics
    'data', 'analytics', 'big data', 'visualization', 'dashboard', 'metrics', 'kpi'
  ];
  
  // Non-tech keywords that indicate a non-technical topic
  const nonTechKeywords = [
    // Lifestyle & Personal
    'cooking', 'recipe', 'food', 'restaurant', 'nutrition', 'diet', 'fitness', 'workout',
    'fashion', 'style', 'clothing', 'makeup', 'beauty', 'skincare', 'hair', 'lifestyle',
    'travel', 'vacation', 'hotel', 'destination', 'tourism', 'flight', 'booking',
    'health', 'wellness', 'meditation', 'yoga', 'mental health', 'therapy', 'medicine',
    
    // Entertainment & Media
    'movie', 'film', 'cinema', 'tv', 'television', 'show', 'series', 'netflix', 'streaming',
    'music', 'song', 'album', 'artist', 'concert', 'festival', 'spotify', 'podcast',
    'game', 'gaming', 'xbox', 'playstation', 'nintendo', 'mobile game', 'board game',
    
    // Business & Finance (non-tech)
    'marketing', 'advertising', 'branding', 'social media', 'content marketing', 'seo',
    'finance', 'investment', 'stock', 'trading', 'insurance', 'loan',
    'real estate', 'property', 'mortgage', 'rent', 'apartment', 'house', 'home',
    
    // Sports & Hobbies
    'sport', 'football', 'basketball', 'soccer', 'tennis', 'golf', 'baseball', 'hockey',
    'marathon', 'running', 'training', 'exercise', 'athlete', 'competition', 'race',
    'hobby', 'craft', 'art', 'painting', 'drawing', 'photography', 'gardening', 'pets'
  ];
  
  // Check if topic contains tech keywords
  const hasTechKeywords = techKeywords.some(keyword => 
    topicLower.includes(keyword) || instructionsLower.includes(keyword)
  );
  
  // Check if topic contains non-tech keywords
  const hasNonTechKeywords = nonTechKeywords.some(keyword => 
    topicLower.includes(keyword) || instructionsLower.includes(keyword)
  );
  
  // If both tech and non-tech keywords, prioritize the more specific match
  if (hasTechKeywords && hasNonTechKeywords) {
    const techMatches = techKeywords.filter(keyword => 
      topicLower.includes(keyword) || instructionsLower.includes(keyword)
    ).length;
    const nonTechMatches = nonTechKeywords.filter(keyword => 
      topicLower.includes(keyword) || instructionsLower.includes(keyword)
    ).length;
    
    return techMatches > nonTechMatches;
  }
  
  // If only one type of keywords found
  if (hasTechKeywords) return true;
  if (hasNonTechKeywords) return false;
  
  // Default assumption for ambiguous topics - check for common tech patterns
  const techPatterns = [
    /\b(alternatives?|vs|comparison)\b.*\b(tool|software|app|platform|service)\b/i,
    /\b(best|top)\b.*\b(tool|software|app|platform|service|solution)\b/i,
    /\b(how to)\b.*\b(code|program|develop|build|create|setup|install)\b/i,
    /\b(guide|tutorial)\b.*\b(programming|development|coding|technical)\b/i
  ];
  
  const matchesTechPattern = techPatterns.some(pattern => 
    pattern.test(topicLower) || pattern.test(instructionsLower)
  );
  
  return matchesTechPattern;
}

async function testTopicClassification() {
  console.log('🧪 Testing Tech vs Non-Tech Topic Classification');
  console.log('=' .repeat(60));
  
  // Test cases: [topic, expected, description]
  const testCases = [
    // Tech Topics (should get external links)
    ['Best Cursor alternatives for developers', true, 'Tech tool comparison'],
    ['React vs Vue comparison 2025', true, 'Programming framework comparison'],
    ['How to setup Docker on Ubuntu', true, 'Technical tutorial'],
    ['Top AI tools for content creation', true, 'AI/tech tools'],
    ['JavaScript frameworks for beginners', true, 'Programming topic'],
    ['Best code editors for Python', true, 'Development tools'],
    ['AWS vs Azure cloud comparison', true, 'Cloud platforms'],
    ['Node.js tutorial for beginners', true, 'Programming tutorial'],
    
    // Non-Tech Topics (should skip external links)
    ['Best Italian recipes for dinner', false, 'Cooking/food topic'],
    ['Top travel destinations in Europe', false, 'Travel topic'],
    ['How to lose weight fast', false, 'Health/fitness topic'],
    ['Best skincare routine for dry skin', false, 'Beauty topic'],
    ['Fashion trends for summer 2025', false, 'Fashion topic'],
    ['How to train for a marathon', false, 'Sports/fitness topic'],
    ['Best investment strategies for 2025', false, 'Finance topic'],
    ['Home decorating ideas on a budget', false, 'Lifestyle topic'],
    ['Top Netflix movies to watch', false, 'Entertainment topic'],
    ['How to care for indoor plants', false, 'Hobby/gardening topic'],
    
    // Edge Cases
    ['Social media marketing tools', true, 'Tech tools for marketing'],
    ['Digital marketing strategies', true, 'Digital/tech related'],
    ['Traditional marketing vs digital marketing', true, 'Tech comparison'],
    ['Content marketing best practices', false, 'Marketing strategy (non-tech)'],
    ['Gaming laptops under $1000', true, 'Tech hardware'],
    ['Board game recommendations', false, 'Non-digital gaming'],
  ];
  
  console.log('\n📊 Classification Results:');
  console.log('-'.repeat(80));
  
  let correctPredictions = 0;
  let totalTests = testCases.length;
  
  testCases.forEach(([topic, expected, description], index) => {
    const prediction = isTechRelatedTopic(topic);
    const correct = prediction === expected;
    const status = correct ? '✅' : '❌';
    const classification = prediction ? 'TECH' : 'NON-TECH';
    const externalLinks = prediction ? 'YES' : 'NO';
    
    console.log(`${status} ${(index + 1).toString().padStart(2)}: ${classification.padEnd(8)} | External Links: ${externalLinks.padEnd(3)} | ${topic}`);
    console.log(`    ${description}`);
    
    if (correct) correctPredictions++;
    
    if (!correct) {
      console.log(`    ⚠️  Expected: ${expected ? 'TECH' : 'NON-TECH'}, Got: ${classification}`);
    }
    
    console.log('');
  });
  
  console.log('-'.repeat(80));
  console.log(`📈 Accuracy: ${correctPredictions}/${totalTests} (${(correctPredictions/totalTests*100).toFixed(1)}%)`);
  
  console.log('\n🔗 External Linking Behavior:');
  console.log('   TECH Topics:     ✅ Include official documentation, GitHub repos, API docs');
  console.log('   NON-TECH Topics: 🚫 Skip external linking, focus on self-contained content');
  
  console.log('\n💡 Benefits:');
  console.log('   • Relevant external links only for technical content');
  console.log('   • Cleaner articles for lifestyle, health, travel topics');
  console.log('   • Better user experience with appropriate linking strategy');
  console.log('   • SEO optimization tailored to content type');
  
  console.log('\n🎯 Tech Topic Patterns Detected:');
  console.log('   • Programming languages and frameworks');
  console.log('   • Development tools and IDEs');
  console.log('   • Cloud platforms and services');
  console.log('   • AI and machine learning tools');
  console.log('   • Software comparisons and alternatives');
  console.log('   • Technical tutorials and guides');
  
  console.log('\n🚫 Non-Tech Topic Patterns Detected:');
  console.log('   • Cooking, recipes, and food');
  console.log('   • Travel and tourism');
  console.log('   • Health, fitness, and wellness');
  console.log('   • Fashion and beauty');
  console.log('   • Entertainment and media');
  console.log('   • Sports and hobbies');
  console.log('   • General lifestyle content');
  
  console.log('\n✅ Smart External Linking System Active!');
}

// Run the test
testTopicClassification().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
}); 