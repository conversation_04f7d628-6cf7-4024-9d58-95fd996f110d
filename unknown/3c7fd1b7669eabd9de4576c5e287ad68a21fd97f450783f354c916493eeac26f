#!/usr/bin/env node

// Test script to verify Tavily key rotation system
console.log('🔑 Testing Tavily API Key Rotation System');
console.log('=========================================');

// Import TavilyApiKeyRotator directly to test
import { TavilySearchService } from '../src/lib/search.ts';

async function testKeyRotation() {
  try {
    console.log('🧪 PHASE 1: Key Initialization Test');
    console.log('----------------------------------');
    
    const tavilyService = new TavilySearchService();
    const initialStatus = tavilyService.getKeyRotatorStatus();
    
    console.log(`🔑 Total API keys initialized: ${initialStatus.totalKeys}`);
    console.log(`🔑 Available keys: ${initialStatus.availableKeys}`);
    console.log(`🔑 Current key index: ${initialStatus.currentKeyIndex + 1}`);
    console.log('');
    
    // Show key health report
    console.log('🏥 KEY HEALTH REPORT:');
    console.log('---------------------');
    initialStatus.keyHealthReport.forEach(report => {
      console.log(`   ${report.keyId}: ${report.status} (${report.errors} errors)`);
    });
    console.log('');
    
    if (initialStatus.totalKeys === 1) {
      console.log('⚠️ WARNING: Only 1 API key detected!');
      console.log('   This means key rotation will not work properly.');
      console.log('   The system should have 9+ fallback keys available.');
      console.log('');
    } else {
      console.log('✅ GOOD: Multiple API keys detected for rotation');
      console.log('');
    }
    
    console.log('🧪 PHASE 2: Key Rotation Test');
    console.log('-----------------------------');
    
    // Get current key info
    const currentKeyInfo = tavilyService.getCurrentKeyInfo();
    console.log(`🔑 Starting with key: ${currentKeyInfo.key}`);
    
    // Force rotation to test mechanism
    console.log('🔄 Testing force rotation...');
    const newKey = tavilyService.forceKeyRotation();
    console.log(`🔄 After force rotation: ${newKey.slice(-4)}`);
    
    // Check status after rotation
    const afterRotationStatus = tavilyService.getKeyRotatorStatus();
    console.log(`🔑 Current key index after rotation: ${afterRotationStatus.currentKeyIndex + 1}`);
    console.log('');
    
    console.log('🧪 PHASE 3: Simulated Error Handling');
    console.log('-----------------------------------');
    
    // Test what happens when we simulate quota exceeded
    const testKey = tavilyService.getCurrentKeyInfo().key;
    console.log(`🧪 Simulating quota exceeded for key: ${testKey}`);
    
    // We can't directly access the keyRotator from the service, so we'll test through search
    try {
      // This will likely fail due to quota, but we want to see the rotation in action
      console.log('🔍 Attempting a small search to test real rotation...');
      await tavilyService.search('test query rotation', 1);
    } catch (error) {
      console.log(`❌ Search failed as expected: ${error.message}`);
      
      // Check if rotation occurred during the failed search
      const finalStatus = tavilyService.getKeyRotatorStatus();
      console.log(`🔑 Final key status: ${finalStatus.availableKeys}/${finalStatus.totalKeys} available`);
      
      if (finalStatus.totalKeys > 1) {
        console.log('✅ Key rotation system is properly configured');
      } else {
        console.log('❌ Key rotation system still shows only 1 key');
      }
    }
    
    console.log('');
    console.log('🧪 PHASE 4: Expected Behavior');
    console.log('-----------------------------');
    
    if (initialStatus.totalKeys >= 9) {
      console.log('✅ EXCELLENT: Full key rotation pool available');
      console.log('   - When one key hits quota limit, system will rotate to next');
      console.log('   - Should provide much more API quota for Stage 2 & 3');
      console.log('   - Each key resets after 24 hours automatically');
    } else if (initialStatus.totalKeys > 1) {
      console.log('⚠️ PARTIAL: Some key rotation available');
      console.log(`   - ${initialStatus.totalKeys} keys available for rotation`);
      console.log('   - Should provide more quota than single key');
    } else {
      console.log('❌ PROBLEM: No key rotation available');
      console.log('   - Only 1 key means no fallback when quota exceeded');
      console.log('   - Need to fix constructor to include fallback keys');
    }
    
    console.log('');
    console.log('🎯 TESTING RESULTS');
    console.log('==================');
    
    console.log(`Total Keys: ${initialStatus.totalKeys}`);
    console.log(`Available Keys: ${initialStatus.availableKeys}`);
    console.log(`Key Rotation: ${initialStatus.totalKeys > 1 ? 'WORKING' : 'NOT WORKING'}`);
    console.log(`Expected Quota: ${initialStatus.totalKeys > 1 ? 'MUCH HIGHER' : 'LIMITED'}`);
    
    if (initialStatus.totalKeys >= 9) {
      console.log('Status: ✅ FULLY OPERATIONAL');
      console.log('');
      console.log('🚀 READY FOR ENHANCED STAGE 2 TESTING');
      console.log('The AI agents should now be able to perform');
      console.log('extensive web research without hitting quota limits!');
    } else {
      console.log('Status: ⚠️ NEEDS INVESTIGATION');
    }
    
  } catch (error) {
    console.error('❌ Key rotation test failed:', error.message);
    console.error('This suggests there may be import or service issues.');
  }
}

// Run the test
testKeyRotation().catch(console.error); 