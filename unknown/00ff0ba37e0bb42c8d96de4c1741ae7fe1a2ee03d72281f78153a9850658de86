#!/usr/bin/env node

import { config } from 'dotenv'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Load environment variables
config({ path: join(__dirname, '..', '.env.local') })

console.log('🔍 Testing OAuth and Database Setup...\n')

// Test environment variables
const requiredEnvVars = [
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET', 
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL',
  'DATABASE_URL'
]

let allEnvVarsPresent = true

console.log('📋 Environment Variables Check:')
requiredEnvVars.forEach(envVar => {
  const value = process.env[envVar]
  if (value) {
    if (envVar.includes('SECRET') || envVar.includes('CLIENT_SECRET')) {
      console.log(`✅ ${envVar}: ${'*'.repeat(10)}...`)
    } else {
      console.log(`✅ ${envVar}: ${value}`)
    }
  } else {
    console.log(`❌ ${envVar}: Missing`)
    allEnvVarsPresent = false
  }
})

console.log('\n🔧 Google OAuth Configuration:')
const clientId = process.env.GOOGLE_CLIENT_ID
if (clientId) {
  console.log(`✅ Client ID format: ${clientId.includes('.apps.googleusercontent.com') ? 'Valid' : 'Invalid'}`)
} else {
  console.log('❌ Client ID: Missing')
}

const clientSecret = process.env.GOOGLE_CLIENT_SECRET
if (clientSecret) {
  console.log(`✅ Client Secret format: ${clientSecret.startsWith('GOCSPX-') ? 'Valid' : 'Invalid'}`)
} else {
  console.log('❌ Client Secret: Missing')
}

console.log('\n🌐 URLs Configuration:')
console.log(`✅ NextAuth URL: ${process.env.NEXTAUTH_URL}`)
console.log(`✅ Expected Redirect URI: ${process.env.NEXTAUTH_URL}/api/auth/callback/google`)

console.log('\n💾 Database Configuration:')
console.log(`✅ Database URL: ${process.env.DATABASE_URL}`)

if (allEnvVarsPresent) {
  console.log('\n🎉 Configuration Complete!')
  console.log('\n📝 Next Steps:')
  console.log('1. Make sure your Google Cloud Console OAuth client has these settings:')
  console.log(`   - Authorized JavaScript origins: ${process.env.NEXTAUTH_URL}`)
  console.log(`   - Authorized redirect URIs: ${process.env.NEXTAUTH_URL}/api/auth/callback/google`)
  console.log('\n2. Visit your login page:')
  console.log(`   ${process.env.NEXTAUTH_URL}/login`)
  console.log('\n3. Test Google OAuth by clicking "Continue with Google"')
} else {
  console.log('\n❌ Configuration Incomplete!')
  console.log('Please check the missing environment variables above.')
}

console.log('\n🔗 Useful URLs:')
console.log(`   - Login Page: ${process.env.NEXTAUTH_URL}/login`)
console.log(`   - Dashboard: ${process.env.NEXTAUTH_URL}/dashboard`)
console.log(`   - API Health: ${process.env.NEXTAUTH_URL}/api/auth/session`) 