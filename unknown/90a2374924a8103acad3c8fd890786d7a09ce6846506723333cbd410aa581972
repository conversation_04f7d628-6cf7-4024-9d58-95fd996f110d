/**
 * Knowledge Base Optimizer for YouTube Generation Workflow
 * Optimizes content generation by reusing previous analyses and learning from patterns
 */

import { KnowledgeBase, KnowledgeEntry } from './knowledge-base'

interface CachedAnalysis {
  videoId: string
  title: string
  analysis: string
  timestamp: number
  relevanceScore?: number
}

interface TopicProfile {
  topic: string
  commonThemes: string[]
  successfulPatterns: string[]
  contentGaps: string[]
  bestPractices: string[]
  lastUpdated: number
}

interface OptimizationMetrics {
  cachedAnalysesFound: number
  cachedCaptionsFound: number
  timeSaved: number
  apiCallsSaved: number
  newAnalysesCreated: number
}

export class KnowledgeBaseOptimizer {
  private static globalCache = new Map<string, KnowledgeBase>()
  private sessionCache: Map<string, any> = new Map()
  
  // Cache settings
  private readonly CACHE_DURATION = 7 * 24 * 60 * 60 * 1000 // 7 days
  private readonly RELEVANCE_THRESHOLD = 0.7
  
  /**
   * Get or create a global knowledge base for a topic
   */
  static getGlobalKnowledgeBase(topicKey: string): KnowledgeBase {
    const key = topicKey.toLowerCase().replace(/[^a-z0-9]/g, '_')
    if (!this.globalCache.has(key)) {
      this.globalCache.set(key, new KnowledgeBase(key))
    }
    return this.globalCache.get(key)!
  }
  
  /**
   * Find cached competitor analyses across all knowledge bases
   */
  static findCachedAnalyses(topic: string, limit: number = 5): CachedAnalysis[] {
    console.log(`🔍 Searching global cache for analyses on "${topic}"...`)
    
    const cachedAnalyses: CachedAnalysis[] = []
    const searchTerms = topic.toLowerCase().split(' ')
    
    // Search across all cached knowledge bases
    this.globalCache.forEach((kb, key) => {
      const competitive = kb.getEntriesByType('competitive')
      
      competitive.forEach(entry => {
        // Calculate relevance
        const relevance = this.calculateRelevance(searchTerms, entry)
        
        if (relevance >= 0.5) {
          const videoId = entry.url?.split('v=')[1] || ''
          cachedAnalyses.push({
            videoId,
            title: entry.title || '',
            analysis: entry.content,
            timestamp: entry.metadata?.timestamp || Date.now(),
            relevanceScore: relevance
          })
        }
      })
    })
    
    // Sort by relevance and recency
    cachedAnalyses.sort((a, b) => {
      const scoreA = (a.relevanceScore || 0) + (1 / (Date.now() - a.timestamp + 1))
      const scoreB = (b.relevanceScore || 0) + (1 / (Date.now() - b.timestamp + 1))
      return scoreB - scoreA
    })
    
    console.log(`📊 Found ${cachedAnalyses.length} relevant cached analyses`)
    return cachedAnalyses.slice(0, limit)
  }
  
  /**
   * Find cached captions across all knowledge bases
   */
  static findCachedCaptions(videoIds: string[]): Map<string, string> {
    console.log(`🔍 Checking global cache for captions of ${videoIds.length} videos...`)
    
    const cachedCaptions = new Map<string, string>()
    
    this.globalCache.forEach(kb => {
      const extracted = kb.getEntriesByType('extracted_content')
      
      extracted.forEach(entry => {
        const videoId = entry.url?.split('v=')[1]
        if (videoId && videoIds.includes(videoId)) {
          // Check if recent enough
          const age = Date.now() - (entry.metadata?.timestamp || 0)
          if (age < 7 * 24 * 60 * 60 * 1000) { // 7 days
            cachedCaptions.set(videoId, entry.content)
          }
        }
      })
    })
    
    console.log(`💾 Found ${cachedCaptions.size} cached captions`)
    return cachedCaptions
  }
  
  /**
   * Build topic profile from all related knowledge
   */
  static buildTopicProfile(topic: string): TopicProfile | null {
    console.log(`📈 Building topic profile for "${topic}"...`)
    
    const searchTerms = topic.toLowerCase().split(' ')
    const commonThemes = new Map<string, number>()
    const contentGaps = new Map<string, number>()
    const successfulPatterns: string[] = []
    const bestPractices: string[] = []
    
    // Analyze all knowledge bases
    this.globalCache.forEach(kb => {
      const allEntries = [
        ...kb.getEntriesByType('research'),
        ...kb.getEntriesByType('competitive'),
        ...kb.getEntriesByType('extracted_content')
      ]
      
      allEntries.forEach(entry => {
        const relevance = this.calculateRelevance(searchTerms, entry)
        
        if (relevance > 0.3) {
          // Extract themes
          entry.metadata?.keywords?.forEach(keyword => {
            commonThemes.set(keyword, (commonThemes.get(keyword) || 0) + 1)
          })
          
          // Extract gaps
          entry.metadata?.gaps?.forEach(gap => {
            contentGaps.set(gap, (contentGaps.get(gap) || 0) + 1)
          })
          
          // Extract successful patterns
          if (entry.metadata?.keyInsights) {
            successfulPatterns.push(...entry.metadata.keyInsights)
          }
          
          // Extract best practices
          entry.metadata?.statistics?.forEach(stat => {
            if (stat.includes('views') || stat.includes('engagement')) {
              bestPractices.push(stat)
            }
          })
        }
      })
    })
    
    if (commonThemes.size === 0) {
      console.log('📊 No historical data found for topic profile')
      return null
    }
    
    // Sort and select top items
    const sortedThemes = Array.from(commonThemes.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([theme]) => theme)
      .slice(0, 10)
    
    const sortedGaps = Array.from(contentGaps.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([gap]) => gap)
      .slice(0, 10)
    
    const profile: TopicProfile = {
      topic,
      commonThemes: sortedThemes,
      successfulPatterns: [...new Set(successfulPatterns)].slice(0, 10),
      contentGaps: sortedGaps,
      bestPractices: [...new Set(bestPractices)].slice(0, 5),
      lastUpdated: Date.now()
    }
    
    console.log(`📊 Topic profile created with ${profile.commonThemes.length} themes, ${profile.contentGaps.length} gaps`)
    return profile
  }
  
  /**
   * Find successful script patterns
   */
  static findSuccessfulPatterns(style: string, duration: string): string[] {
    console.log(`🎯 Finding successful patterns for ${style} style, ${duration} duration...`)
    
    const patterns: string[] = []
    
    this.globalCache.forEach(kb => {
      const scripts = kb.searchContent('Final Script')
      
      scripts.forEach(entry => {
        if (entry.metadata?.keywords?.includes(style) || 
            entry.metadata?.keywords?.includes(duration)) {
          // Extract opening hooks
          const matches = entry.content.match(/\[([\d:]+)\]\s*([^[]+)/g)
          if (matches) {
            const hooks = matches.slice(0, 3).map(m => m.replace(/\[[\d:]+\]\s*/, ''))
            patterns.push(...hooks)
          }
        }
      })
    })
    
    console.log(`✨ Found ${patterns.length} successful patterns`)
    return [...new Set(patterns)].slice(0, 5)
  }
  
  /**
   * Calculate relevance score
   */
  private static calculateRelevance(searchTerms: string[], entry: KnowledgeEntry): number {
    let score = 0
    const titleWords = (entry.title || '').toLowerCase().split(' ')
    const contentPreview = entry.content.substring(0, 500).toLowerCase()
    
    searchTerms.forEach(term => {
      // Title match (highest weight)
      if (titleWords.includes(term)) score += 0.3
      
      // Content match
      if (contentPreview.includes(term)) score += 0.1
      
      // Keyword match
      if (entry.metadata?.keywords?.some(k => k.toLowerCase().includes(term))) {
        score += 0.2
      }
    })
    
    // Recency bonus
    const ageInDays = (Date.now() - (entry.metadata?.timestamp || 0)) / (24 * 60 * 60 * 1000)
    if (ageInDays < 1) score += 0.2
    else if (ageInDays < 3) score += 0.1
    else if (ageInDays < 7) score += 0.05
    
    return Math.min(score, 1.0)
  }
  
  /**
   * Get optimization recommendations
   */
  static getOptimizationRecommendations(topic: string): string[] {
    console.log(`💡 Generating optimization recommendations for "${topic}"...`)
    
    const recommendations: string[] = []
    
    // Check cached analyses
    const cachedAnalyses = this.findCachedAnalyses(topic, 3)
    if (cachedAnalyses.length > 0) {
      recommendations.push(
        `🔄 Found ${cachedAnalyses.length} cached competitor analyses - reuse to save API calls`
      )
    }
    
    // Check topic profile
    const profile = this.buildTopicProfile(topic)
    if (profile) {
      if (profile.contentGaps.length > 0) {
        recommendations.push(
          `📊 Common content gaps: ${profile.contentGaps.slice(0, 3).join(', ')}`
        )
      }
      
      if (profile.bestPractices.length > 0) {
        recommendations.push(
          `✨ Best practice: ${profile.bestPractices[0]}`
        )
      }
      
      if (profile.commonThemes.length > 0) {
        recommendations.push(
          `🎯 Focus on themes: ${profile.commonThemes.slice(0, 3).join(', ')}`
        )
      }
    }
    
    // Check patterns
    const patterns = this.findSuccessfulPatterns('educational', '5-10 minutes')
    if (patterns.length > 0) {
      recommendations.push(
        `💡 Successful hook: "${patterns[0].substring(0, 50)}..."`
      )
    }
    
    console.log(`💡 Generated ${recommendations.length} recommendations`)
    return recommendations
  }
  
  /**
   * Track optimization metrics
   */
  static trackMetrics(sessionId: string, metrics: OptimizationMetrics): void {
    const kb = this.getGlobalKnowledgeBase('_optimization_metrics')
    
    kb.addEntry({
      type: 'research',
      title: `Optimization Metrics: ${sessionId}`,
      content: JSON.stringify(metrics, null, 2),
      metadata: {
        source: 'workflow_optimization',
        timestamp: Date.now(),
        statistics: [
          `${metrics.cachedAnalysesFound} cached analyses found`,
          `${metrics.cachedCaptionsFound} cached captions found`,
          `${metrics.apiCallsSaved} API calls saved`,
          `${metrics.timeSaved}s time saved`
        ],
        keywords: ['optimization', 'metrics', 'performance']
      }
    })
    
    console.log(`📊 Tracked optimization metrics: ${metrics.apiCallsSaved} API calls saved`)
  }
  
  /**
   * Clear old cache entries
   */
  static cleanupCache(maxAge: number = 30 * 24 * 60 * 60 * 1000): void {
    console.log('🧹 Cleaning up old cache entries...')
    
    let totalRemoved = 0
    
    this.globalCache.forEach((kb, key) => {
      const allEntries = [
        ...kb.getEntriesByType('research'),
        ...kb.getEntriesByType('competitive'),
        ...kb.getEntriesByType('extracted_content'),
        ...kb.getEntriesByType('writing_style')
      ]
      
      allEntries.forEach(entry => {
        const age = Date.now() - (entry.metadata?.timestamp || 0)
        if (age > maxAge) {
          kb.removeEntry(entry.id)
          totalRemoved++
        }
      })
      
      // Remove empty knowledge bases
      if (kb.getSize() === 0) {
        this.globalCache.delete(key)
      }
    })
    
    console.log(`🧹 Removed ${totalRemoved} old entries`)
  }

  /**
   * Get global cache size for debugging
   */
  static getGlobalCacheSize(): number {
    return this.globalCache.size
  }

  /**
   * Clear entire global cache (for debugging or cleanup)
   */
  static clearGlobalCache(): void {
    console.log('🧹 Clearing entire global knowledge base cache...')
    const entriesCleared = this.globalCache.size
    this.globalCache.clear()
    console.log(`🧹 Cleared ${entriesCleared} global cache entries`)
  }
}

/**
 * Intelligent caching strategy for YouTube workflow
 */
export class SmartCache {
  private static instance: SmartCache
  private cache: Map<string, { data: any, timestamp: number }> = new Map()
  private readonly TTL = 60 * 60 * 1000 // 1 hour
  
  static getInstance(): SmartCache {
    if (!SmartCache.instance) {
      SmartCache.instance = new SmartCache()
    }
    return SmartCache.instance
  }
  
  set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
  
  get(key: string): any | null {
    const cached = this.cache.get(key)
    if (!cached) return null
    
    if (Date.now() - cached.timestamp > this.TTL) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data
  }
  
  clear(): void {
    this.cache.clear()
  }
} 