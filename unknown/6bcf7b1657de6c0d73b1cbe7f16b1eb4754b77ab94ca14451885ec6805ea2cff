#!/usr/bin/env node

/**
 * Quick Cleanup Script
 * Clears any existing problematic state before testing fixes
 */

console.log('🧹 Quick Cleanup for Agent Hanging Fixes\n');

// Force garbage collection if available
if (global.gc) {
  console.log('🗑️ Running garbage collection...');
  global.gc();
  console.log('✅ Garbage collection completed');
} else {
  console.log('ℹ️ Garbage collection not available (run with --expose-gc if needed)');
}

// Clear any lingering timers (this is more of a safety measure)
console.log('⏰ Clearing any lingering timers...');

// In a real cleanup, we'd need access to the actual timer IDs
// But this is mainly to reset the environment
console.log('✅ Environment reset');

console.log('\n💡 Cleanup completed! You can now test the agent fixes.');
console.log('\n🧪 To test the fixes:');
console.log('1. Start your dev server: npm run dev');
console.log('2. Run the test script: node scripts/test-agent-hanging-fix.mjs');
console.log('3. Or test manually through the web interface');

console.log('\n✅ The agent should now run multiple times without hanging!'); 