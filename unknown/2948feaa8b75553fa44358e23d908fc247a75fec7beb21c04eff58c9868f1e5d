'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Save, 
  FileText, 
  Download, 
  Upload,
  Settings,
  Home,
  Brain,
  Sparkles,
  PenTool,
  Eye,
  EyeOff,
  Palette,
  Type,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Maximize,
  Minimize
} from 'lucide-react'
import Link from 'next/link'
import RichTextEditor from '@/components/editor/RichTextEditor'

interface Document {
  id: string
  title: string
  content: string
  createdAt: Date
  updatedAt: Date
}

export default function EditorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [content, setContent] = useState('')
  const [title, setTitle] = useState('Untitled Document')
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [savedDocuments, setSavedDocuments] = useState<Document[]>([])
  const [showDocuments, setShowDocuments] = useState(false)
  const [wordCount, setWordCount] = useState(0)
  const [characterCount, setCharacterCount] = useState(0)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading editor...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  // Auto-save functionality
  useEffect(() => {
    const timer = setTimeout(() => {
      if (content && title) {
        handleAutoSave()
      }
    }, 2000)

    return () => clearTimeout(timer)
  }, [content, title])

  // Update word and character count
  useEffect(() => {
    const text = content.replace(/<[^>]*>/g, '').trim()
    setWordCount(text.split(/\s+/).filter(word => word.length > 0).length)
    setCharacterCount(text.length)
  }, [content])

  const handleAutoSave = async () => {
    setIsSaving(true)
    
    // Simulate auto-save (in real app, save to database)
    await new Promise(resolve => setTimeout(resolve, 500))
    
    setIsSaving(false)
  }

  const handleSave = async () => {
    setIsSaving(true)
    
    const document: Document = {
      id: Date.now().toString(),
      title,
      content,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    // Simulate saving to database
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setSavedDocuments(prev => [document, ...prev])
    setIsSaving(false)
  }

  const handleLoad = (document: Document) => {
    setTitle(document.title)
    setContent(document.content)
    setShowDocuments(false)
  }

  const handleExport = () => {
    const blob = new Blob([content], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Navigation helper for generator pages
  const openGenerator = (type: string) => {
    window.open(`/generate/${type}`, '_blank')
  }

  return (
    <div className={`min-h-screen transition-all duration-300 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Header */}
      {!isFullscreen && (
        <div className="border-b border-white/10 bg-black/20 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Link href="/">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center space-x-2 text-white hover:text-blue-400 transition-colors"
                  >
                    <Home className="w-5 h-5" />
                    <span className="font-semibold">Invincible</span>
                  </motion.button>
                </Link>
                
                <div className="text-gray-400">|</div>
                
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="bg-transparent text-white font-medium text-lg focus:outline-none focus:text-blue-400 transition-colors"
                  placeholder="Document title..."
                />
                
                {isSaving && (
                  <div className="flex items-center text-green-400 text-sm">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                    Saving...
                  </div>
                )}
              </div>
              
              <div className="flex items-center space-x-2">
                {/* AI Generation Buttons */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={() => openGenerator('blog')}
                  className="flex items-center space-x-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                  title="Open Blog Generator"
                >
                  <PenTool className="w-4 h-4" />
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={() => openGenerator('email')}
                  className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  title="Open Email Generator"
                >
                  <Sparkles className="w-4 h-4" />
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={() => openGenerator('youtube')}
                  className="flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                  title="Open Video Script Generator"
                >
                  <Brain className="w-4 h-4" />
                </motion.button>

                <div className="w-px h-6 bg-white/20"></div>

                {/* Editor Controls */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={() => setIsPreviewMode(!isPreviewMode)}
                  className={`p-2 rounded-lg transition-colors ${
                    isPreviewMode ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                  title={isPreviewMode ? 'Exit Preview' : 'Preview'}
                >
                  {isPreviewMode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="p-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                  title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
                >
                  {isFullscreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
                </motion.button>

                <div className="w-px h-6 bg-white/20"></div>

                {/* File Operations */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={() => setShowDocuments(!showDocuments)}
                  className="flex items-center space-x-2 px-3 py-2 glass-card text-white hover:bg-white/20 rounded-lg transition-colors"
                >
                  <FileText className="w-4 h-4" />
                  <span>Open</span>
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={handleSave}
                  disabled={isSaving}
                  className="flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  <Save className="w-4 h-4" />
                  <span>Save</span>
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={handleExport}
                  className="p-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                  title="Export"
                >
                  <Download className="w-4 h-4" />
                </motion.button>
                
                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    className="p-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                    title="Dashboard"
                  >
                    <Settings className="w-4 h-4" />
                  </motion.button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex h-full">
        {/* Documents Sidebar */}
        <AnimatePresence>
          {showDocuments && !isFullscreen && (
            <motion.div
              initial={{ x: -300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              className="w-80 bg-black/30 backdrop-blur-sm border-r border-white/10 p-4"
            >
              <h3 className="text-lg font-semibold text-white mb-4">Recent Documents</h3>
              
              {savedDocuments.length === 0 ? (
                <p className="text-gray-400 text-sm">No saved documents yet</p>
              ) : (
                <div className="space-y-2">
                  {savedDocuments.map((doc) => (
                    <motion.div
                      key={doc.id}
                      whileHover={{ scale: 1.02 }}
                      onClick={() => handleLoad(doc)}
                      className="glass-card p-3 cursor-pointer hover:bg-white/20 transition-colors"
                    >
                      <h4 className="text-white font-medium truncate">{doc.title}</h4>
                      <p className="text-gray-400 text-xs">
                        {doc.createdAt.toLocaleDateString()}
                      </p>
                    </motion.div>
                  ))}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Editor */}
        <div className="flex-1 flex flex-col">
          <div className={`flex-1 p-8 ${isFullscreen ? 'h-screen' : ''}`}>
            {isPreviewMode ? (
              <div className="glass-card p-8 max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold text-white mb-6">{title}</h1>
                <div 
                  className="prose prose-invert max-w-none"
                  dangerouslySetInnerHTML={{ __html: content }}
                />
              </div>
            ) : (
              <div className="max-w-6xl mx-auto">
                <RichTextEditor
                  content={content}
                  onChange={setContent}
                  placeholder="Start writing your masterpiece..."
                  className="min-h-[600px]"
                />
              </div>
            )}
          </div>

          {/* Status Bar */}
          {!isFullscreen && (
            <div className="border-t border-white/10 bg-black/20 backdrop-blur-sm px-8 py-3">
              <div className="flex items-center justify-between text-sm text-gray-400">
                <div className="flex items-center space-x-6">
                  <span>{wordCount} words</span>
                  <span>{characterCount} characters</span>
                  <span>Last saved: {isSaving ? 'Saving...' : 'Just now'}</span>
                </div>
                
                <div className="flex items-center space-x-4">
                  <span>Ready</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 