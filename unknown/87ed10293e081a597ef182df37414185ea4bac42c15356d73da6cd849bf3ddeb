import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth(
  function middleware(req) {
    // Add any additional middleware logic here
    const { pathname } = req.nextUrl
    
    // Don't redirect API routes - let them handle their own authentication
    if (pathname.startsWith('/api/')) {
      return NextResponse.next()
    }
    
    // If user is not authenticated and trying to access protected pages (not API)
    if (!req.nextauth.token) {
      // Redirect to login page for protected pages
      const loginUrl = new URL('/login', req.url)
      return NextResponse.redirect(loginUrl)
    }
    
    // Allow authenticated users to proceed
    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl
        
        // Always allow access to API routes (they handle their own auth)
        if (pathname.startsWith('/api/')) {
          return true
        }
        
        // Always allow access to public pages
        const publicPages = [
          '/',
          '/login',
          '/auth/signin',
          '/auth/error'
        ]
        
        // Check if the current path is a public page
        const isPublicPage = publicPages.some(page => pathname === page)
        
        if (isPublicPage) {
          return true
        }
        
        // For all other pages, require authentication
        return !!token
      },
    },
    pages: {
      signIn: '/login',
      error: '/login',
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ]
} 