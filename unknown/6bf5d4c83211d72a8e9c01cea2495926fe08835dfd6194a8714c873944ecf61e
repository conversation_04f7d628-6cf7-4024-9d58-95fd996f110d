export interface AgentConfig {
  temperature?: number;
  maxTokens?: number;
  retryAttempts?: number;
  debugMode?: boolean;
}

export interface TaskContext {
  taskId: string;
  topic: string;
  customInstructions?: string;
  targetAudience?: string;
  contentLength?: number;
  tone?: string;
  keywords?: string[];
  competitorUrls?: string[];
  seoStrategy?: SEOStrategy;
  contentType?: string;
  targetLocation?: string;
  nicheStrategy?: any; // NicheSearchStrategy type from niche-intelligence
  nicheProfile?: any; // NicheProfile type from niche-intelligence
  nicheId?: string;
  nicheIntelligence?: any; // NicheIntelligenceService type from niche-intelligence
}

export interface SEOStrategy {
  primaryKeyword: string;
  secondaryKeywords: string[];
  searchIntent: 'informational' | 'navigational' | 'transactional' | 'commercial';
  contentGaps: string[];
  competitorAnalysis: CompetitorAnalysis[];
}

export interface CompetitorAnalysis {
  url: string;
  title: string;
  wordCount: number;
  mainTopics: string[];
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
}

export interface ResearchData {
  sources: ResearchSource[];
  keyInsights: string[];
  statisticsAndData?: StatisticalData[];
  statistics?: string[];
  expertQuotes?: ExpertQuote[];
  trends: string[];
  competitorUrls?: string[];
  researchQuality: number;
  searchQueries?: string[];
  totalSources: number;
  orchestratorRecommendations?: string[];
  competitiveAdvantages?: string[];
  researchDepth?: string;
  lastUpdated?: number;
}

export interface ResearchSource {
  url: string;
  title: string;
  content: string;
  relevanceScore: number;
  lastUpdated?: string;
  authority?: number;
  authorityScore?: number;
  contentDepth?: number;
  publicationType?: string;
  keyInsights?: string[];
  statisticsData?: string[];
  actionableAdvice?: string[];
  uniquePerspectives?: string[];
  contentGaps?: string[];
  credibilityIndicators?: string[];
  originalQuery?: string;
  researchValue?: number;
  researchIntent?: string;
}

export interface StatisticalData {
  statistic: string;
  source: string;
  context?: string;
  year?: number;
}

export interface ExpertQuote {
  quote: string;
  author: string;
  credentials: string;
  source: string;
}

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  query: string;
  relevanceScore: number;
}

export interface ContentStrategy {
  outline: ContentOutline;
  seoOptimization: SEOOptimization;
  competitiveEdge: string[];
  humanizationTactics: string[];
  engagementStrategies: string[];
  callToActions: string[];
}

export interface ContentOutline {
  title: string;
  metaDescription: string;
  introduction: OutlineSection;
  mainSections: OutlineSection[];
  conclusion?: OutlineSection;
  estimatedWordCount: number;
}

export interface OutlineSection {
  heading: string;
  subheadings?: string[];
  keyPoints: string[];
  targetWordCount: number;
  seoKeywords: string[];
}

export interface SEOOptimization {
  titleTags: string[];
  metaDescriptions: string[];
  headerOptimization: HeaderOptimization;
  keywordDensity: KeywordDensity;
  internalLinkingStrategy: InternalLinkStrategy[];
  featuredSnippetOptimization: string[];
}

export interface HeaderOptimization {
  h1: string;
  h2: string[];
  h3: string[];
}

export interface KeywordDensity {
  primary: { keyword: string; targetDensity: number };
  secondary: { keyword: string; targetDensity: number }[];
  lsi: string[];
}

export interface InternalLinkStrategy {
  anchorText: string;
  targetUrl: string;
  context: string;
}

export interface GeneratedContent {
  title: string;
  content: string;
  metaDescription: string;
  wordCount: number;
  readabilityScore?: number;
  seoScore?: number;
  keywordUsage: KeywordUsage[];
  suggestions: ContentSuggestion[];
}

export interface KeywordUsage {
  keyword: string;
  frequency: number;
  density: number;
  positions: number[];
}

export interface ContentSuggestion {
  type: 'improvement' | 'addition' | 'optimization';
  section: string;
  suggestion: string;
  priority: 'high' | 'medium' | 'low';
}

export interface QualityAssessment {
  overallScore: number;
  criteria: QualityCriteria[];
  improvements: ImprovementSuggestion[];
  approved: boolean;
  feedback: string;
}

export interface QualityCriteria {
  name: string;
  score: number;
  maxScore: number;
  feedback: string;
}

export interface ImprovementSuggestion {
  section: string;
  issue: string;
  suggestion: string;
  priority: 'high' | 'medium' | 'low';
}

export interface OrchestratorState {
  currentPhase: 'research' | 'strategy' | 'deepResearch' | 'writing' | 'quality' | 'complete';
  taskContext: TaskContext;
  researchData?: ResearchData;
  contentStrategy?: ContentStrategy;
  generatedContent?: GeneratedContent;
  qualityAssessment?: QualityAssessment;
  iterations: number;
  maxIterations: number;
  startTime: number;
  logs: OrchestratorLog[];
}

export interface OrchestratorLog {
  timestamp: number;
  phase: string;
  component: string;
  action: string;
  result: 'success' | 'error' | 'retry';
  details: string;
  duration?: number;
}

export interface AgentResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  logs?: string[];
  executionTime?: number;
}

export interface ComponentInterface {
  execute(context: TaskContext, state: OrchestratorState): Promise<AgentResponse>;
  validateInput(context: TaskContext): boolean;
  getName(): string;
  getDescription(): string;
} 