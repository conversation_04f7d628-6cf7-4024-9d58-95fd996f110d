import { NextRequest } from 'next/server'
import { storeProgressStream, removeProgressStream } from '@/lib/progress-manager'

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ progressId: string }> }
) {
  const { progressId } = await context.params

  const stream = new ReadableStream({
    start(controller) {
      // Store the controller for this progress ID
      storeProgressStream(progressId, controller)

      // Send initial connection message
      const encoder = new TextEncoder()
      controller.enqueue(
        encoder.encode(`data: ${JSON.stringify({ 
          type: 'connected', 
          progressId 
        })}\n\n`)
      )

      // Clean up on close
      request.signal.addEventListener('abort', () => {
        removeProgressStream(progressId)
        controller.close()
      })
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  })
} 