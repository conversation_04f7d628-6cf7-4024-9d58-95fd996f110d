# Settings System Documentation

## Overview

The INVINCIBLE application features a comprehensive, robust settings system that manages user preferences, theme customization, and application behavior. The system is built with React Context, TypeScript, and integrates seamlessly throughout the entire application.

## Architecture

### Core Components

1. **SettingsContext** (`src/contexts/SettingsContext.tsx`)
   - Manages user settings state
   - Handles localStorage persistence
   - Provides settings CRUD operations
   - Includes error handling and loading states

2. **ThemeContext** (`src/contexts/ThemeContext.tsx`)
   - Manages theme and appearance settings
   - Applies CSS variables dynamically
   - Handles system theme detection
   - Supports real-time theme switching

3. **Settings Page** (`src/app/settings/page.tsx`)
   - User-friendly settings interface
   - Tabbed navigation for different categories
   - Real-time updates and validation
   - Save/reset functionality

4. **API Routes** (`src/app/api/settings/route.ts`)
   - Backend settings management
   - Validation and sanitization
   - Error handling and security

5. **Middleware** (`src/middleware/settings.ts`)
   - Settings validation utilities
   - Rate limiting and security
   - Audit logging capabilities

## Settings Categories

### 1. Profile Settings
- **First Name**: User's first name
- **Last Name**: User's last name
- **Email**: User's email address
- **Bio**: Personal description (max 500 characters)
- **Avatar**: Profile picture (placeholder for future implementation)

### 2. Preferences
- **Language**: Interface language (8 supported languages)
- **Timezone**: User's timezone for date/time display
- **Auto-save**: Automatic content saving preference

### 3. Content Preferences
- **Default Word Count**: Default word count for content generation (100-10,000)
- **Default Tone**: Preferred writing tone (6 options)
- **Include Research**: Default AI research inclusion setting

### 4. Notifications
- **Email Notifications**: Important account updates
- **Push Notifications**: Browser notifications
- **Weekly Reports**: Usage summaries
- **Marketing Emails**: Promotional content

### 5. Appearance & Display
- **Theme**: Dark, Light, or Auto (system)
- **Accent Color**: 5 professional color options (Professional Blue, Executive Purple, Corporate Green, Business Red, Enterprise Orange)
- **Animations**: Enable/disable UI animations
- **Compact Mode**: Condensed interface layout

### 6. Privacy & Data
- **Profile Visibility**: Private, Team, or Public
- **Data Sharing**: Anonymous usage data consent
- **Analytics Tracking**: Usage pattern analytics

## Usage Examples

### Basic Settings Access

```typescript
import { useSettings } from '@/contexts/SettingsContext'

function MyComponent() {
  const { settings, updateSettings } = useSettings()

  return (
    <div>
      <h1>Welcome, {settings.firstName}!</h1>
      <button onClick={() => updateSettings({ firstName: 'New Name' })}>
        Update Name
      </button>
    </div>
  )
}
```

### Theme Integration

```typescript
import { useTheme } from '@/contexts/ThemeContext'

function ThemeToggle() {
  const { theme, setTheme } = useTheme()

  return (
    <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
      Switch to {theme === 'dark' ? 'Light' : 'Dark'} Mode
    </button>
  )
}
```

### User Preferences Hook

```typescript
import { useUserPreferences } from '@/hooks/useUserPreferences'

function BlogForm() {
  const { contentDefaults, profile } = useUserPreferences()

  const [formData, setFormData] = useState({
    wordCount: contentDefaults.wordCount,
    tone: contentDefaults.tone,
    includeResearch: contentDefaults.includeResearch
  })

  return (
    <form>
      <h2>Hello, {profile.firstName}!</h2>
      {/* Form fields using default values */}
    </form>
  )
}
```

## Integration Points

### 1. Dashboard Layout
- User name display in sidebar
- Theme application throughout interface
- Compact mode styling

### 2. Blog Generator
- Default word count from settings
- Default tone selection
- Research inclusion preference

### 3. Global Styling
- CSS variables for theme colors
- Animation duration controls
- Compact mode spacing adjustments

### 4. Form Components
- Auto-save behavior based on user preference
- Language-specific placeholders
- Theme-aware styling

## API Endpoints

### GET /api/settings
Retrieves user settings from the backend.

**Response:**
```json
{
  "success": true,
  "settings": {
    "firstName": "John",
    "lastName": "Doe",
    // ... other settings
  }
}
```

### POST /api/settings
Saves user settings to the backend.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  // ... other settings
}
```

**Response:**
```json
{
  "success": true,
  "message": "Settings saved successfully"
}
```

### PUT /api/settings
Performs settings operations like reset.

**Request Body:**
```json
{
  "action": "reset"
}
```

## Security Features

### 1. Validation
- Client-side and server-side validation
- Type checking with TypeScript
- Sanitization of user inputs

### 2. Rate Limiting
- 10 requests per minute per IP
- Prevents abuse of settings endpoints

### 3. Audit Logging
- All settings changes are logged
- Includes user ID, timestamp, and changes

### 4. Data Protection
- Sensitive data encryption (placeholder)
- Secure headers on API responses
- XSS and CSRF protection

## Persistence Strategy

### 1. Local Storage
- Immediate persistence for offline access
- Fallback when API is unavailable
- Automatic synchronization on load

### 2. Backend Database
- Permanent storage across devices
- User authentication integration
- Backup and restore capabilities

### 3. Synchronization
- Local changes sync to backend
- Conflict resolution strategies
- Offline-first approach

## Customization Guide

### Adding New Settings

1. **Update TypeScript Interface**
```typescript
// In SettingsContext.tsx
export interface UserSettings {
  // ... existing settings
  newSetting: string
}
```

2. **Update Default Values**
```typescript
const defaultSettings: UserSettings = {
  // ... existing defaults
  newSetting: 'default value'
}
```

3. **Add to Settings Page**
```typescript
<ModernInput
  label="New Setting"
  value={settings.newSetting}
  onChange={(value) => updateSettings({ newSetting: value as string })}
/>
```

4. **Update API Validation**
```typescript
// In api/settings/route.ts
if (!validNewSettingValues.includes(settings.newSetting)) {
  return NextResponse.json(
    { success: false, error: 'Invalid new setting value' },
    { status: 400 }
  )
}
```

### Theme Customization

1. **Add New Accent Color**
```typescript
// In ThemeContext.tsx
const accentColors = {
  blue: { primary: '#2563eb', secondary: '#1e40af', light: '#60a5fa' },
  purple: { primary: '#7c3aed', secondary: '#6d28d9', light: '#a78bfa' },
  green: { primary: '#059669', secondary: '#047857', light: '#34d399' },
  red: { primary: '#e11d48', secondary: '#be185d', light: '#fb7185' },
  orange: { primary: '#d97706', secondary: '#b45309', light: '#fbbf24' },
  // Add new professional color
  newColor: {
    primary: '#hex-value',
    secondary: '#hex-value',
    light: '#hex-value'
  }
}
```

2. **Update CSS Variables**
```css
/* In globals.css */
:root {
  --new-color-primary: #hex-value;
  --new-color-secondary: #hex-value;
}
```

## Best Practices

### 1. Performance
- Use React.memo for settings-dependent components
- Debounce settings updates to prevent excessive API calls
- Lazy load settings that aren't immediately needed

### 2. User Experience
- Provide immediate visual feedback for changes
- Show loading states during save operations
- Implement optimistic updates for better responsiveness

### 3. Error Handling
- Graceful degradation when settings fail to load
- Clear error messages for validation failures
- Retry mechanisms for network failures

### 4. Accessibility
- Proper ARIA labels for all form controls
- Keyboard navigation support
- Screen reader compatibility

## Testing Strategy

### 1. Unit Tests
- Settings context functionality
- Validation logic
- Theme switching behavior

### 2. Integration Tests
- Settings page interactions
- API endpoint responses
- Cross-component settings usage

### 3. E2E Tests
- Complete settings workflow
- Theme persistence across sessions
- Settings synchronization

## Future Enhancements

### 1. Advanced Features
- Settings import/export
- Multiple user profiles
- Team settings management
- Settings versioning and rollback

### 2. Performance Optimizations
- Settings caching strategies
- Incremental updates
- Background synchronization

### 3. Security Improvements
- Two-factor authentication for sensitive changes
- Settings change notifications
- Advanced audit logging

This settings system provides a solid foundation for user customization while maintaining security, performance, and user experience standards.
