'use client'

import { useRef, useState, useEffect } from 'react'

// Social Media Feed Animation
function SocialMediaIcon() {
  const [posts, setPosts] = useState<Array<{ id: number; content: string; likes: number; shares: number; comments: number; platform: string; isLiking: boolean; isTrending: boolean }>>([])
  const [newPostAnimation, setNewPostAnimation] = useState(false)
  const [totalReach, setTotalReach] = useState(15420)
  const [engagementRate, setEngagementRate] = useState(4.8)
  const [growthRate, setGrowthRate] = useState(12.5)
  const [notifications, setNotifications] = useState(0)
  const [isTyping, setIsTyping] = useState(false)
  const [currentTypingText, setCurrentTypingText] = useState('')
  const [showLiveIndicator, setShowLiveIndicator] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const typingRef = useRef<NodeJS.Timeout | null>(null)

  const postContents = [
    "🚀 Just launched our new AI-powered content creation tool! The future of marketing is here.",
    "💡 5 game-changing AI strategies that transformed our content workflow. Thread below 👇",
    "📊 Our latest campaign achieved 340% ROI using AI-generated content. Here's how we did it...",
    "✨ Behind the scenes: How AI is revolutionizing creative processes in 2024",
    "🎯 Pro tip: Combine human creativity with AI efficiency for unbeatable results",
    "🔥 BREAKING: Our AI just went viral! 10M+ views in 24 hours 📈",
    "💬 What's your biggest content creation challenge? Let's solve it together!",
    "🎉 Celebrating 50K followers! Thank you for joining our AI revolution journey"
  ]

  const platforms = ['Twitter', 'Instagram', 'LinkedIn', 'TikTok']

  // Typing animation effect
  useEffect(() => {
    const startTyping = () => {
      setIsTyping(true)
      const randomPost = postContents[Math.floor(Math.random() * postContents.length)]
      let charIndex = 0
      
      const typeChar = () => {
        if (charIndex < randomPost.length) {
          setCurrentTypingText(randomPost.substring(0, charIndex + 1))
          charIndex++
          typingRef.current = setTimeout(typeChar, 50 + Math.random() * 100)
        } else {
          setTimeout(() => {
            setIsTyping(false)
            setCurrentTypingText('')
          }, 2000)
        }
      }
      
      typeChar()
    }

    // Start typing animation randomly
    const typingInterval = setInterval(() => {
      if (Math.random() > 0.7) {
        startTyping()
      }
    }, 8000)

    return () => {
      clearInterval(typingInterval)
      if (typingRef.current) clearTimeout(typingRef.current)
    }
  }, [])

  // Live indicator animation
  useEffect(() => {
    const liveInterval = setInterval(() => {
      setShowLiveIndicator(prev => !prev)
    }, 3000)

    return () => clearInterval(liveInterval)
  }, [])

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setNewPostAnimation(true)
      
      setTimeout(() => {
        const newPost = {
          id: Date.now(),
          content: postContents[Math.floor(Math.random() * postContents.length)],
          likes: Math.floor(Math.random() * 1000) + 50,
          shares: Math.floor(Math.random() * 50) + 5,
          comments: Math.floor(Math.random() * 25) + 2,
          platform: platforms[Math.floor(Math.random() * platforms.length)],
          isLiking: false,
          isTrending: Math.random() > 0.6
        }

        setPosts(prev => [newPost, ...prev.slice(0, 1)])
        setTotalReach(prev => prev + Math.floor(Math.random() * 500) + 200)
        setEngagementRate(prev => Math.min(10, prev + Math.random() * 0.5))
        setGrowthRate(prev => Math.min(50, prev + Math.random() * 2))
        setNotifications(prev => prev + Math.floor(Math.random() * 3) + 1)
        setNewPostAnimation(false)
      }, 2000)

      // Update existing posts with like animations
      setPosts(prev => prev.map(post => ({
        ...post,
        likes: post.likes + Math.floor(Math.random() * 10) + 2,
        shares: post.shares + Math.floor(Math.random() * 3) + 1,
        comments: post.comments + Math.floor(Math.random() * 5) + 1,
        isLiking: Math.random() > 0.7,
        isTrending: post.isTrending || Math.random() > 0.8
      })))

      // Reset like animations
      setTimeout(() => {
        setPosts(prev => prev.map(post => ({ ...post, isLiking: false })))
      }, 1000)
    }, 5000)

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }, [])

  return (
    <div className="relative w-full h-full flex items-center justify-center p-4">
      {/* Darker Glass Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900/40 via-black/60 to-gray-900/40 backdrop-blur-2xl"></div>
      
      {/* Main Content */}
      <div className="relative z-10 w-full max-w-xs">
        {/* Phone Container */}
        <div className="w-full h-[340px] bg-black/30 backdrop-blur-2xl rounded-2xl border border-white/10 shadow-2xl relative overflow-hidden">
          {/* Phone Screen */}
          <div className="absolute inset-2 bg-black/90 backdrop-blur-2xl rounded-xl overflow-hidden border border-white/5">
            {/* Status Bar */}
            <div className="h-6 bg-black/60 backdrop-blur-2xl flex items-center justify-between px-3 text-white/80 text-xs border-b border-white/5">
              <span className="font-medium">9:41 AM</span>
              <div className="flex items-center space-x-1">
                {/* Notification Badge */}
                {notifications > 0 && (
                  <div className="relative">
                    <div className="w-4 h-2 bg-green-400 rounded-sm"></div>
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-bold">{notifications > 9 ? '9+' : notifications}</span>
                    </div>
                  </div>
                )}
                <span className="font-medium">100%</span>
              </div>
            </div>

            {/* App Header */}
            <div className="h-12 bg-black/50 backdrop-blur-2xl flex items-center justify-between px-3 border-b border-white/5">
              <div className="flex items-center space-x-2">
                                  <div className="w-6 h-6 bg-blue-500/20 backdrop-blur-xl rounded-lg flex items-center justify-center border border-blue-500/30">
                    <span className="text-blue-400 font-bold text-sm">📱</span>
                  </div>
                  <h3 className="text-white/90 font-bold text-sm">Social Hub</h3>
              </div>
              
              {/* Live Streaming Indicator */}
                              {showLiveIndicator && (
                  <div className="flex items-center space-x-1 bg-red-500/20 backdrop-blur-2xl px-2 py-1 rounded-full border border-red-500/20">
                    <div className="w-2 h-2 bg-red-400/80 rounded-full animate-pulse"></div>
                    <span className="text-red-400 text-xs font-bold">LIVE</span>
                  </div>
                )}
            </div>

            {/* New Post Animation */}
            {newPostAnimation && (
              <div className="absolute top-18 left-2 right-2 h-16 bg-gradient-to-r from-blue-500/90 to-cyan-500/90 backdrop-blur-xl rounded-lg flex items-center justify-center animate-bounce z-10 border border-white/20">
                <div className="text-center">
                  <div className="text-lg mb-1 animate-spin">✨</div>
                  <span className="text-white font-semibold text-xs">Creating Viral Content...</span>
                </div>
              </div>
            )}

            {/* Typing Indicator */}
            {isTyping && (
              <div className="absolute top-20 left-2 right-2 bg-blue-500/90 backdrop-blur-xl rounded-lg p-2 z-10 border border-white/20">
                <div className="flex items-center space-x-2 mb-1">
                  <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"></div>
                  <span className="text-white text-xs font-semibold">AI is typing...</span>
                  <div className="flex space-x-1">
                    <div className="w-1 h-1 bg-white rounded-full animate-bounce"></div>
                    <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                </div>
                <div className="text-white text-xs bg-white/10 rounded p-1">
                  {currentTypingText}<span className="animate-pulse">|</span>
                </div>
              </div>
            )}

            {/* Posts Feed */}
            <div className="p-2 space-y-2 overflow-hidden h-full">
              {posts.map((post, index) => (
                <div
                  key={post.id}
                  className={`bg-black/30 backdrop-blur-2xl rounded-lg p-3 border border-white/10 transition-all duration-500 relative ${
                    index === 0 ? 'animate-slideInFromTop' : ''
                  } ${post.isTrending ? 'ring-2 ring-yellow-400/30' : ''}`}
                >
                  {/* Trending Badge */}
                  {post.isTrending && (
                    <div className="absolute -top-1 -right-1 bg-yellow-500/20 backdrop-blur-2xl text-yellow-400 px-2 py-1 rounded-full text-xs font-bold animate-pulse border border-yellow-500/20">
                      🔥 TRENDING
                    </div>
                  )}

                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center relative">
                      <span className="text-white text-xs font-bold">AI</span>
                      {/* Online indicator */}
                      <div className="absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-400 rounded-full border border-white"></div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-1">
                        <span className="text-white/90 text-xs font-semibold">AI Content Creator</span>
                        <div className="w-3 h-3 bg-blue-500/60 rounded-full flex items-center justify-center">
                          <span className="text-white/80 text-xs">✓</span>
                        </div>
                      </div>
                      <div className="text-white/50 text-xs">{post.platform} • 2m ago</div>
                    </div>
                  </div>
                  
                  <p className="text-white/70 text-xs mb-2 leading-relaxed line-clamp-2">
                    {postContents[index % postContents.length]}
                  </p>
                  
                  <div className="flex justify-between text-xs">
                    <div className={`flex items-center space-x-1 text-red-400 transition-all ${post.isLiking ? 'scale-125' : ''}`}>
                      <span className={post.isLiking ? 'animate-bounce' : ''}>❤️</span>
                      <span className="animate-pulse font-medium">{post.likes.toLocaleString()}</span>
                      {post.isLiking && (
                        <div className="absolute">
                          <span className="text-red-400 animate-ping">💖</span>
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-1 text-green-400">
                      <span className="animate-spin" style={{ animationDuration: '3s' }}>🔄</span>
                      <span className="animate-pulse font-medium">{post.shares}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-blue-400">
                      <span>💬</span>
                      <span className="animate-pulse font-medium">{post.comments}</span>
                    </div>
                  </div>

                  {/* Engagement Animation */}
                  {post.isLiking && (
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="absolute top-2 right-2 text-red-400 animate-bounce">💕</div>
                      <div className="absolute bottom-2 left-2 text-yellow-400 animate-bounce" style={{ animationDelay: '0.2s' }}>⭐</div>
                      <div className="absolute top-1/2 left-1/2 text-blue-400 animate-bounce" style={{ animationDelay: '0.4s' }}>✨</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Home Button with Pulse */}
          <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-white/20 backdrop-blur-xl rounded-full border border-white/30 animate-pulse"></div>
          
          {/* Glass Overlay Effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"></div>
        </div>

        {/* Enhanced Stats Panel */}
        <div className="mt-3 flex space-x-2">
          <div className="bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 p-2 flex-1 relative overflow-hidden">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-400 mb-1 animate-pulse">{totalReach.toLocaleString()}</div>
              <div className="text-white/70 text-xs">Reach</div>
            </div>
            {/* Growth indicator */}
            <div className="absolute top-1 right-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-ping"></div>
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 p-2 flex-1 relative">
            <div className="text-center">
              <div className="text-lg font-bold text-cyan-400 mb-1">{engagementRate.toFixed(1)}%</div>
              <div className="text-white/70 text-xs">Engagement</div>
            </div>
            {/* Trending arrow */}
            <div className="absolute top-1 right-1 text-green-400 text-xs animate-bounce">↗️</div>
          </div>
          <div className="bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 p-2 flex-1 relative">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-500 mb-1">+{growthRate.toFixed(1)}%</div>
              <div className="text-white/70 text-xs">Growth</div>
            </div>
            {/* Fire indicator for hot growth */}
            {growthRate > 20 && (
              <div className="absolute top-1 right-1 text-orange-400 text-xs animate-pulse">🔥</div>
            )}
          </div>
        </div>

        {/* Enhanced Floating Social Icons */}
        <div className="absolute -top-2 -left-2 bg-blue-500/80 backdrop-blur-xl rounded-full w-10 h-10 flex items-center justify-center animate-float border border-white/20 relative">
          <span className="text-white text-sm">📘</span>
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"></div>
        </div>
        <div className="absolute -top-1 -right-3 bg-cyan-500/80 backdrop-blur-xl rounded-full w-8 h-8 flex items-center justify-center animate-float border border-white/20" style={{ animationDelay: '0.3s' }}>
          <span className="text-white text-xs">📷</span>
        </div>
        <div className="absolute -bottom-2 -left-3 bg-blue-600/80 backdrop-blur-xl rounded-full w-8 h-8 flex items-center justify-center animate-float border border-white/20" style={{ animationDelay: '0.6s' }}>
          <span className="text-white text-xs animate-spin" style={{ animationDuration: '4s' }}>🎥</span>
        </div>
        <div className="absolute -bottom-1 -right-2 bg-cyan-600/80 backdrop-blur-xl rounded-full w-10 h-10 flex items-center justify-center animate-float border border-white/20" style={{ animationDelay: '0.9s' }}>
          <span className="text-white text-sm">💬</span>
        </div>

        {/* Enhanced Engagement Metrics */}
        <div className="absolute top-4 -right-16 bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 p-2 text-white text-xs">
          <div className="space-y-1">
            <div className="flex items-center space-x-1">
              <span className="animate-bounce">👥</span>
              <span className="animate-pulse font-medium">2.8K</span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="text-green-400">📈</span>
              <span className="animate-pulse font-medium text-green-400">+24%</span>
            </div>
            <div className="flex items-center space-x-1">
              <span className="animate-pulse">🔥</span>
              <span className="animate-pulse font-medium text-orange-400">Viral</span>
            </div>
          </div>
        </div>

        {/* Floating Notifications */}
        {notifications > 5 && (
          <div className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-blue-500/90 backdrop-blur-xl text-white px-3 py-1 rounded-full text-xs font-bold animate-bounce border border-white/20">
            🎉 Going Viral!
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes slideInFromTop {
          0% {
            transform: translateY(-100%);
            opacity: 0;
          }
          100% {
            transform: translateY(0);
            opacity: 1;
          }
        }
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-5px);
          }
        }
        .animate-slideInFromTop {
          animation: slideInFromTop 0.6s ease-out;
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </div>
  )
}

export default function SocialMediaPreview() {
  return (
    <div className="w-full h-full bg-gradient-to-br from-gray-900 via-blue-900 to-cyan-900">
      <SocialMediaIcon />
    </div>
  )
}