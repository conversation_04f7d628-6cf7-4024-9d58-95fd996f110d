#!/usr/bin/env node

/**
 * Test: URL Generation Logic
 * 
 * This script tests the exact URL generation logic used in the dashboard
 */

console.log('🧪 Testing URL Generation Logic\n')

// Exact mapping logic from dashboard/page.tsx line 711
function generateContentUrl(toolId) {
  const contentType = toolId === 'invincible-agent' ? 'invincible_research' 
    : toolId === 'blog-generator' ? 'blog' 
    : toolId === 'email-generator' ? 'email' 
    : toolId === 'youtube-script' ? 'youtube_script' 
    : toolId.replace('-', '_')
  
  return `/content?type=${contentType}`
}

console.log('🔗 DASHBOARD URL GENERATION TEST:\n')

const tools = [
  { id: 'invincible-agent', title: 'Invincible V.1' },
  { id: 'email-generator', title: 'Email Composer' },
  { id: 'tweet-generator', title: 'Social Media' },
  { id: 'blog-generator', title: 'Blog Writer' },
  { id: 'youtube-script', title: 'Video Scripts' }
]

tools.forEach(tool => {
  const url = generateContentUrl(tool.id)
  console.log(`📱 ${tool.title} (${tool.id})`)
  console.log(`   🔗 Generated URL: ${url}`)
  console.log(`   📊 Expected content: ${getExpectedCount(tool.id)}`)
  console.log('')
})

function getExpectedCount(toolId) {
  const counts = {
    'blog-generator': '23 blog items',
    'youtube-script': '7 youtube_script items', 
    'invincible-agent': '4 invincible_research items',
    'email-generator': '0 email items',
    'tweet-generator': '0 social_media items'
  }
  return counts[toolId] || '0 items'
}

console.log('🎯 MANUAL TEST INSTRUCTIONS:\n')

console.log('To test if filtering works, try these direct URLs:')
console.log('')
console.log('1. Blog content (should show 23 items):')
console.log('   http://localhost:3001/content?type=blog')
console.log('')
console.log('2. YouTube scripts (should show 7 items):')
console.log('   http://localhost:3001/content?type=youtube_script')
console.log('')
console.log('3. Invincible research (should show 4 items):')
console.log('   http://localhost:3001/content?type=invincible_research')
console.log('')

console.log('🔍 WHAT TO CHECK:\n')
console.log('1. Do the direct URLs above filter correctly?')
console.log('2. If yes → problem is in dashboard click handling')
console.log('3. If no → problem is in content page URL parameter reading')
console.log('4. Check browser console for any JavaScript errors')
console.log('5. Check Network tab for API calls to /api/content?type=...')

console.log('\n🛠️ DEBUGGING STEPS:\n')
console.log('Step 1: Test direct URLs above first')
console.log('Step 2: Open dev tools before clicking dashboard buttons')
console.log('Step 3: Watch console logs and network requests')
console.log('Step 4: Report what you see in console/network tab')

console.log('\n💡 LIKELY CULPRITS:\n')
console.log('• useEffect dependency array issue')
console.log('• State not updating after URL parameter read')
console.log('• Component not re-rendering')
console.log('• Timing issue with session check')
console.log('• Browser caching the old content') 