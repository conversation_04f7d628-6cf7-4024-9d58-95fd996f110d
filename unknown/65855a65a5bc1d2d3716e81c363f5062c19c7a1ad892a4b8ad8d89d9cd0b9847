#!/usr/bin/env node

/**
 * Test Enhanced YouTube Service with Multiple Caption Extraction Methods
 * This script tests the new YouTube service with improved caption extraction
 */

import { YouTubeService } from '../src/lib/youtube-service.ts';

async function testEnhancedYouTubeService() {
  console.log('🚀 Testing Enhanced YouTube Service');
  console.log('=====================================');

  const youtubeService = new YouTubeService();

  // Test 1: Search for videos
  console.log('\n📺 Test 1: Searching for videos...');
  try {
    const searchResult = await youtubeService.searchVideos('JavaScript tutorial', 3);
    console.log(`✅ Found ${searchResult.videos.length} videos`);
    
    searchResult.videos.forEach((video, index) => {
      console.log(`${index + 1}. ${video.title}`);
      console.log(`   Channel: ${video.channelTitle}`);
      console.log(`   Views: ${parseInt(video.viewCount).toLocaleString()}`);
      console.log(`   Duration: ${video.duration}`);
      console.log(`   ID: ${video.id}\n`);
    });
  } catch (error) {
    console.error('❌ Search failed:', error.message);
  }

  // Test 2: Extract captions using multiple methods
  console.log('\n📝 Test 2: Testing caption extraction methods...');
  
  // Use Rick Astley's "Never Gonna Give You Up" as it has captions
  const testVideoId = 'dQw4w9WgXcQ';
  console.log(`Testing with video ID: ${testVideoId}`);

  try {
    console.log('\n🔍 Extracting captions...');
    const captions = await youtubeService.extractCaptions(testVideoId, 'en');
    
    if (captions.length > 0) {
      console.log(`✅ Successfully extracted ${captions.length} caption segments`);
      
      // Show first few captions
      console.log('\n📋 First 5 caption segments:');
      captions.slice(0, 5).forEach((caption, index) => {
        const timestamp = Math.floor(caption.start / 60) + ':' + String(Math.floor(caption.start % 60)).padStart(2, '0');
        console.log(`${index + 1}. [${timestamp}] ${caption.text}`);
      });
      
      // Test transcript combination
      const fullTranscript = youtubeService.combineCaptions(captions);
      console.log(`\n📄 Full transcript length: ${fullTranscript.length} characters`);
      console.log(`📄 Preview: ${fullTranscript.substring(0, 200)}...`);
      
      // Test key moments extraction
      const keyMoments = youtubeService.extractKeyMoments(captions, 30);
      console.log(`\n⏰ Extracted ${keyMoments.length} key moments:`);
      keyMoments.slice(0, 3).forEach((moment, index) => {
        console.log(`${index + 1}. [${moment.timestamp}] ${moment.text}`);
      });
      
    } else {
      console.log('⚠️ No captions found for this video');
    }
  } catch (error) {
    console.error('❌ Caption extraction failed:', error.message);
  }

  // Test 3: Get available languages
  console.log('\n🌍 Test 3: Getting available caption languages...');
  try {
    const languages = await youtubeService.getAvailableLanguages(testVideoId);
    console.log(`✅ Available languages: ${languages.join(', ')}`);
  } catch (error) {
    console.error('❌ Failed to get languages:', error.message);
  }

  // Test 4: Get video metadata
  console.log('\n📊 Test 4: Getting video metadata...');
  try {
    const metadata = await youtubeService.getVideoMetadata(testVideoId);
    if (metadata) {
      console.log('✅ Video metadata:');
      console.log(`   Title: ${metadata.title}`);
      console.log(`   Channel: ${metadata.channelTitle}`);
      console.log(`   Views: ${parseInt(metadata.viewCount).toLocaleString()}`);
      console.log(`   Likes: ${metadata.likeCount ? parseInt(metadata.likeCount).toLocaleString() : 'N/A'}`);
      console.log(`   Duration: ${metadata.duration}`);
      console.log(`   Published: ${new Date(metadata.publishedAt).toLocaleDateString()}`);
    } else {
      console.log('⚠️ Could not get video metadata');
    }
  } catch (error) {
    console.error('❌ Failed to get metadata:', error.message);
  }

  // Test 5: Search and extract captions in one go
  console.log('\n🔄 Test 5: Search and extract captions combined...');
  try {
    const results = await youtubeService.searchAndExtractCaptions('AI tutorial', 2, 'en');
    console.log(`✅ Processed ${results.length} videos with captions`);
    
    results.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.video.title}`);
      console.log(`   Captions: ${result.captions.length} segments`);
      console.log(`   Transcript length: ${result.transcript.length} characters`);
      if (result.transcript.length > 0) {
        console.log(`   Preview: ${result.transcript.substring(0, 100)}...`);
      }
    });
  } catch (error) {
    console.error('❌ Combined search and extraction failed:', error.message);
  }

  // Test 6: Test with a video that might not have captions
  console.log('\n🧪 Test 6: Testing fallback behavior with different video...');
  const testVideoId2 = 'jNQXAC9IVRw'; // A different video ID
  try {
    const captions2 = await youtubeService.extractCaptions(testVideoId2, 'en');
    if (captions2.length > 0) {
      console.log(`✅ Extracted ${captions2.length} captions from video 2`);
    } else {
      console.log('ℹ️ No captions available for video 2 (this is expected for some videos)');
    }
  } catch (error) {
    console.log('ℹ️ Caption extraction failed for video 2 (expected behavior)');
  }

  console.log('\n🎉 Enhanced YouTube Service testing completed!');
  console.log('\n📋 Summary of Features Tested:');
  console.log('   ✅ Video search with provided API key');
  console.log('   ✅ Multi-method caption extraction (youtube-transcript + custom)');
  console.log('   ✅ Language detection and fallback');
  console.log('   ✅ Caption processing and formatting');
  console.log('   ✅ Key moments extraction');
  console.log('   ✅ Video metadata retrieval');
  console.log('   ✅ Combined search and extraction workflow');
  console.log('   ✅ Graceful error handling and fallbacks');
}

// Run the test
testEnhancedYouTubeService().catch(console.error); 