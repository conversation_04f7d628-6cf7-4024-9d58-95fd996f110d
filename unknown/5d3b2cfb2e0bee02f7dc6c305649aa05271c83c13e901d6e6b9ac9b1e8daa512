{"timestamp": "2025-06-30T05:43:33.576Z", "testType": "direct_implementation_test", "results": {"integrity": {"packageJsonUpdated": true, "serviceFileExists": true, "serviceFileValid": true, "documentationExists": true}, "compilation": true, "apiAvailable": true, "directService": {"directTestPossible": false, "reason": "Unknown file extension \".ts\" for /Users/<USER>/Desktop/old invincible with deepresearch/src/lib/youtube-service.ts"}}}