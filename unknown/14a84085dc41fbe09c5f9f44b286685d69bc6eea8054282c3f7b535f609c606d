#!/usr/bin/env node

/**
 * Debug: Content Filtering Issues
 * 
 * This script checks the database content and verifies the filtering logic
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function debugContentFiltering() {
  console.log('🔍 Debugging Content Filtering Issues\n')

  try {
    // Check what content exists in the database
    console.log('📊 CHECKING DATABASE CONTENT:\n')
    
    const allContent = await prisma.content.findMany({
      select: {
        id: true,
        type: true,
        title: true,
        userId: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (allContent.length === 0) {
      console.log('❌ NO CONTENT FOUND IN DATABASE')
      console.log('   This is likely why filtering appears broken - there\'s nothing to filter!')
      console.log('   Try generating some content first using the AI tools.\n')
      return
    }

    console.log(`✅ Found ${allContent.length} content items:\n`)

    // Group content by type
    const contentByType = {}
    allContent.forEach(item => {
      if (!contentByType[item.type]) {
        contentByType[item.type] = []
      }
      contentByType[item.type].push(item)
    })

    // Show content breakdown
    Object.keys(contentByType).forEach(type => {
      const count = contentByType[type].length
      console.log(`📄 ${type}: ${count} items`)
      
      // Show first few titles
      contentByType[type].slice(0, 3).forEach(item => {
        console.log(`   • "${item.title.substring(0, 50)}${item.title.length > 50 ? '...' : ''}"`)
      })
      console.log('')
    })

    // Check tool mapping
    console.log('🔗 TOOL ID MAPPINGS:\n')
    
    const toolMappings = [
      { dashboardId: 'invincible-agent', contentType: 'invincible_research' },
      { dashboardId: 'blog-generator', contentType: 'blog' },
      { dashboardId: 'email-generator', contentType: 'email' },
      { dashboardId: 'youtube-script', contentType: 'youtube_script' },
      { dashboardId: 'tweet-generator', contentType: 'social_media' }
    ]

    toolMappings.forEach(mapping => {
      const hasContent = contentByType[mapping.contentType] ? '✅' : '❌'
      const count = contentByType[mapping.contentType]?.length || 0
      console.log(`${hasContent} ${mapping.dashboardId} → ${mapping.contentType} (${count} items)`)
    })

    console.log('\n🧪 TESTING API FILTERING:\n')

    // Test API filtering for each type that has content
    for (const [type, items] of Object.entries(contentByType)) {
      console.log(`Testing filter for type: ${type}`)
      
      try {
        const filteredContent = await prisma.content.findMany({
          where: {
            type: type
          },
          select: {
            id: true,
            type: true,
            title: true
          }
        })
        
        console.log(`✅ API would return ${filteredContent.length} items for type "${type}"`)
      } catch (error) {
        console.log(`❌ Error filtering type "${type}":`, error.message)
      }
    }

    console.log('\n💡 RECOMMENDATIONS:\n')

    if (Object.keys(contentByType).length === 0) {
      console.log('1. Generate some content using the AI tools first')
      console.log('2. Try creating a blog post, email, or using Invincible V.1')
      console.log('3. Then test the filtering functionality')
    } else {
      console.log('1. The database has content, so the issue might be in the frontend')
      console.log('2. Check browser console for JavaScript errors')
      console.log('3. Verify the URL parameters are being processed correctly')
      console.log('4. Check Network tab to see if API calls include type parameter')
    }

  } catch (error) {
    console.error('❌ Database error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugContentFiltering() 