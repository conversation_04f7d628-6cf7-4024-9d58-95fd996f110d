#!/usr/bin/env node

/**
 * Test script for Enhanced Retry Logic
 * Usage: node scripts/test-unlimited-retries.mjs
 */

import { TavilySearchService } from '../src/lib/search.js';

console.log('🔄 Testing Enhanced Retry Logic');
console.log('===============================');

async function testRetryLogic() {
  try {
    const tavilyService = new TavilySearchService();
    
    // Check initial key status
    const initialStatus = tavilyService.getKeyRotatorStatus();
    console.log(`📊 Total Keys: ${initialStatus.totalKeys}`);
    console.log(`📊 Available Keys: ${initialStatus.availableKeys}`);
    
    // Test search with enhanced retry
    console.log(`\n🔍 Testing search with unlimited retries...`);
    const testQuery = '5 best open router alternatives of 2025';
    
    try {
      const results = await tavilyService.search(testQuery, 5);
      console.log(`✅ Search successful: ${results.items.length} results`);
    } catch (error) {
      console.log(`❌ Search failed: ${error.message}`);
      
      // Show final key status
      const finalStatus = tavilyService.getKeyRotatorStatus();
      console.log(`📊 Final Available Keys: ${finalStatus.availableKeys}/${finalStatus.totalKeys}`);
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testRetryLogic(); 