import { motion } from 'framer-motion'

interface ProgressBarProps {
  progress: number
  color?: string
  gradient?: string
  height?: number
  showPercentage?: boolean
  animated?: boolean
}

export default function ProgressBar({ 
  progress, 
  color = '#3B82F6',
  gradient,
  height = 8,
  showPercentage = false,
  animated = true
}: ProgressBarProps) {
  return (
    <div className="relative">
      <div 
        className="w-full bg-white/10 rounded-full overflow-hidden"
        style={{ height: `${height}px` }}
      >
        <motion.div
          className="h-full rounded-full relative overflow-hidden"
          style={{
            background: gradient ? `linear-gradient(to right, ${gradient})` : color,
          }}
          initial={animated ? { width: 0 } : { width: `${progress}%` }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 1.5, ease: "easeOut" }}
        >
          {/* Shimmer effect */}
          {animated && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{
                x: ['-100%', '100%'],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                repeatDelay: 1,
                ease: "linear"
              }}
            />
          )}
        </motion.div>
      </div>
      
      {showPercentage && (
        <div className="flex justify-between mt-2 text-sm">
          <span className="text-gray-400">Progress</span>
          <motion.span 
            className="text-white font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            {progress}%
          </motion.span>
        </div>
      )}
    </div>
  )
} 