#!/usr/bin/env node

/**
 * Test script to demonstrate instant key rotation functionality
 * Usage: npm run test:rotation
 */

// Since we're using TypeScript, we'll create a simple test without direct imports
// This approach works with the compiled JavaScript from Next.js

import axios from 'axios';

// Mock the TavilySearchService functionality for testing
class MockTavilySearchService {
  constructor() {
    this.keys = [
      '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',
      '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',
      '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',
      '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',
      '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',
      '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',
      '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',
      '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',
      '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'
    ];
    this.currentKeyIndex = 0;
    this.keyStatus = new Map();
    
    // Initialize all keys as healthy
    this.keys.forEach(key => {
      this.keyStatus.set(key, { errors: 0, active: true });
    });
  }

  getCurrentKeyInfo() {
    const currentKey = this.keys[this.currentKeyIndex];
    return {
      key: `...${currentKey.slice(-4)}`,
      status: 'healthy',
      availableKeys: this.keys.filter(key => this.keyStatus.get(key).active).length
    };
  }

  getKeyRotatorStatus() {
    const availableKeys = this.keys.filter(key => this.keyStatus.get(key).active).length;
    const keyHealthReport = this.keys.map((key, index) => ({
      keyId: `Key ${index + 1} (...${key.slice(-4)})`,
      status: this.keyStatus.get(key).active ? 'active' : 'limited',
      errors: this.keyStatus.get(key).errors
    }));

    return {
      totalKeys: this.keys.length,
      currentKeyIndex: this.currentKeyIndex,
      availableKeys,
      lastRotation: new Date(),
      keyHealthReport
    };
  }

  forceKeyRotation() {
    const oldKey = this.keys[this.currentKeyIndex];
    this.currentKeyIndex = (this.currentKeyIndex + 1) % this.keys.length;
    const newKey = this.keys[this.currentKeyIndex];
    console.log(`🔄 Rotated from ...${oldKey.slice(-4)} to ...${newKey.slice(-4)}`);
    return newKey;
  }

  async search(query, numResults) {
    const currentKey = this.keys[this.currentKeyIndex];
    console.log(`🔍 Attempting search with key ending in ...${currentKey.slice(-4)}`);
    
    try {
      // Test actual Tavily API call
      const response = await axios.post('https://api.tavily.com/search', {
        api_key: currentKey,
        query: query,
        search_depth: "basic",
        max_results: numResults
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log(`✅ Search successful: ${response.data.results?.length || 0} results`);
      return { items: response.data.results || [] };
      
    } catch (error) {
      const status = this.keyStatus.get(currentKey);
      status.errors += 1;
      
      console.log(`❌ Search failed: ${error.response?.status || 'Network Error'}`);
      console.log(`🔄 INSTANT KEY ROTATION triggered (Error: ${error.response?.status || 'Network'})`);
      
      // Simulate instant rotation
      this.forceKeyRotation();
      
      throw error;
    }
  }
}

async function testInstantKeyRotation() {
  console.log('🧪 Testing Instant Key Rotation for Tavily API');
  console.log('=' .repeat(60));
  
  const tavilyService = new MockTavilySearchService();
  
  // Show initial status
  console.log('\n📊 Initial Key Status:');
  const initialStatus = tavilyService.getKeyRotatorStatus();
  console.log(`   Total Keys: ${initialStatus.totalKeys}`);
  console.log(`   Current Key: ${initialStatus.currentKeyIndex + 1}/${initialStatus.totalKeys}`);
  console.log(`   Available Keys: ${initialStatus.availableKeys}`);
  
  // Show key health report
  if (initialStatus.keyHealthReport) {
    console.log('\n🏥 Key Health Report:');
    initialStatus.keyHealthReport.forEach(report => {
      console.log(`   ${report.keyId}: ${report.status} (${report.errors} errors)`);
    });
  }
  
  // Test 1: Normal search
  console.log('\n🔍 Test 1: Normal Search');
  try {
    const results = await tavilyService.search('test query', 3);
    console.log(`✅ Normal search successful: ${results.items.length} results`);
  } catch (error) {
    console.log(`❌ Normal search failed: ${error.message}`);
    console.log('🔄 This triggered instant key rotation');
  }
  
  // Show status after first test
  console.log('\n📊 Status After Test 1:');
  const afterTest1 = tavilyService.getKeyRotatorStatus();
  console.log(`   Current Key: ${afterTest1.currentKeyIndex + 1}/${afterTest1.totalKeys}`);
  console.log(`   Available Keys: ${afterTest1.availableKeys}`);
  
  // Test 2: Force rotation
  console.log('\n🔄 Test 2: Force Key Rotation');
  const oldKey = tavilyService.getCurrentKeyInfo();
  console.log(`   Before rotation: ${oldKey.key}`);
  
  const newKey = tavilyService.forceKeyRotation();
  const afterRotation = tavilyService.getCurrentKeyInfo();
  console.log(`   After rotation: ${afterRotation.key}`);
  console.log(`   Rotation ${oldKey.key !== afterRotation.key ? 'SUCCESS' : 'FAILED'}`);
  
  // Test 3: Multiple searches to test automatic rotation
  console.log('\n🔍 Test 3: Multiple Searches (Testing Automatic Rotation)');
  const queries = ['ai tools', 'javascript framework', 'nodejs tutorial'];
  
  for (let i = 0; i < queries.length; i++) {
    console.log(`\n   Search ${i + 1}/${queries.length}: "${queries[i]}"`);
    const keyBefore = tavilyService.getCurrentKeyInfo();
    console.log(`   Key before: ${keyBefore.key}`);
    
    try {
      const results = await tavilyService.search(queries[i], 2);
      console.log(`   ✅ Search successful: ${results.items.length} results`);
    } catch (error) {
      console.log(`   ❌ Search failed: ${error.message.substring(0, 100)}...`);
      console.log(`   🔄 Instant key rotation triggered`);
    }
    
    const keyAfter = tavilyService.getCurrentKeyInfo();
    console.log(`   Key after: ${keyAfter.key}`);
    
    if (keyBefore.key !== keyAfter.key) {
      console.log(`   🔄 Key rotation detected!`);
    }
    
    // Small delay between searches
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  // Final status report
  console.log('\n📊 Final Status Report:');
  const finalStatus = tavilyService.getKeyRotatorStatus();
  console.log(`   Total Keys: ${finalStatus.totalKeys}`);
  console.log(`   Current Key: ${finalStatus.currentKeyIndex + 1}/${finalStatus.totalKeys}`);
  console.log(`   Available Keys: ${finalStatus.availableKeys}`);
  console.log(`   Last Rotation: ${finalStatus.lastRotation.toISOString()}`);
  
  if (finalStatus.keyHealthReport) {
    console.log('\n🏥 Final Key Health Report:');
    finalStatus.keyHealthReport.forEach(report => {
      console.log(`   ${report.keyId}: ${report.status} (${report.errors} errors)`);
    });
  }
  
  console.log('\n✅ Instant Key Rotation Test Complete!');
  console.log('=' .repeat(60));
  
  // Show what instant rotation handles
  console.log('\n🔄 Instant Rotation Triggers:');
  console.log('   • Quota/Rate limit errors (429, quota exceeded)');
  console.log('   • Authentication errors (401, 403, invalid, expired)');
  console.log('   • Request validation errors (400, 422, bad request)');
  console.log('   • Throttling detection (too many requests)');
  console.log('   • Server errors on first attempt (5xx)');
  console.log('   • Network/connection errors on first attempt');
  console.log('   • High error count (3+ errors per key)');
  
  console.log('\n⚡ Benefits of Instant Rotation:');
  console.log('   • Immediate recovery from API errors');
  console.log('   • No waiting for quota reset timers');
  console.log('   • Faster retry with new key (500ms vs 2-8s)');
  console.log('   • Intelligent key health tracking');
  console.log('   • Automatic selection of least problematic key');
  console.log('   • 9 API keys providing 450% more capacity');
}

// Run the test
testInstantKeyRotation().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
}); 