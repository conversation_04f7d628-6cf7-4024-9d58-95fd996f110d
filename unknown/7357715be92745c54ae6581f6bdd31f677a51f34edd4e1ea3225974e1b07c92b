import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { QuotaManager, QuotaType } from '@/lib/quota'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const quotaType = searchParams.get('type') as QuotaType

    if (quotaType) {
      // Get specific quota
      const quota = await QuotaManager.checkQuota(session.user.id, quotaType)
      return NextResponse.json(quota)
    } else {
      // Get all quotas
      const quotas = await QuotaManager.getAllQuotas(session.user.id)
      return NextResponse.json(quotas)
    }
  } catch (error) {
    console.error('Error fetching quotas:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { quotaType } = await request.json()

    if (!quotaType) {
      return NextResponse.json(
        { error: 'Quota type is required' },
        { status: 400 }
      )
    }

    const success = await QuotaManager.useQuota(session.user.id, quotaType)
    
    if (!success) {
      return NextResponse.json(
        { error: 'Quota exceeded or insufficient quota' },
        { status: 429 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error using quota:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 