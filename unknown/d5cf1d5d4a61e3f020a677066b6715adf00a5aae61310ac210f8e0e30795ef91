#!/usr/bin/env node

/**
 * Debug Caption Extraction - Examine Response Format
 */

import { YoutubeTranscript } from 'youtube-transcript';
import axios from 'axios';

console.log('🔍 Debug Caption Extraction');
console.log('============================');

async function debugCaptionFormat(videoId) {
  console.log(`🔧 Debugging caption format for ${videoId}...`);
  
  try {
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
    const response = await axios.get(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      },
      timeout: 15000
    });

    const html = response.data;
    
    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?})\s*;\s*(?:var\s+(?:meta|head)|<\/script|\n)/);
    
    if (!playerResponseMatch) {
      console.log('❌ Could not find ytInitialPlayerResponse');
      return;
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    
    console.log(`📊 Video: ${playerResponse.videoDetails?.title || 'Unknown'}`);
    
    if (!playerResponse.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
      console.log('⚠️ No captions available');
      return;
    }

    const tracks = playerResponse.captions.playerCaptionsTracklistRenderer.captionTracks;
    console.log(`✅ Found ${tracks.length} caption tracks`);
    
    // Try the first English track (auto-generated might be more reliable)
    let bestTrack = tracks.find(track => track.languageCode === 'en' && track.kind === 'asr');
    if (!bestTrack) {
      bestTrack = tracks.find(track => track.languageCode === 'en');
    }
    if (!bestTrack) {
      bestTrack = tracks[0];
    }
    
    if (!bestTrack?.baseUrl) {
      console.log('❌ No suitable track found');
      return;
    }

    console.log(`🎯 Using: ${bestTrack.name?.simpleText || bestTrack.languageCode} (${bestTrack.kind || 'manual'})`);
    console.log(`🔗 Base URL: ${bestTrack.baseUrl}`);
    
    // Try different formats
    const formats = ['json3', 'srv3', 'ttml', 'vtt'];
    
    for (const format of formats) {
      console.log(`\n📝 Testing format: ${format}`);
      try {
        const captionUrl = bestTrack.baseUrl + `&fmt=${format}`;
        const captionResponse = await axios.get(captionUrl, {
          timeout: 10000
        });
        
        console.log(`✅ Format ${format} - Status: ${captionResponse.status}`);
        console.log(`📄 Content Type: ${captionResponse.headers['content-type']}`);
        console.log(`📊 Data Length: ${JSON.stringify(captionResponse.data).length} chars`);
        
        // Show first part of response
        const dataStr = typeof captionResponse.data === 'string' 
          ? captionResponse.data 
          : JSON.stringify(captionResponse.data, null, 2);
        console.log(`📋 First 300 chars: ${dataStr.substring(0, 300)}...`);
        
        // Try to parse if it's json3
        if (format === 'json3') {
          if (captionResponse.data && typeof captionResponse.data === 'object') {
            console.log('🔍 JSON3 Structure:');
            console.log(`   - events: ${captionResponse.data.events ? captionResponse.data.events.length : 'undefined'}`);
            
            if (captionResponse.data.events && captionResponse.data.events.length > 0) {
              const firstEvent = captionResponse.data.events[0];
              console.log('   - First event:', JSON.stringify(firstEvent, null, 2));
              
              // Try to extract captions
              const captions = [];
              for (const event of captionResponse.data.events) {
                if (event.segs) {
                  const text = event.segs
                    .map(seg => seg.utf8 || '')
                    .join(' ')
                    .trim();
                  
                  if (text) {
                    captions.push({
                      text,
                      start: parseFloat(event.tStartMs) / 1000 || 0,
                      duration: parseFloat(event.dDurationMs) / 1000 || 2.0
                    });
                  }
                }
              }
              
              console.log(`✅ Extracted ${captions.length} captions from json3`);
              if (captions.length > 0) {
                console.log('📋 First 3 captions:');
                captions.slice(0, 3).forEach((cap, i) => {
                  console.log(`   ${i + 1}. [${Math.floor(cap.start)}s] ${cap.text}`);
                });
                return captions; // Success!
              }
            }
          }
        }
        
        // Try VTT format (WebVTT)
        if (format === 'vtt' && typeof captionResponse.data === 'string') {
          console.log('🔍 Parsing VTT format...');
          const vttLines = captionResponse.data.split('\n');
          const captions = [];
          
          for (let i = 0; i < vttLines.length; i++) {
            const line = vttLines[i];
            // Look for timestamp pattern: 00:00:00.000 --> 00:00:02.000
            if (line.includes('-->')) {
              const [startTime, endTime] = line.split('-->').map(t => t.trim());
              const textLine = vttLines[i + 1];
              
              if (textLine && textLine.trim()) {
                captions.push({
                  text: textLine.trim(),
                  start: parseVTTTime(startTime),
                  duration: parseVTTTime(endTime) - parseVTTTime(startTime)
                });
              }
            }
          }
          
          console.log(`✅ Extracted ${captions.length} captions from VTT`);
          if (captions.length > 0) {
            console.log('📋 First 3 captions:');
            captions.slice(0, 3).forEach((cap, i) => {
              console.log(`   ${i + 1}. [${Math.floor(cap.start)}s] ${cap.text}`);
            });
            return captions; // Success!
          }
        }
        
      } catch (error) {
        console.log(`❌ Format ${format} failed: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Debug failed: ${error.message}`);
  }
}

function parseVTTTime(timeStr) {
  // Parse VTT time format: 00:00:00.000
  const parts = timeStr.split(':');
  if (parts.length === 3) {
    const hours = parseInt(parts[0]);
    const minutes = parseInt(parts[1]);
    const seconds = parseFloat(parts[2]);
    return hours * 3600 + minutes * 60 + seconds;
  }
  return 0;
}

// Test youtube-transcript with different options
async function testYouTubeTranscriptDebug(videoId) {
  console.log(`\n📝 Testing youtube-transcript library with ${videoId}...`);
  
  const options = [
    { lang: 'en' },
    { lang: 'en-US' },
    {}, // No options
    { lang: 'en-GB' },
  ];
  
  for (const option of options) {
    try {
      console.log(`🔍 Trying options: ${JSON.stringify(option)}`);
      const transcript = await YoutubeTranscript.fetchTranscript(videoId, option);
      
      console.log(`✅ Success! Got ${transcript.length} segments`);
      if (transcript.length > 0) {
        console.log('📋 First 3 segments:');
        transcript.slice(0, 3).forEach((segment, index) => {
          console.log(`   ${index + 1}. [${Math.floor(segment.offset)}s] ${segment.text}`);
        });
        return transcript; // Success!
      }
    } catch (error) {
      console.log(`❌ Failed with options ${JSON.stringify(option)}: ${error.message}`);
    }
  }
}

// Test with a simple, well-known video
async function runDebugTests() {
  // Test with a known educational video that should have captions
  const testVideos = [
    'W6NZfCO5SIk', // Programming with Mosh
    'EerdGm-ehJQ', // SuperSimpleDev
    'dQw4w9WgXcQ', // Rick Astley (classic test video)
  ];
  
  for (const videoId of testVideos) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🎯 Testing Video: ${videoId}`);
    console.log(`🔗 URL: https://youtube.com/watch?v=${videoId}`);
    console.log(`${'='.repeat(60)}`);
    
    // Test youtube-transcript first
    const transcriptResult = await testYouTubeTranscriptDebug(videoId);
    if (transcriptResult && transcriptResult.length > 0) {
      console.log('🎉 youtube-transcript worked! Skipping custom debug for this video.');
      continue;
    }
    
    // Debug our custom method
    const customResult = await debugCaptionFormat(videoId);
    if (customResult && customResult.length > 0) {
      console.log('🎉 Custom extraction worked!');
      break;
    }
  }
  
  console.log('\n🏁 Debug session completed!');
}

runDebugTests(); 