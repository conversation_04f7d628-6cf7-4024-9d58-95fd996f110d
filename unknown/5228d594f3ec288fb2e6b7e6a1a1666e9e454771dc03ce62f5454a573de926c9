#!/usr/bin/env node
/**
 * Test Direct YouTube API Implementation
 * Tests the new implementation that uses YouTube Data API v3 directly
 */

import fs from 'fs';

const BASE_URL = 'http://localhost:3001';

console.log('🚀 Testing Direct YouTube API Implementation');
console.log('=' .repeat(60));

async function testYouTubeGeneration() {
  try {
    console.log('\n📺 Testing YouTube Script Generation with Direct API...');
    
    const testPayload = {
      title: 'AI Technology Trends 2025 - Direct API Test',
      brief: 'Test the new direct YouTube API implementation for caption extraction and content generation',
      duration: '3-5 minutes',
      style: 'educational and engaging',
      targetAudience: 'tech enthusiasts and developers',
      useAdvancedResearch: true
    };

    console.log('📋 Test Payload:', JSON.stringify(testPayload, null, 2));
    
    const response = await fetch(`${BASE_URL}/api/generate/youtube`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`\n🔍 Response Status: ${response.status} ${response.statusText}`);

    if (response.ok) {
      const data = await response.json();
      console.log('✅ YouTube generation successful!');
      
      if (data.content) {
        console.log(`📄 Generated content length: ${data.content.length} characters`);
        
        // Analyze content for expected elements
        const hasTimestamps = data.content.includes('[') && data.content.includes(']');
        const hasNarration = data.content.includes('NARRATION') || data.content.includes('[00:');
        const hasResearch = data.content.includes('according to') || data.content.includes('research') || data.content.includes('data');
        const hasTechnicalContent = data.content.includes('AI') || data.content.includes('technology') || data.content.includes('2025');
        
        console.log(`\n📊 Content Analysis:`);
        console.log(`   ⏱️  Contains timestamps: ${hasTimestamps ? '✅' : '❌'}`);
        console.log(`   🎭 Contains narration cues: ${hasNarration ? '✅' : '❌'}`);
        console.log(`   🔍 Contains research data: ${hasResearch ? '✅' : '❌'}`);
        console.log(`   🤖 Contains technical content: ${hasTechnicalContent ? '✅' : '❌'}`);
        
        // Save sample output
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `test-youtube-output-${timestamp}.txt`;
        fs.writeFileSync(filename, data.content);
        console.log(`💾 Sample output saved to: ${filename}`);
        
        // Show content preview
        const preview = data.content.substring(0, 500);
        console.log(`\n📖 Content Preview (first 500 chars):`);
        console.log('-'.repeat(50));
        console.log(preview);
        if (data.content.length > 500) {
          console.log('...(truncated)');
        }
        console.log('-'.repeat(50));
        
        return {
          success: true,
          contentGenerated: true,
          contentLength: data.content.length,
          hasTimestamps,
          hasNarration,
          hasResearch,
          hasTechnicalContent
        };
      } else {
        console.log('⚠️ No content generated');
        return { success: true, contentGenerated: false };
      }
      
    } else {
      const errorText = await response.text();
      console.error('❌ YouTube generation failed');
      console.error(`Status: ${response.status} ${response.statusText}`);
      console.error(`Response: ${errorText}`);
      
      return { 
        success: false, 
        error: `HTTP ${response.status}: ${errorText}` 
      };
    }
    
  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
    return { success: false, error: error.message };
  }
}

async function testServerHealth() {
  try {
    console.log('\n🏥 Testing server health...');
    
    const response = await fetch(`${BASE_URL}/api/health`, {
      method: 'GET'
    });
    
    if (response.ok) {
      console.log('✅ Server health check passed');
      return true;
    } else {
      console.log(`⚠️ Health check returned: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Health check failed: ${error.message}`);
    return false;
  }
}

async function testBasicConnectivity() {
  try {
    console.log('\n🔗 Testing basic connectivity...');
    
    const response = await fetch(`${BASE_URL}`, {
      method: 'GET'
    });
    
    if (response.status < 500) {
      console.log(`✅ Server responding (${response.status})`);
      return true;
    } else {
      console.log(`❌ Server error: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.error('💥 Connectivity test failed:', error.message);
    console.log('ℹ️  Make sure the development server is running: npm run dev');
    return false;
  }
}

async function runTests() {
  const results = {
    timestamp: new Date().toISOString(),
    testType: 'direct_youtube_api',
    serverPort: 3001,
    connectivity: false,
    serverHealth: false,
    youtubeGeneration: null
  };

  console.log(`🌐 Testing server at: ${BASE_URL}`);

  // Test 1: Basic connectivity
  results.connectivity = await testBasicConnectivity();
  
  if (!results.connectivity) {
    console.log('\n⚠️ Server not accessible. Please ensure npm run dev is running on port 3001.');
    return results;
  }

  // Test 2: Server health (optional)
  results.serverHealth = await testServerHealth();

  // Test 3: YouTube generation with new direct API
  results.youtubeGeneration = await testYouTubeGeneration();

  // Generate summary report
  console.log('\n📊 Test Summary Report');
  console.log('=' .repeat(60));
  console.log(`🔗 Server connectivity: ${results.connectivity ? '✅ OK' : '❌ Failed'}`);
  console.log(`🏥 Server health: ${results.serverHealth ? '✅ OK' : '⚠️ N/A'}`);
  console.log(`📺 YouTube generation: ${results.youtubeGeneration?.success ? '✅ OK' : '❌ Failed'}`);
  
  if (results.youtubeGeneration?.success && results.youtubeGeneration?.contentGenerated) {
    console.log('\n🎉 Direct YouTube API Implementation Test Results:');
    console.log(`   📄 Content generated: ${results.youtubeGeneration.contentLength} characters`);
    console.log(`   ⏱️  Timestamps included: ${results.youtubeGeneration.hasTimestamps ? '✅' : '❌'}`);
    console.log(`   🎭 Narration cues: ${results.youtubeGeneration.hasNarration ? '✅' : '❌'}`);
    console.log(`   🔍 Research integration: ${results.youtubeGeneration.hasResearch ? '✅' : '❌'}`);
    console.log(`   🤖 Technical content: ${results.youtubeGeneration.hasTechnicalContent ? '✅' : '❌'}`);
    
    console.log('\n✨ SUCCESS: Direct YouTube API implementation is working!');
    console.log('🔧 The system is now using YouTube Data API v3 instead of youtube-transcript library');
  } else {
    console.log('\n⚠️ YouTube generation needs investigation');
    if (results.youtubeGeneration?.error) {
      console.log(`💥 Error details: ${results.youtubeGeneration.error}`);
    }
  }

  // Save detailed results
  const reportPath = `test-results-direct-api-${Date.now()}.json`;
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  console.log(`\n📋 Detailed test results saved to: ${reportPath}`);

  return results;
}

// Run the tests
console.log('⏳ Starting tests in 2 seconds...\n');
setTimeout(() => {
  runTests().catch(console.error);
}, 2000); 