#!/usr/bin/env node

/**
 * Demo script to showcase Word Count Adherence functionality
 * Shows how the system enforces specified word count requirements
 * Usage: node scripts/demo-word-count.mjs
 */

const COLORS = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  DIM: '\x1b[2m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m'
};

function colorize(text, color) {
  return `${color}${text}${COLORS.RESET}`;
}

console.log(colorize('📊 Word Count Adherence - Demo', COLORS.CYAN));
console.log(colorize('=' .repeat(45), COLORS.DIM));

// Sample content examples to demonstrate word counting
const SAMPLE_CONTENT_500 = `
# How to Improve Your Sleep Quality

Getting better sleep is easier than you think. Here's how to start tonight.

## Why Sleep Matters

Good sleep helps your body recover. It also improves your mood and focus. Most adults need 7-9 hours each night.

## Simple Sleep Tips

**Create a bedtime routine.** Do the same things every night before bed. This tells your body it's time to sleep.

**Keep your room cool.** The best temperature is between 65-68°F. A cool room helps you fall asleep faster.

**Avoid screens before bed.** Blue light from phones and TVs can keep you awake. Try reading a book instead.

**Exercise during the day.** Regular activity helps you sleep better at night. Just don't work out right before bed.

## What to Avoid

Don't drink caffeine after 2 PM. Coffee and tea can stay in your system for hours. This makes it hard to fall asleep.

Skip large meals before bedtime. Heavy food can cause discomfort. Eat dinner at least 3 hours before sleep.

## Quick Sleep Fixes

If you can't sleep, don't just lie there. Get up and do a quiet activity. Read or listen to calm music.

Keep a sleep diary. Write down when you go to bed and wake up. This helps you find patterns.

## Getting Started

Pick one tip to try tonight. Start small and build good habits. Better sleep takes time but it's worth it.

Remember, everyone is different. What works for others might not work for you. Be patient and keep trying.

Good sleep is not a luxury. It's essential for your health and happiness. Start improving your sleep today.
`;

const SAMPLE_CONTENT_1000 = `
# Complete Guide to Starting a Small Business

Starting your own business can be exciting and rewarding. Here's everything you need to know to get started.

## Planning Your Business

**Choose your business idea.** Pick something you're passionate about. Your enthusiasm will help you through tough times.

**Research your market.** Find out who your customers are. Learn what they need and want. Study your competition too.

**Write a business plan.** This doesn't have to be fancy. Include your goals, target market, and financial projections.

## Legal Requirements

**Choose a business structure.** You can be a sole proprietorship, LLC, or corporation. Each has different tax implications.

**Register your business name.** Make sure no one else is using it. Check with your state and local government.

**Get necessary licenses.** Different businesses need different permits. Check with your city, county, and state.

**Open a business bank account.** Keep your personal and business finances separate. This makes taxes much easier.

## Financial Planning

**Estimate startup costs.** Include equipment, supplies, rent, and marketing. Don't forget about living expenses while you get started.

**Secure funding.** You might use savings, loans, or investors. Consider starting small and growing gradually.

**Set up accounting.** Track all income and expenses from day one. Use simple software or hire a bookkeeper.

**Understand taxes.** You'll need to pay income tax, self-employment tax, and possibly sales tax. Consider hiring an accountant.

## Marketing Your Business

**Identify your target customers.** Who exactly needs your product or service? Where do they spend their time?

**Create a simple website.** Even a basic site helps customers find you. Include your contact information and services.

**Use social media.** Pick one or two platforms where your customers are. Share helpful content, not just sales pitches.

**Network locally.** Join business groups and attend community events. Word of mouth is powerful for small businesses.

## Managing Operations

**Start small.** Don't try to do everything at once. Focus on doing a few things really well.

**Track your progress.** Set goals and measure your results. Adjust your strategy based on what you learn.

**Take care of customers.** Happy customers come back and refer others. Great service is your best marketing tool.

**Keep learning.** Running a business is a skill you develop over time. Read books, take courses, and find mentors.

## Common Mistakes to Avoid

Don't underestimate how long things take. Everything in business takes longer than expected. Plan accordingly.

Don't ignore your finances. Check your numbers regularly. Know where your money is going.

Don't try to do everything yourself. As you grow, delegate tasks. Your time is better spent on high-value activities.

## Getting Help

**Join local business groups.** Connect with other entrepreneurs. They understand the challenges you're facing.

**Use free resources.** Libraries, SBA, and SCORE offer free business advice. Take advantage of these services.

**Consider hiring professionals.** Accountants, lawyers, and consultants can save you time and money in the long run.

## Next Steps

Start with one small step today. Maybe that's researching your market or writing down your business idea.

Remember, every successful business started with someone taking the first step. You don't need to have everything figured out.

The most important thing is to begin. You'll learn and improve as you go. Your future self will thank you for starting today.
`;

function accurateWordCount(content) {
  // Same logic as in the main agent
  const cleanContent = content
    .replace(/#{1,6}\s+/g, '') // Remove markdown headers
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Replace markdown links with just text
    .replace(/\*\*([^*]+)\*\*/g, '$1') // Remove bold formatting
    .replace(/\*([^*]+)\*/g, '$1') // Remove italic formatting
    .replace(/`([^`]+)`/g, '$1') // Remove code formatting
    .replace(/\n{2,}/g, ' ') // Replace multiple newlines with single space
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim();

  const words = cleanContent.split(/\s+/).filter(word => 
    word.length > 0 && 
    word.match(/[a-zA-Z0-9]/) // Must contain at least one alphanumeric character
  );

  return words.length;
}

function validateWordCount(actualCount, targetCount) {
  const tolerance = 50;
  const difference = Math.abs(actualCount - targetCount);
  const percentage = ((difference / targetCount) * 100).toFixed(1);
  const withinTolerance = difference <= tolerance;

  return {
    withinTolerance,
    difference,
    percentage,
    status: withinTolerance 
      ? `✅ TARGET MET` 
      : actualCount < targetCount 
        ? `⚠️ UNDER TARGET` 
        : `⚠️ OVER TARGET`
  };
}

console.log(colorize('\n🔧 Word Count Enhancement Features:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(40), COLORS.DIM));

const features = [
  '✅ Precise word count targeting (±50 words tolerance)',
  '✅ Automatic content adjustment for length compliance',
  '✅ Accurate word counting algorithm',
  '✅ Real-time validation and monitoring',
  '✅ Smart content expansion and condensation',
  '✅ Maintains content quality during adjustments',
  '✅ Clear logging and status reporting'
];

features.forEach(feature => {
  console.log(colorize(feature, COLORS.WHITE));
});

console.log(colorize('\n📊 Demo: Word Count Analysis', COLORS.BRIGHT));
console.log(colorize('=' .repeat(35), COLORS.DIM));

// Analyze sample content
const samples = [
  { name: 'Short Article', content: SAMPLE_CONTENT_500, target: 500 },
  { name: 'Medium Article', content: SAMPLE_CONTENT_1000, target: 1000 }
];

samples.forEach((sample, index) => {
  const actualCount = accurateWordCount(sample.content);
  const validation = validateWordCount(actualCount, sample.target);
  
  console.log(colorize(`\n${index + 1}. ${sample.name}:`, COLORS.MAGENTA));
  console.log(colorize(`   Target: ${sample.target} words`, COLORS.WHITE));
  console.log(colorize(`   Actual: ${actualCount} words`, COLORS.WHITE));
  console.log(colorize(`   Status: ${validation.status}`, 
    validation.withinTolerance ? COLORS.GREEN : COLORS.YELLOW));
  console.log(colorize(`   Variance: ${validation.difference} words (${validation.percentage}%)`, COLORS.DIM));
});

console.log(colorize('\n🎯 Enhanced Generation Process:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(38), COLORS.DIM));

const processSteps = [
  '1. 📝 Initial content generation with word count requirements',
  '2. 📊 Accurate word count analysis of generated content',
  '3. 🔍 Validation against target word count (±50 tolerance)',
  '4. 🔧 Automatic adjustment if outside acceptable range',
  '5. ✅ Final validation and detailed status reporting'
];

processSteps.forEach(step => {
  console.log(colorize(step, COLORS.WHITE));
});

console.log(colorize('\n💡 Adjustment Strategies:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(30), COLORS.DIM));

console.log(colorize('\n📈 For Content Expansion (when too short):', COLORS.MAGENTA));
const expansionStrategies = [
  '• Add more practical examples and case studies',
  '• Expand existing points with clear explanations',
  '• Include additional actionable tips and steps',
  '• Add relevant statistics and facts (explained simply)',
  '• Include more subheadings and bullet points'
];

expansionStrategies.forEach(strategy => {
  console.log(colorize(strategy, COLORS.DIM));
});

console.log(colorize('\n📉 For Content Condensation (when too long):', COLORS.MAGENTA));
const condensationStrategies = [
  '• Remove redundant phrases and repetitive content',
  '• Combine similar points into single statements',
  '• Eliminate unnecessary examples while keeping the best',
  '• Condense long explanations into shorter versions',
  '• Remove filler words and overly descriptive language'
];

condensationStrategies.forEach(strategy => {
  console.log(colorize(strategy, COLORS.DIM));
});

console.log(colorize('\n🧪 Testing & Validation:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(28), COLORS.DIM));

console.log(colorize('Available Test Commands:', COLORS.MAGENTA));
console.log(colorize('  npm run test:wordcount    # Full word count test suite', COLORS.WHITE));
console.log(colorize('  npm run demo:simple       # Simple writing demo', COLORS.WHITE));
console.log(colorize('  npm run test:enhanced     # Enhanced analysis test', COLORS.WHITE));

console.log(colorize('\n📋 Expected Test Results:', COLORS.MAGENTA));
const expectedResults = [
  '✅ 500-word articles: 450-550 words (within tolerance)',
  '✅ 1000-word articles: 950-1050 words (within tolerance)', 
  '✅ 1500-word articles: 1450-1550 words (within tolerance)',
  '✅ 2000-word articles: 1950-2050 words (within tolerance)'
];

expectedResults.forEach(result => {
  console.log(colorize(`  ${result}`, COLORS.WHITE));
});

console.log(colorize('\n🎉 Key Benefits:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(20), COLORS.DIM));

const benefits = [
  '🎯 Consistent content length for professional standards',
  '📈 Improved SEO with precise length optimization', 
  '⚡ Automatic adjustment saves manual editing time',
  '📊 Real-time monitoring and detailed reporting',
  '🔧 Smart content refinement while preserving quality',
  '✅ Reliable delivery that meets exact specifications'
];

benefits.forEach(benefit => {
  console.log(colorize(benefit, COLORS.GREEN));
});

console.log(colorize('\n📚 Implementation Details:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(32), COLORS.DIM));

console.log(colorize('Word Count Algorithm Features:', COLORS.MAGENTA));
console.log(colorize('  • Removes markdown formatting for accurate counts', COLORS.WHITE));
console.log(colorize('  • Filters out empty strings and non-alphanumeric tokens', COLORS.WHITE));
console.log(colorize('  • Standardized processing across entire system', COLORS.WHITE));
console.log(colorize('  • Real-time validation with detailed variance reporting', COLORS.WHITE));

console.log(colorize('\nGeneration Enhancement:', COLORS.MAGENTA));
console.log(colorize('  • Explicit word count requirements in prompts', COLORS.WHITE));
console.log(colorize('  • Mandatory compliance messaging', COLORS.WHITE));
console.log(colorize('  • Structured content planning for target length', COLORS.WHITE));
console.log(colorize('  • Quality preservation during adjustments', COLORS.WHITE));

console.log(colorize('\n✅ Word Count System Ready!', COLORS.CYAN));
console.log(colorize('   Your content will now precisely match specified word counts', COLORS.WHITE));
console.log(colorize('=' .repeat(60), COLORS.DIM)); 