'use client'

import { useRef, useState, useEffect } from 'react'

// Email Composer Animation
function EmailComposerIcon() {
  const [currentEmail, setCurrentEmail] = useState('')
  const [showCursor, setShowCursor] = useState(true)
  const [emailsSent, setEmailsSent] = useState(0)
  const [isComposing, setIsComposing] = useState(false)
  const [showSendAnimation, setShowSendAnimation] = useState(false)
  const [deliveryRate, setDeliveryRate] = useState(98.5)
  const [openRate, setOpenRate] = useState(76.2)
  const typewriterRef = useRef<NodeJS.Timeout | null>(null)
  const cursorRef = useRef<NodeJS.Timeout | null>(null)

  const emailTemplates = [
    "Subject: Welcome to Our AI Revolution!\n\nDear Valued Customer,\n\nWe're thrilled to welcome you to the future of content creation. Our advanced AI platform is designed to transform your marketing efforts...",
    "Subject: Your Weekly AI Insights Newsletter\n\nHello Innovation Leader,\n\nThis week, we're exploring groundbreaking developments in artificial intelligence and how they're reshaping digital marketing landscapes...",
    "Subject: Exclusive Early Access - 50% Off Premium Features\n\nHi there!\n\nAs one of our valued beta users, you've earned exclusive access to our premium AI tools with a special discount...",
    "Subject: Team Collaboration Meeting - AI Strategy Session\n\nHi Team,\n\nI hope this email finds you well. I'm scheduling our quarterly AI strategy review for next Tuesday at 2:00 PM..."
  ]

  useEffect(() => {
    let templateIndex = 0
    let charIndex = 0
    let isDeleting = false

    const typeEmail = () => {
      const currentTemplate = emailTemplates[templateIndex]
      
      if (!isDeleting) {
        setCurrentEmail(currentTemplate.substring(0, charIndex + 1))
        charIndex++
        setIsComposing(true)
        
        if (charIndex === currentTemplate.length) {
          setTimeout(() => {
            // Send animation
            setShowSendAnimation(true)
            setIsComposing(false)
            setEmailsSent(prev => prev + 1)
            setDeliveryRate(prev => Math.min(99.9, prev + Math.random() * 0.5))
            setOpenRate(prev => Math.min(85, prev + Math.random() * 2))
            
            setTimeout(() => {
              setShowSendAnimation(false)
              isDeleting = true
            }, 2000)
          }, 1000)
        }
      } else {
        setCurrentEmail(currentTemplate.substring(0, charIndex - 1))
        charIndex--
        
        if (charIndex === 0) {
          isDeleting = false
          templateIndex = (templateIndex + 1) % emailTemplates.length
        }
      }
    }

    typewriterRef.current = setInterval(typeEmail, isDeleting ? 20 : 80)

    return () => {
      if (typewriterRef.current) clearInterval(typewriterRef.current)
    }
  }, [])

  useEffect(() => {
    cursorRef.current = setInterval(() => {
      setShowCursor(prev => !prev)
    }, 500)

    return () => {
      if (cursorRef.current) clearInterval(cursorRef.current)
    }
  }, [])

  return (
    <div className="relative w-full h-full flex items-center justify-center p-4">
      {/* Darker Glass Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900/40 via-black/60 to-gray-900/40 backdrop-blur-2xl"></div>
      
      {/* Main Content */}
      <div className="relative z-10 w-full max-w-sm">
        {/* Email Interface */}
        <div className="w-full h-[300px] bg-black/30 backdrop-blur-2xl rounded-xl border border-white/10 shadow-2xl relative overflow-hidden">
          {/* Email Header */}
          <div className="h-12 bg-black/50 backdrop-blur-2xl border-b border-white/5 flex items-center justify-between px-4">
            <div className="flex items-center space-x-2">
                              <div className="w-8 h-8 bg-emerald-500/20 backdrop-blur-xl rounded-lg flex items-center justify-center border border-emerald-500/30">
                  <span className="text-emerald-400 font-bold text-sm">📧</span>
                </div>
                <div>
                  <h3 className="text-white/90 font-semibold text-sm">AI Email Composer</h3>
                  <p className="text-white/60 text-xs">Smart Email Generation</p>
                </div>
            </div>
                          <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-400/60 rounded-full animate-pulse shadow-lg"></div>
                <span className="text-white/80 text-xs font-medium">ACTIVE</span>
              </div>
          </div>

          {/* Email Composition Area */}
          <div className="p-4 h-full">
            <div className="bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 h-full p-3 relative overflow-hidden">
              <pre className="text-xs text-white/80 font-mono whitespace-pre-wrap leading-relaxed">
                {currentEmail}
                <span className={`${showCursor && isComposing ? 'opacity-100' : 'opacity-0'} transition-opacity text-emerald-400/80`}>|</span>
              </pre>
              
              {/* Send Animation */}
              {showSendAnimation && (
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/90 to-blue-500/90 backdrop-blur-xl flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="text-3xl mb-2 animate-bounce">✈️</div>
                    <div className="font-bold text-sm">Email Sent Successfully!</div>
                    <div className="text-emerald-100 text-xs mt-1">Delivered to recipient</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Glass Overlay Effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"></div>
        </div>

        {/* Stats Panel */}
        <div className="mt-4 flex space-x-2">
          <div className="bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 flex-1">
            <div className="text-center">
              <div className="text-lg font-bold text-emerald-400/90 mb-1">{emailsSent}</div>
              <div className="text-white/50 text-xs">Sent</div>
            </div>
          </div>
          <div className="bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 flex-1">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-400/90 mb-1">{deliveryRate.toFixed(1)}%</div>
              <div className="text-white/50 text-xs">Delivery</div>
            </div>
          </div>
          <div className="bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 flex-1">
            <div className="text-center">
              <div className="text-lg font-bold text-purple-400/90 mb-1">{openRate.toFixed(1)}%</div>
              <div className="text-white/50 text-xs">Open Rate</div>
            </div>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute -top-2 -left-2 bg-emerald-500/20 backdrop-blur-2xl text-emerald-400 px-3 py-1 rounded-full text-xs font-semibold border border-emerald-500/20 animate-float">
          📨 Inbox
        </div>
        <div className="absolute -top-1 -right-3 bg-blue-500/20 backdrop-blur-2xl text-blue-400 px-3 py-1 rounded-full text-xs font-semibold border border-blue-500/20 animate-float" style={{ animationDelay: '0.5s' }}>
          ✅ Delivered
        </div>
        <div className="absolute -bottom-2 -left-3 bg-purple-500/20 backdrop-blur-2xl text-purple-400 px-3 py-1 rounded-full text-xs font-semibold border border-purple-500/20 animate-float" style={{ animationDelay: '1s' }}>
          📝 Templates
        </div>
        <div className="absolute -bottom-1 -right-2 bg-indigo-500/20 backdrop-blur-2xl text-indigo-400 px-3 py-1 rounded-full text-xs font-semibold border border-indigo-500/20 animate-float" style={{ animationDelay: '1.5s' }}>
          🔗 Personalized
        </div>

        {/* Typing Indicator */}
        {isComposing && (
          <div className="absolute bottom-16 right-4 bg-emerald-600/80 backdrop-blur-xl text-white px-3 py-1 rounded-full text-xs flex items-center space-x-2 border border-white/20">
            <div className="flex space-x-1">
              <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce"></div>
              <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
            </div>
            <span className="font-medium">AI Composing...</span>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-5px);
          }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </div>
  )
}

export default function EmailPreview() {
  return (
    <div className="w-full h-full bg-gradient-to-br from-gray-900 via-emerald-900 to-blue-900">
      <EmailComposerIcon />
    </div>
  )
} 