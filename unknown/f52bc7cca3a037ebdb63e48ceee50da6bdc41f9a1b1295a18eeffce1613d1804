#!/usr/bin/env node

/**
 * Script to reset user quotas
 * Usage: node scripts/reset-quotas.mjs [userId] [quotaType]
 * Usage: node scripts/reset-quotas.mjs --all (resets all quotas for all users)
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const COLORS = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m'
};

function colorize(text, color) {
  return `${color}${text}${COLORS.RESET}`;
}

async function resetUserQuotas(userId, quotaType = null) {
  try {
    if (quotaType) {
      // Reset specific quota type
      const result = await prisma.userQuota.updateMany({
        where: {
          userId: userId,
          quotaType: quotaType
        },
        data: {
          used: 0,
          resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
        }
      });

      console.log(colorize(`✅ Reset ${quotaType} quota for user ${userId}`, COLORS.GREEN));
      return result.count;
    } else {
      // Reset all quotas for user
      const result = await prisma.userQuota.updateMany({
        where: {
          userId: userId
        },
        data: {
          used: 0,
          resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
        }
      });

      console.log(colorize(`✅ Reset all quotas for user ${userId}`, COLORS.GREEN));
      return result.count;
    }
  } catch (error) {
    console.error(colorize(`❌ Error resetting quotas: ${error.message}`, COLORS.RED));
    return 0;
  }
}

async function resetAllQuotas() {
  try {
    const result = await prisma.userQuota.updateMany({
      data: {
        used: 0,
        resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
      }
    });

    console.log(colorize(`✅ Reset all quotas for all users (${result.count} records)`, COLORS.GREEN));
    return result.count;
  } catch (error) {
    console.error(colorize(`❌ Error resetting all quotas: ${error.message}`, COLORS.RED));
    return 0;
  }
}

async function listUserQuotas(userId) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        quotas: true,
        subscription: true
      }
    });

    if (!user) {
      console.log(colorize(`❌ User ${userId} not found`, COLORS.RED));
      return;
    }

    const plan = user.subscription?.plan || 'free';
    
    console.log(colorize(`\n📊 Quotas for user: ${user.email} (${plan} plan)`, COLORS.CYAN));
    console.log(colorize('=' .repeat(60), COLORS.BLUE));

    if (user.quotas.length === 0) {
      console.log(colorize('No quotas found for this user', COLORS.YELLOW));
      return;
    }

    user.quotas.forEach(quota => {
      const percentage = quota.totalLimit === -1 ? 0 : (quota.used / quota.totalLimit) * 100;
      const limitText = quota.totalLimit === -1 ? 'Unlimited' : quota.totalLimit.toString();
      const status = quota.totalLimit === -1 ? 'Unlimited' : 
                    quota.used >= quota.totalLimit ? 'Exceeded' : 'Available';
      
      console.log(colorize(`\n📝 ${quota.quotaType}:`, COLORS.MAGENTA));
      console.log(colorize(`   Used: ${quota.used}/${limitText}`, COLORS.WHITE));
      console.log(colorize(`   Status: ${status}`, 
        status === 'Exceeded' ? COLORS.RED : 
        status === 'Unlimited' ? COLORS.GREEN : COLORS.YELLOW));
      console.log(colorize(`   Reset Date: ${quota.resetDate.toISOString().split('T')[0]}`, COLORS.BLUE));
      
      if (quota.totalLimit !== -1) {
        console.log(colorize(`   Usage: ${percentage.toFixed(1)}%`, COLORS.CYAN));
      }
    });
  } catch (error) {
    console.error(colorize(`❌ Error listing quotas: ${error.message}`, COLORS.RED));
  }
}

async function getAllUsers() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        subscription: {
          select: {
            plan: true
          }
        }
      }
    });

    console.log(colorize('\n👥 Available Users:', COLORS.CYAN));
    console.log(colorize('=' .repeat(40), COLORS.BLUE));

    users.forEach((user, index) => {
      const plan = user.subscription?.plan || 'free';
      console.log(colorize(`${index + 1}. ${user.email} (${plan}) - ID: ${user.id}`, COLORS.WHITE));
    });

    return users;
  } catch (error) {
    console.error(colorize(`❌ Error listing users: ${error.message}`, COLORS.RED));
    return [];
  }
}

async function main() {
  console.log(colorize('🔄 Quota Reset Tool', COLORS.CYAN));
  console.log(colorize('=' .repeat(30), COLORS.BLUE));

  const args = process.argv.slice(2);

  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    console.log(colorize('\nUsage:', COLORS.YELLOW));
    console.log(colorize('  node scripts/reset-quotas.mjs --all', COLORS.WHITE));
    console.log(colorize('  node scripts/reset-quotas.mjs --list-users', COLORS.WHITE));
    console.log(colorize('  node scripts/reset-quotas.mjs <userId>', COLORS.WHITE));
    console.log(colorize('  node scripts/reset-quotas.mjs <userId> <quotaType>', COLORS.WHITE));
    console.log(colorize('  node scripts/reset-quotas.mjs <userId> --list', COLORS.WHITE));
    
    console.log(colorize('\nQuota Types:', COLORS.YELLOW));
    console.log(colorize('  - invincible_research', COLORS.WHITE));
    console.log(colorize('  - blog_posts', COLORS.WHITE));
    console.log(colorize('  - emails', COLORS.WHITE));
    console.log(colorize('  - social_media', COLORS.WHITE));
    console.log(colorize('  - youtube_scripts', COLORS.WHITE));

    console.log(colorize('\nExamples:', COLORS.YELLOW));
    console.log(colorize('  node scripts/reset-quotas.mjs --all', COLORS.WHITE));
    console.log(colorize('  node scripts/reset-quotas.mjs user123 invincible_research', COLORS.WHITE));
    return;
  }

  if (args[0] === '--all') {
    console.log(colorize('\n🔄 Resetting all quotas for all users...', COLORS.YELLOW));
    const count = await resetAllQuotas();
    console.log(colorize(`\n✅ Successfully reset ${count} quota records`, COLORS.GREEN));
    return;
  }

  if (args[0] === '--list-users') {
    await getAllUsers();
    return;
  }

  const userId = args[0];
  const quotaTypeOrAction = args[1];

  if (quotaTypeOrAction === '--list') {
    await listUserQuotas(userId);
    return;
  }

  if (quotaTypeOrAction) {
    // Reset specific quota type
    console.log(colorize(`\n🔄 Resetting ${quotaTypeOrAction} quota for user ${userId}...`, COLORS.YELLOW));
    const count = await resetUserQuotas(userId, quotaTypeOrAction);
    if (count > 0) {
      console.log(colorize(`\n✅ Successfully reset quota`, COLORS.GREEN));
    } else {
      console.log(colorize(`\n⚠️ No quota found to reset`, COLORS.YELLOW));
    }
  } else {
    // Reset all quotas for user
    console.log(colorize(`\n🔄 Resetting all quotas for user ${userId}...`, COLORS.YELLOW));
    const count = await resetUserQuotas(userId);
    if (count > 0) {
      console.log(colorize(`\n✅ Successfully reset ${count} quota records`, COLORS.GREEN));
    } else {
      console.log(colorize(`\n⚠️ No quotas found to reset`, COLORS.YELLOW));
    }
  }

  // Show updated quotas
  console.log(colorize('\n📊 Updated quotas:', COLORS.CYAN));
  await listUserQuotas(userId);
}

main()
  .catch(error => {
    console.error(colorize(`💥 Fatal error: ${error.message}`, COLORS.RED));
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 