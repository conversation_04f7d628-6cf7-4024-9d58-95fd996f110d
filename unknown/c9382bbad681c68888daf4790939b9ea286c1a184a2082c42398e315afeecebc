// NicheComponentConnector.ts - Enhanced Niche-Component Integration System

import { 
  ArticleType, 
  NicheProfile, 
  ARTICLE_TYPES, 
  NICHES 
} from './ArticleNichePatterns';

export interface NicheComponentMapping {
  nicheId: string;
  niche: NicheProfile;
  compatibleArticleTypes: ArticleType[];
  preferredComponents: {
    visualElements: string[];
    engagementTechniques: string[];
    structuralComponents: string[];
    seoComponents: string[];
  };
  contentPatterns: {
    introPatterns: string[];
    bodyPatterns: string[];
    conclusionPatterns: string[];
    transitionPhrases: string[];
  };
  vocabularyBank: {
    nicheSpecificTerms: string[];
    powerWords: string[];
    technicalJargon: string[];
    commonPhrases: string[];
  };
  audienceAlignment: {
    demographics: string[];
    painPoints: string[];
    desires: string[];
    behaviorPatterns: string[];
  };
  performanceMetrics: {
    avgEngagementRate: number;
    conversionPotential: number;
    viralityScore: number;
    retentionScore: number;
  };
}

export class NicheComponentConnector {
  private nicheComponentMappings: Map<string, NicheComponentMapping> = new Map();
  
  constructor() {
    this.initializeNicheComponentMappings();
  }

  /**
   * Initialize comprehensive niche-component mappings
   */
  private initializeNicheComponentMappings(): void {
    NICHES.forEach(niche => {
      const mapping = this.createNicheComponentMapping(niche);
      this.nicheComponentMappings.set(niche.id, mapping);
    });
  }

  /**
   * Create detailed niche-component mapping for a specific niche
   */
  private createNicheComponentMapping(niche: NicheProfile): NicheComponentMapping {
    // Get compatible article types for this niche
    const compatibleArticleTypes = ARTICLE_TYPES.filter(articleType => 
      niche.preferredArticleTypes.includes(articleType.id) ||
      this.isArticleTypeCompatible(niche, articleType)
    );

    // Generate niche-specific components
    const preferredComponents = this.generatePreferredComponents(niche);
    const contentPatterns = this.generateContentPatterns(niche);
    const vocabularyBank = this.generateVocabularyBank(niche);
    const performanceMetrics = this.calculatePerformanceMetrics(niche, compatibleArticleTypes);

    return {
      nicheId: niche.id,
      niche,
      compatibleArticleTypes,
      preferredComponents,
      contentPatterns,
      vocabularyBank,
      audienceAlignment: {
        demographics: niche.targetAudience.demographics,
        painPoints: niche.targetAudience.painPoints,
        desires: niche.targetAudience.desires,
        behaviorPatterns: this.inferBehaviorPatterns(niche)
      },
      performanceMetrics
    };
  }

  /**
   * Check if an article type is compatible with a niche
   */
  private isArticleTypeCompatible(niche: NicheProfile, articleType: ArticleType): boolean {
    // Technology niches work well with how-to guides and tutorials
    if (niche.category === 'Technology' && 
        ['how-to-guide', 'tutorial', 'troubleshooting-guide', 'tool-review'].includes(articleType.id)) {
      return true;
    }
    
    // Health niches work well with educational content
    if (niche.category === 'Health' && 
        ['beginner-guide', 'myth-busting', 'research-report', 'what-is-explainer'].includes(articleType.id)) {
      return true;
    }
    
    // Business/Finance niches work well with case studies and analysis
    if (['Business', 'Finance'].includes(niche.category) && 
        ['case-study', 'industry-analysis', 'success-story', 'comparison-post'].includes(articleType.id)) {
      return true;
    }
    
    // Lifestyle niches work well with personal content
    if (niche.category === 'Lifestyle' && 
        ['personal-story', 'challenge-series', 'diy-project', 'seasonal-content'].includes(articleType.id)) {
      return true;
    }
    
    return false;
  }

  /**
   * Generate preferred components based on niche characteristics
   */
  private generatePreferredComponents(niche: NicheProfile): NicheComponentMapping['preferredComponents'] {
    const components = {
      visualElements: [] as string[],
      engagementTechniques: [] as string[],
      structuralComponents: [] as string[],
      seoComponents: [] as string[]
    };

    // Technology niches
    if (niche.category === 'Technology') {
      components.visualElements = ['code snippets', 'screenshots', 'workflow diagrams', 'before/after comparisons'];
      components.engagementTechniques = ['hands-on examples', 'interactive demos', 'real-world applications', 'problem-solving scenarios'];
      components.structuralComponents = ['step-by-step sections', 'prerequisite boxes', 'troubleshooting sections', 'resource links'];
      components.seoComponents = ['technical keywords', 'tool comparisons', 'version-specific content', 'implementation guides'];
    }
    
    // Health niches
    else if (niche.category === 'Health') {
      components.visualElements = ['infographics', 'progress charts', 'symptom checklists', 'before/after photos'];
      components.engagementTechniques = ['personal testimonials', 'expert quotes', 'myth-busting', 'scientific explanations'];
      components.structuralComponents = ['safety disclaimers', 'expert credentials', 'research citations', 'action plans'];
      components.seoComponents = ['condition-specific keywords', 'treatment options', 'prevention tips', 'wellness goals'];
    }
    
    // Business/Finance niches
    else if (['Business', 'Finance'].includes(niche.category)) {
      components.visualElements = ['charts and graphs', 'ROI calculations', 'case study timelines', 'market data'];
      components.engagementTechniques = ['success metrics', 'failure analysis', 'strategic insights', 'industry trends'];
      components.structuralComponents = ['executive summaries', 'key takeaways', 'action items', 'resource recommendations'];
      components.seoComponents = ['industry terminology', 'market segments', 'business goals', 'financial metrics'];
    }
    
    // Lifestyle niches
    else if (niche.category === 'Lifestyle') {
      components.visualElements = ['lifestyle photos', 'transformation galleries', 'product showcases', 'inspiration boards'];
      components.engagementTechniques = ['personal stories', 'community challenges', 'reader polls', 'social sharing'];
      components.structuralComponents = ['personal anecdotes', 'tip sections', 'resource lists', 'community features'];
      components.seoComponents = ['lifestyle keywords', 'trend-based content', 'seasonal relevance', 'personal branding'];
    }
    
    // Default components for other categories
    else {
      components.visualElements = ['supporting images', 'data visualizations', 'concept diagrams', 'example showcases'];
      components.engagementTechniques = ['storytelling', 'examples', 'analogies', 'interactive elements'];
      components.structuralComponents = ['clear headings', 'bullet points', 'summary boxes', 'call-to-actions'];
      components.seoComponents = ['relevant keywords', 'topic clusters', 'semantic terms', 'trending phrases'];
    }

    return components;
  }

  /**
   * Generate content patterns specific to the niche
   */
  private generateContentPatterns(niche: NicheProfile): NicheComponentMapping['contentPatterns'] {
    const basePatterns = {
      introPatterns: [
        `Welcome to the ${niche.name} community`,
        `If you're interested in ${niche.contentThemes[0]}, you're in the right place`,
        `Today we're diving deep into ${niche.contentThemes[0]}`
      ],
      bodyPatterns: [
        `Let's explore how ${niche.contentThemes[0]} can transform your approach`,
        `The key to success in ${niche.name} is understanding these principles`,
        `Most people don't realize that ${niche.contentThemes[0]} requires this specific approach`
      ],
      conclusionPatterns: [
        `That's how you can excel in ${niche.name}`,
        `Remember, ${niche.contentThemes[0]} is a journey, not a destination`,
        `Apply these ${niche.name} strategies and watch your results improve`
      ],
      transitionPhrases: [
        `Speaking of ${niche.contentThemes[0]}`,
        `This brings us to another crucial aspect of ${niche.name}`,
        `Now that you understand ${niche.contentThemes[0]}, let's move on to`
      ]
    };

    // Customize patterns based on niche category
    if (niche.category === 'Technology') {
      basePatterns.introPatterns.push(
        'Let me show you the latest breakthrough in this field',
        'Here\'s what the tech community is buzzing about'
      );
    } else if (niche.category === 'Health') {
      basePatterns.introPatterns.push(
        'Your health journey starts with understanding this',
        'Let\'s separate fact from fiction in health advice'
      );
    }

    return basePatterns;
  }

  /**
   * Generate vocabulary bank specific to the niche
   */
  private generateVocabularyBank(niche: NicheProfile): NicheComponentMapping['vocabularyBank'] {
    return {
      nicheSpecificTerms: niche.keywords.primary,
      powerWords: this.generatePowerWords(niche),
      technicalJargon: this.generateTechnicalJargon(niche),
      commonPhrases: this.generateCommonPhrases(niche)
    };
  }

  /**
   * Generate power words for the niche
   */
  private generatePowerWords(niche: NicheProfile): string[] {
    const basePowerWords = ['revolutionary', 'breakthrough', 'proven', 'cutting-edge', 'innovative'];
    
    // Add niche-specific power words
    if (niche.category === 'Technology') {
      basePowerWords.push('disruptive', 'next-generation', 'automated', 'intelligent', 'scalable');
    } else if (niche.category === 'Health') {
      basePowerWords.push('transformative', 'healing', 'rejuvenating', 'life-changing', 'empowering');
    } else if (['Business', 'Finance'].includes(niche.category)) {
      basePowerWords.push('profitable', 'strategic', 'competitive', 'lucrative', 'game-changing');
    }
    
    return basePowerWords;
  }

  /**
   * Generate technical jargon for the niche
   */
  private generateTechnicalJargon(niche: NicheProfile): string[] {
    return niche.keywords.secondary;
  }

  /**
   * Generate common phrases for the niche
   */
  private generateCommonPhrases(niche: NicheProfile): string[] {
    const phrases = [
      `in the ${niche.name} space`,
      `${niche.name} experts recommend`,
      `according to ${niche.name} research`,
      `the ${niche.name} community believes`
    ];
    
    return phrases;
  }

  /**
   * Infer behavior patterns from niche data
   */
  private inferBehaviorPatterns(niche: NicheProfile): string[] {
    const patterns = [];
    
    if (niche.competitionLevel === 'high') {
      patterns.push('highly engaged audience', 'seeks authoritative content', 'compares multiple sources');
    }
    
    if (niche.growthRate > 0.15) {
      patterns.push('early adopters', 'trend followers', 'innovation seekers');
    }
    
    patterns.push('community-driven', 'values expertise', 'shares valuable content');
    
    return patterns;
  }

  /**
   * Calculate performance metrics for niche-article type combinations
   */
  private calculatePerformanceMetrics(niche: NicheProfile, articleTypes: ArticleType[]): NicheComponentMapping['performanceMetrics'] {
    const avgEngagement = articleTypes.reduce((sum, type) => sum + type.metrics.avgEngagementRate, 0) / articleTypes.length;
    const avgConversion = articleTypes.reduce((sum, type) => sum + type.metrics.typicalConversionRate, 0) / articleTypes.length;
    
    // Calculate virality score based on niche characteristics
    let viralityScore = 50; // Base score
    if (niche.competitionLevel === 'low') viralityScore += 20;
    if (niche.growthRate > 0.15) viralityScore += 15;
    if (niche.targetAudience.psychographics.includes('Early adopters')) viralityScore += 10;
    
    // Calculate retention score
    let retentionScore = 60; // Base score
    if (niche.contentThemes.length > 5) retentionScore += 10; // More topics = more content variety
    if (niche.targetAudience.desires.includes('Educational value')) retentionScore += 15;
    
    return {
      avgEngagementRate: avgEngagement,
      conversionPotential: avgConversion * 100,
      viralityScore: Math.min(100, viralityScore),
      retentionScore: Math.min(100, retentionScore)
    };
  }

  /**
   * Get niche-component mapping for a specific niche
   */
  public getNicheComponentMapping(nicheId: string): NicheComponentMapping | undefined {
    return this.nicheComponentMappings.get(nicheId);
  }

  /**
   * Get all niche-component mappings
   */
  public getAllNicheComponentMappings(): NicheComponentMapping[] {
    return Array.from(this.nicheComponentMappings.values());
  }

  /**
   * Find best article types for a niche
   */
  public getBestArticleTypesForNiche(nicheId: string): ArticleType[] {
    const mapping = this.nicheComponentMappings.get(nicheId);
    if (!mapping) return [];
    
    return mapping.compatibleArticleTypes.sort((a, b) => 
      b.metrics.avgEngagementRate - a.metrics.avgEngagementRate
    );
  }

  /**
   * Get optimal content structure for niche-article type combination
   */
  public getOptimalContentStructure(nicheId: string, articleTypeId: string): {
    structure: any;
    recommendations: string[];
    expectedPerformance: any;
  } | null {
    const mapping = this.nicheComponentMappings.get(nicheId);
    const articleType = ARTICLE_TYPES.find(type => type.id === articleTypeId);
    
    if (!mapping || !articleType) return null;
    
    return {
      structure: {
        introduction: {
          patterns: mapping.contentPatterns.introPatterns,
          components: mapping.preferredComponents.structuralComponents.slice(0, 2),
          vocabulary: mapping.vocabularyBank.powerWords.slice(0, 5)
        },
        body: {
          patterns: mapping.contentPatterns.bodyPatterns,
          visualElements: mapping.preferredComponents.visualElements,
          engagementTechniques: mapping.preferredComponents.engagementTechniques
        },
        conclusion: {
          patterns: mapping.contentPatterns.conclusionPatterns,
          callToActions: this.generateCTAs(mapping.niche),
          nextSteps: this.generateNextSteps(mapping.niche)
        }
      },
      recommendations: [
        `Use ${mapping.vocabularyBank.nicheSpecificTerms.slice(0, 3).join(', ')} terminology`,
        `Target ${mapping.audienceAlignment.demographics.join(' and ')} demographics`,
        `Address pain points: ${mapping.audienceAlignment.painPoints.slice(0, 2).join(', ')}`,
        `Appeal to desires: ${mapping.audienceAlignment.desires.slice(0, 2).join(', ')}`
      ],
      expectedPerformance: {
        engagementRate: `${mapping.performanceMetrics.avgEngagementRate.toFixed(1)}%`,
        conversionPotential: `${mapping.performanceMetrics.conversionPotential.toFixed(1)}%`,
        viralityScore: `${mapping.performanceMetrics.viralityScore}/100`,
        retentionScore: `${mapping.performanceMetrics.retentionScore}/100`
      }
    };
  }

  /**
   * Generate call-to-actions specific to the niche
   */
  private generateCTAs(niche: NicheProfile): string[] {
    const baseCTAs = [
      'Start your journey today',
      'Take action now',
      'Join the community'
    ];
    
    // Add niche-specific CTAs
    if (niche.category === 'Technology') {
      baseCTAs.push('Try the tool yourself', 'Download the software', 'Start building');
    } else if (niche.category === 'Health') {
      baseCTAs.push('Consult your doctor', 'Start your wellness journey', 'Take control of your health');
    }
    
    return baseCTAs;
  }

  /**
   * Generate next steps specific to the niche
   */
  private generateNextSteps(niche: NicheProfile): string[] {
    return [
      `Explore more ${niche.name} content`,
      `Connect with the ${niche.name} community`,
      `Apply these ${niche.contentThemes[0]} strategies`,
      `Share your ${niche.name} success stories`
    ];
  }

  /**
   * Get niche compatibility score for an article type
   */
  public getNicheCompatibilityScore(nicheId: string, articleTypeId: string): number {
    const mapping = this.nicheComponentMappings.get(nicheId);
    const articleType = mapping?.compatibleArticleTypes.find(type => type.id === articleTypeId);
    
    if (!mapping || !articleType) return 0;
    
    // Calculate compatibility based on multiple factors
    let score = 50; // Base compatibility
    
    // Boost score if it's a preferred article type
    if (mapping.niche.preferredArticleTypes.includes(articleTypeId)) {
      score += 30;
    }
    
    // Boost based on article type metrics
    score += articleType.metrics.avgEngagementRate * 20;
    
    // Boost based on niche growth rate
    score += mapping.niche.growthRate * 50;
    
    return Math.min(100, score);
  }
}

// Export singleton instance
export const nicheComponentConnector = new NicheComponentConnector(); 