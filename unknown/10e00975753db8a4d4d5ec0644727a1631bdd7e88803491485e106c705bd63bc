#!/usr/bin/env node

import { PrismaClient } from '@prisma/client'
import { config } from 'dotenv'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Load environment variables
config({ path: join(__dirname, '..', '.env.local') })

const prisma = new PrismaClient()

async function checkUserData() {
  try {
    console.log('🔍 Checking user data in database...\n')
    
    // Get all users with their related data
    const users = await prisma.user.findMany({
      include: {
        accounts: true,
        sessions: true,
        settings: true,
        subscription: true,
        quotas: true,
        _count: {
          select: {
            content: true,
            usageHistory: true
          }
        }
      }
    })

    if (users.length === 0) {
      console.log('❌ No users found in database')
      return
    }

    console.log(`✅ Found ${users.length} user(s) in database:\n`)

    users.forEach((user, index) => {
      console.log(`👤 User ${index + 1}:`)
      console.log(`   ID: ${user.id}`)
      console.log(`   Name: ${user.name}`)
      console.log(`   Email: ${user.email}`)
      console.log(`   First Name: ${user.firstName || 'Not set'}`)
      console.log(`   Last Name: ${user.lastName || 'Not set'}`)
      console.log(`   Image: ${user.image ? 'Set' : 'Not set'}`)
      console.log(`   Created: ${user.createdAt}`)
      
      console.log(`\n🔗 Accounts (${user.accounts.length}):`)
      user.accounts.forEach(account => {
        console.log(`   - ${account.provider}: ${account.providerAccountId}`)
      })
      
      console.log(`\n⚙️ Settings: ${user.settings ? 'Created' : 'Missing'}`)
      if (user.settings) {
        console.log(`   - Language: ${user.settings.defaultLanguage}`)
        console.log(`   - Timezone: ${user.settings.timezone}`)
        console.log(`   - Theme: ${user.settings.theme}`)
      }
      
      console.log(`\n💳 Subscription: ${user.subscription ? 'Created' : 'Missing'}`)
      if (user.subscription) {
        console.log(`   - Plan: ${user.subscription.plan}`)
        console.log(`   - Status: ${user.subscription.status}`)
      }
      
      console.log(`\n📊 Quotas (${user.quotas.length}):`)
      user.quotas.forEach(quota => {
        console.log(`   - ${quota.quotaType}: ${quota.used}/${quota.totalLimit} (resets: ${quota.resetDate.toLocaleDateString()})`)
      })
      
      console.log(`\n📈 Usage:`)
      console.log(`   - Content pieces: ${user._count.content}`)
      console.log(`   - Usage history: ${user._count.usageHistory}`)
      
      console.log('\n' + '='.repeat(50) + '\n')
    })

    // Check for any orphaned data
    const orphanedSettings = await prisma.userSettings.count({
      where: {
        user: null
      }
    })
    
    const orphanedQuotas = await prisma.userQuota.count({
      where: {
        user: null
      }
    })

    if (orphanedSettings > 0 || orphanedQuotas > 0) {
      console.log('⚠️ Found orphaned data:')
      if (orphanedSettings > 0) console.log(`   - ${orphanedSettings} orphaned settings`)
      if (orphanedQuotas > 0) console.log(`   - ${orphanedQuotas} orphaned quotas`)
    } else {
      console.log('✅ No orphaned data found')
    }

  } catch (error) {
    console.error('❌ Error checking user data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUserData() 