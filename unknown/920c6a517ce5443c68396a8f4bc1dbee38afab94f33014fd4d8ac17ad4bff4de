'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { 
  User,
  Settings,
  LogOut,
  Crown,
  BarChart3,
  Clock,
  ChevronDown,
  Bell,
  Shield,
  HelpCircle,
  Palette,
  CreditCard,
  Activity,
  TrendingUp
} from 'lucide-react'

interface UserProfile {
  id: string
  name: string | null
  email: string | null
  image: string | null
  firstName: string | null
  lastName: string | null
  bio: string | null
  subscription?: {
    plan: string
    status: string
  }
  stats?: {
    totalContent: number
    totalUsage: number
  }
}

interface ProfileButtonProps {
  userProfile: UserProfile | null
  className?: string
  showDropdown?: boolean
}

export default function ProfileButton({ userProfile, className = "", showDropdown = true }: ProfileButtonProps) {
  const { data: session } = useSession()
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const getUserInitials = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName[0]}${userProfile.lastName[0]}`
    } else if (userProfile?.name) {
      const names = userProfile.name.split(' ')
      return names.length > 1 ? `${names[0][0]}${names[names.length - 1][0]}` : names[0][0]
    } else if (userProfile?.email) {
      return userProfile.email[0].toUpperCase()
    }
    return 'U'
  }

  const getDisplayName = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName} ${userProfile.lastName}`
    } else if (userProfile?.name) {
      return userProfile.name
    } else if (userProfile?.email) {
      return userProfile.email.split('@')[0]
    }
    return 'User'
  }

  const getSubscriptionDisplay = () => {
    return userProfile?.subscription?.plan === 'free' ? 'Free Plan' : 
           userProfile?.subscription?.plan === 'pro' ? 'Pro Member' : 'Member'
  }

  const getSubscriptionColor = () => {
    return userProfile?.subscription?.plan === 'free' ? 'text-gray-400' : 
           userProfile?.subscription?.plan === 'pro' ? 'text-yellow-400' : 'text-blue-400'
  }

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/login' })
  }

  if (!userProfile) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className="w-10 h-10 bg-gray-600 rounded-xl animate-pulse" />
        <div className="space-y-1">
          <div className="w-20 h-3 bg-gray-600 rounded animate-pulse" />
          <div className="w-16 h-2 bg-gray-700 rounded animate-pulse" />
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <motion.button
        onClick={() => showDropdown && setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center space-x-3 p-2 rounded-xl hover:bg-white/5 transition-all group"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {/* Avatar */}
        <div className="relative">
          {userProfile.image ? (
            <img
              src={userProfile.image}
              alt={getDisplayName()}
              className="w-10 h-10 rounded-xl object-cover border-2 border-white/20 group-hover:border-violet-400/50 transition-colors"
            />
          ) : (
            <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-xl flex items-center justify-center text-white font-semibold group-hover:from-violet-400 group-hover:to-indigo-400 transition-all">
              {getUserInitials()}
            </div>
          )}
          {/* Online indicator */}
          <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-emerald-400 border-2 border-gray-900 rounded-full" />
        </div>

        {/* User Info */}
        <div className="text-left">
          <p className="text-sm font-medium text-white group-hover:text-violet-300 transition-colors">
            {getDisplayName()}
          </p>
          <div className="flex items-center space-x-1">
            <Crown className={`w-3 h-3 ${getSubscriptionColor()}`} />
            <p className={`text-xs ${getSubscriptionColor()}`}>
              {getSubscriptionDisplay()}
            </p>
          </div>
        </div>

        {/* Dropdown Arrow */}
        {showDropdown && (
          <motion.div
            animate={{ rotate: isDropdownOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" />
          </motion.div>
        )}
      </motion.button>

      {/* Dropdown Menu */}
      {showDropdown && (
        <AnimatePresence>
          {isDropdownOpen && (
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full right-0 mt-2 w-80 bg-black/90 backdrop-blur-xl border border-white/10 rounded-2xl shadow-2xl z-50"
            >
              {/* Profile Header */}
              <div className="p-4 border-b border-white/10">
                <div className="flex items-center space-x-3">
                  {userProfile.image ? (
                    <img
                      src={userProfile.image}
                      alt={getDisplayName()}
                      className="w-12 h-12 rounded-xl object-cover border-2 border-white/20"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-xl flex items-center justify-center text-white font-semibold text-lg">
                      {getUserInitials()}
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="font-semibold text-white">{getDisplayName()}</h3>
                    <p className="text-sm text-gray-400">{userProfile.email}</p>
                    <div className="flex items-center space-x-1 mt-1">
                      <Crown className={`w-3 h-3 ${getSubscriptionColor()}`} />
                      <span className={`text-xs ${getSubscriptionColor()}`}>
                        {getSubscriptionDisplay()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="p-4 border-b border-white/10">
                <div className="grid grid-cols-3 gap-3">
                  <div className="text-center">
                    <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-1">
                      <BarChart3 className="w-4 h-4 text-blue-400" />
                    </div>
                    <p className="text-xs text-gray-400">Content</p>
                    <p className="text-sm font-semibold text-white">{userProfile.stats?.totalContent || 0}</p>
                  </div>
                  <div className="text-center">
                    <div className="w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center mx-auto mb-1">
                      <TrendingUp className="w-4 h-4 text-emerald-400" />
                    </div>
                    <p className="text-xs text-gray-400">Quality</p>
                    <p className="text-sm font-semibold text-white">9.8/10</p>
                  </div>
                  <div className="text-center">
                    <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-1">
                      <Clock className="w-4 h-4 text-purple-400" />
                    </div>
                    <p className="text-xs text-gray-400">Saved</p>
                    <p className="text-sm font-semibold text-white">48h</p>
                  </div>
                </div>
              </div>

              {/* Menu Items */}
              <div className="p-2">
                <Link href="/profile">
                  <motion.button
                    whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                    className="w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                      <User className="w-4 h-4 text-blue-400" />
                    </div>
                    <div>
                      <p className="text-white font-medium">View Profile</p>
                      <p className="text-xs text-gray-400">Manage your profile information</p>
                    </div>
                  </motion.button>
                </Link>

                <Link href="/settings">
                  <motion.button
                    whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                    className="w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors"
                    onClick={() => setIsDropdownOpen(false)}
                  >
                    <div className="w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center">
                      <Settings className="w-4 h-4 text-emerald-400" />
                    </div>
                    <div>
                      <p className="text-white font-medium">Settings</p>
                      <p className="text-xs text-gray-400">Preferences and configuration</p>
                    </div>
                  </motion.button>
                </Link>

                <motion.button
                  whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                  className="w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors"
                >
                  <div className="w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                    <Bell className="w-4 h-4 text-yellow-400" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Notifications</p>
                    <p className="text-xs text-gray-400">Manage your alerts</p>
                  </div>
                  <div className="ml-auto">
                    <div className="w-2 h-2 bg-violet-500 rounded-full" />
                  </div>
                </motion.button>

                {userProfile.subscription?.plan === 'free' && (
                  <motion.button
                    whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                    className="w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors"
                  >
                    <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-lg flex items-center justify-center">
                      <Crown className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <p className="text-white font-medium">Upgrade to Pro</p>
                      <p className="text-xs text-gray-400">Unlock premium features</p>
                    </div>
                  </motion.button>
                )}

                <motion.button
                  whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.05)' }}
                  className="w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors"
                >
                  <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <HelpCircle className="w-4 h-4 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-white font-medium">Help & Support</p>
                    <p className="text-xs text-gray-400">Get help and documentation</p>
                  </div>
                </motion.button>
              </div>

              {/* Sign Out */}
              <div className="p-2 border-t border-white/10">
                <motion.button
                  whileHover={{ backgroundColor: 'rgba(239, 68, 68, 0.1)' }}
                  onClick={handleSignOut}
                  className="w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors"
                >
                  <div className="w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center">
                    <LogOut className="w-4 h-4 text-red-400" />
                  </div>
                  <div>
                    <p className="text-red-400 font-medium">Sign Out</p>
                    <p className="text-xs text-gray-400">Sign out of your account</p>
                  </div>
                </motion.button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      )}
    </div>
  )
} 