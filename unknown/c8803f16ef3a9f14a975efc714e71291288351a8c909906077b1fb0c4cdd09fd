#!/usr/bin/env node

/**
 * Demo script showing the Unlimited Retry Enhancement
 * Shows before/after comparison and benefits
 * Usage: node scripts/demo-unlimited-retries.mjs
 */

const COLORS = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  RED: '\x1b[31m',
  GRE<PERSON>: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m'
};

function colorize(text, color) {
  return `${color}${text}${COLORS.RESET}`;
}

console.log(colorize('🔄 Unlimited Retry Enhancement - Demo', COLORS.CYAN));
console.log(colorize('=' .repeat(50), COLORS.BLUE));

console.log(colorize('\n❌ BEFORE Enhancement (Limited to 3 attempts):', COLORS.RED));
console.log(colorize('=' .repeat(45), COLORS.RED));

const beforeExample = [
  '🔍 Tavily search attempt 1/3 for: 5 best open router alternatives of 2025',
  '🔑 Using API key ending in: ...gSay',
  '❌ Tavily search attempt 1 failed: getaddrinfo ENOTFOUND api.tavily.com',
  '⚡ Retrying immediately with new key (attempt 2/3)',
  '🔍 Tavily search attempt 2/3 for: 5 best open router alternatives of 2025',
  '🔑 Using API key ending in: ...9Vq',
  '❌ Tavily search attempt 2 failed: getaddrinfo ENOTFOUND api.tavily.com',
  '⏳ Waiting 4000ms before retry...',
  '🔍 Tavily search attempt 3/3 for: 5 best open router alternatives of 2025',
  '🔑 Using API key ending in: ...3a5',
  '❌ Tavily search attempt 3 failed: getaddrinfo ENOTFOUND api.tavily.com',
  '❌ All Tavily search attempts failed',
  '🔑 Key rotation status: 10/10 keys available',
  '❌ Failed to perform Tavily search after 3 attempts'
];

beforeExample.forEach(line => {
  console.log(colorize(line, line.includes('❌') ? COLORS.RED : COLORS.WHITE));
});

console.log(colorize('\n✅ AFTER Enhancement (Uses all available keys):', COLORS.GREEN));
console.log(colorize('=' .repeat(48), COLORS.GREEN));

const afterExample = [
  '🔍 Starting Tavily search with up to 20 attempts across 10 API keys',
  '🔍 Tavily search attempt 1/20 for: 5 best open router alternatives of 2025',
  '🔑 Using API key ending in: ...gSay (10/10 keys available)',
  '❌ Tavily search attempt 1 failed: getaddrinfo ENOTFOUND api.tavily.com',
  '⚡ INSTANT ROTATION: Network/Connection error (attempt 1, trying different endpoint)',
  '🔍 Tavily search attempt 2/20 for: 5 best open router alternatives of 2025',
  '🔑 Using API key ending in: ...9Vq (10/10 keys available)',
  '❌ Tavily search attempt 2 failed: getaddrinfo ENOTFOUND api.tavily.com',
  '⚡ INSTANT ROTATION: Network/Connection error (attempt 2, trying different endpoint)',
  '🔍 Tavily search attempt 3/20 for: 5 best open router alternatives of 2025',
  '🔑 Using API key ending in: ...3a5 (10/10 keys available)',
  '❌ Tavily search attempt 3 failed: getaddrinfo ENOTFOUND api.tavily.com',
  '🔍 Tavily search attempt 4/20 for: 5 best open router alternatives of 2025',
  '🔑 Using API key ending in: ...HXW (10/10 keys available)',
  '❌ Tavily search attempt 4 failed: getaddrinfo ENOTFOUND api.tavily.com',
  '🔍 Tavily search attempt 5/20 for: 5 best open router alternatives of 2025',
  '🔑 Using API key ending in: ...YRR (10/10 keys available)',
  '❌ Persistent DNS/network error after 5 attempts. This is likely not a key issue.',
  '🔑 Current key status: 10/10 keys available',
  '❌ Network connectivity issue (ENOTFOUND): This appears to be a DNS/network problem'
];

afterExample.forEach(line => {
  if (line.includes('❌')) {
    console.log(colorize(line, COLORS.RED));
  } else if (line.includes('✅')) {
    console.log(colorize(line, COLORS.GREEN));
  } else if (line.includes('⚡')) {
    console.log(colorize(line, COLORS.YELLOW));
  } else {
    console.log(colorize(line, COLORS.WHITE));
  }
});

console.log(colorize('\n🎯 Key Improvements Made:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(30), COLORS.BLUE));

const improvements = [
  {
    category: 'Retry Logic',
    before: 'Fixed 3 attempts maximum',
    after: 'Dynamic: max(totalKeys × 2, 10) attempts',
    benefit: 'With 10 keys = 20 max attempts'
  },
  {
    category: 'Key Utilization', 
    before: 'Only used 3 keys maximum',
    after: 'Uses all available keys systematically',
    benefit: 'Dramatically higher success rates'
  },
  {
    category: 'Error Detection',
    before: 'Basic retry on any error',
    after: 'Smart categorization: network vs. key issues',
    benefit: 'Prevents wasted attempts'
  },
  {
    category: 'Early Termination',
    before: 'Always try full 3 attempts',
    after: 'Stop after 5 attempts for DNS issues',
    benefit: 'Faster failure detection'
  },
  {
    category: 'Monitoring',
    before: 'Basic attempt logging',
    after: 'Real-time key health and progress tracking',
    benefit: 'Better debugging and visibility'
  }
];

improvements.forEach((item, index) => {
  console.log(colorize(`\n${index + 1}. ${item.category}:`, COLORS.MAGENTA));
  console.log(colorize(`   Before: ${item.before}`, COLORS.RED));
  console.log(colorize(`   After:  ${item.after}`, COLORS.GREEN));
  console.log(colorize(`   Benefit: ${item.benefit}`, COLORS.CYAN));
});

console.log(colorize('\n📊 Technical Implementation:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(35), COLORS.BLUE));

console.log(colorize('\n🔧 Dynamic Retry Calculation:', COLORS.MAGENTA));
console.log(colorize('   // Get the number of available keys dynamically', COLORS.WHITE));
console.log(colorize('   const initialKeyStatus = this.keyRotator.getStatus()', COLORS.WHITE));
console.log(colorize('   const maxRetries = Math.max(initialKeyStatus.totalKeys * 2, 10)', COLORS.WHITE));
console.log(colorize('   // With 10 keys: maxRetries = Math.max(10 * 2, 10) = 20', COLORS.CYAN));

console.log(colorize('\n🧠 Smart Error Handling:', COLORS.MAGENTA));
console.log(colorize('   // Network/DNS errors - stop early if persistent', COLORS.WHITE));
console.log(colorize('   if (error.code === "ENOTFOUND" && attempt >= 5) {', COLORS.WHITE));
console.log(colorize('     throw new Error("Network connectivity issue")', COLORS.WHITE));
console.log(colorize('   }', COLORS.WHITE));

console.log(colorize('\n🔄 Intelligent Key Rotation:', COLORS.MAGENTA));
console.log(colorize('   // Quota/Auth errors - rotate immediately', COLORS.WHITE));
console.log(colorize('   if (status === 429 || errorMessage.includes("quota")) {', COLORS.WHITE));
console.log(colorize('     shouldRotateKey = true', COLORS.WHITE));
console.log(colorize('     this.keyRotator.markKeyAsQuotaExceeded(currentKey)', COLORS.WHITE));
console.log(colorize('   }', COLORS.WHITE));

console.log(colorize('\n📈 Performance Benefits:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(30), COLORS.BLUE));

const benefits = [
  'Higher Success Rates: Utilizes all 10 API keys instead of just 3',
  'Smarter Error Handling: Distinguishes network vs. key issues',
  'Resource Efficiency: Early termination for non-key problems',
  'Better Monitoring: Real-time key status and health tracking',
  'Improved Reliability: System doesn\'t give up prematurely',
  'Enhanced Debugging: Detailed error categorization and logging'
];

benefits.forEach((benefit, index) => {
  console.log(colorize(`✅ ${index + 1}. ${benefit}`, COLORS.GREEN));
});

console.log(colorize('\n🔍 Error Categories Now Handled:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(38), COLORS.BLUE));

const errorCategories = [
  {
    type: 'Quota/Rate Limit Errors',
    status: 'HTTP 429, 432',
    action: 'Instant key rotation',
    example: 'API quota exceeded'
  },
  {
    type: 'Authentication Errors',
    status: 'HTTP 401, 403',
    action: 'Mark key as expired, rotate',
    example: 'Invalid API key'
  },
  {
    type: 'Network/DNS Errors',
    status: 'ENOTFOUND, TIMEOUT',
    action: 'Try 5 attempts, then stop',
    example: 'DNS resolution failure'
  },
  {
    type: 'Server Errors',
    status: 'HTTP 500+',
    action: 'Retry with different key',
    example: 'Service temporarily unavailable'
  }
];

errorCategories.forEach((category, index) => {
  console.log(colorize(`\n${index + 1}. ${category.type}:`, COLORS.MAGENTA));
  console.log(colorize(`   Status: ${category.status}`, COLORS.WHITE));
  console.log(colorize(`   Action: ${category.action}`, COLORS.YELLOW));
  console.log(colorize(`   Example: ${category.example}`, COLORS.CYAN));
});

console.log(colorize('\n🎛️ Current Configuration:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(30), COLORS.BLUE));

console.log(colorize('📊 With 10 API Keys Available:', COLORS.MAGENTA));
console.log(colorize('   • Max Attempts: 20 (10 keys × 2)', COLORS.WHITE));
console.log(colorize('   • Network Error Limit: 5 attempts', COLORS.WHITE));
console.log(colorize('   • Key Health: Tracked per key', COLORS.WHITE));
console.log(colorize('   • Rotation: Instant for quota/auth errors', COLORS.WHITE));
console.log(colorize('   • Early Termination: Yes (for network issues)', COLORS.WHITE));

console.log(colorize('\n🧪 Testing & Validation:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(28), COLORS.BLUE));

console.log(colorize('Available Commands:', COLORS.MAGENTA));
console.log(colorize('   npm run test:retries      # Test enhanced retry logic', COLORS.WHITE));
console.log(colorize('   npm run reset:quotas      # Manage API quotas', COLORS.WHITE));
console.log(colorize('   npm run demo:simple       # Test simple writing', COLORS.WHITE));

console.log(colorize('\n📝 Files Modified:', COLORS.MAGENTA));
console.log(colorize('   • src/lib/search.ts       # Enhanced TavilySearchService', COLORS.WHITE));
console.log(colorize('   • scripts/test-unlimited-retries.mjs  # Test script', COLORS.WHITE));
console.log(colorize('   • package.json            # Added test:retries command', COLORS.WHITE));

console.log(colorize('\n🚀 Summary:', COLORS.BRIGHT));
console.log(colorize('=' .repeat(15), COLORS.BLUE));

console.log(colorize('The enhancement transforms the search system from:', COLORS.WHITE));
console.log(colorize('❌ Giving up after 3 attempts (with 10 keys available)', COLORS.RED));
console.log(colorize('                    ↓', COLORS.YELLOW));
console.log(colorize('✅ Trying up to 20 attempts across all 10 keys', COLORS.GREEN));
console.log(colorize('✅ Smart early termination for network issues', COLORS.GREEN));
console.log(colorize('✅ Intelligent key rotation based on error type', COLORS.GREEN));
console.log(colorize('✅ Real-time monitoring and health tracking', COLORS.GREEN));

console.log(colorize('\n🎉 Result: Dramatically improved success rates!', COLORS.CYAN));
console.log(colorize('The system now fully utilizes all available API keys', COLORS.WHITE));
console.log(colorize('instead of stopping prematurely after just 3 attempts.', COLORS.WHITE));
console.log(colorize('=' .repeat(60), COLORS.BLUE)); 