# Enhanced YouTube Script Generation System

## Overview

The Enhanced YouTube Script Generation system is a sophisticated AI-powered tool that creates professional, engaging YouTube scripts by analyzing competitor content, extracting insights from top-performing videos, and ensuring factual accuracy through multi-stage verification.

## Key Features

### 1. **Competitor Analysis**
- Searches for top YouTube videos on the given topic
- Extracts captions from competitor videos
- Analyzes content structure, engagement techniques, and gaps
- Identifies unique angles and successful patterns

### 2. **Multi-Model AI Workflow**
- **<PERSON>wen (via OpenRouter)**: Deep analysis and fact extraction
- **Gemini**: Creative script generation and polish
- **Tavily**: Real-time web research and fact verification

### 3. **Fact-Checking System**
- Extracts all claims and statistics from generated content
- Verifies facts through web search
- Provides confidence scores and sources
- Updates content with verified information

### 4. **Real-Time Progress Tracking**
- Server-Sent Events (SSE) for live progress updates
- Detailed progress messages at each phase
- Visual progress bar in the UI

## Workflow Architecture

```mermaid
graph TD
    A[User Input] -->|Title + Brief| B[Research Phase]
    B --> C[YouTube Video Search]
    B --> D[Caption Extraction]
    B --> E[Web Research - Tavily]
    
    C --> F[Analysis Phase - Qwen]
    D --> F
    E --> F
    
    F --> G[Script Generation - Gemini]
    
    G --> H[Fact Checking]
    H --> I[Statistics Verification - Qwen]
    H --> J[Source Validation - Tavily]
    
    I --> K[Final Polish - Gemini]
    J --> K
    
    K --> L[Final YouTube Script]
```

## API Endpoint

### POST `/api/generate/youtube`

#### Request Body
```typescript
{
  title: string;           // Video title
  brief: string;           // Detailed description of video content
  duration?: string;       // Target video duration (default: "5-10 minutes")
  style?: string;          // Video style (default: "educational")
  targetAudience?: string; // Target audience (default: "general audience")
  useAdvancedResearch?: boolean; // Enable competitor analysis (default: true)
}
```

#### Response
```typescript
{
  success: boolean;
  content: string;         // Generated script
  progressId?: string;     // Progress tracking ID
  metadata?: {
    videosAnalyzed: number;
    factsChecked: number;
    totalFacts: number;
    researchSources: number;
  };
  insights?: {
    topCompetitors: Array<{
      title: string;
      channel: string;
      views: string;
    }>;
    contentGaps: string[];
    verifiedFacts: number;
  };
  quota: {
    used: number;
    limit: number;
    remaining: number;
  };
}
```

## Detailed Phase Breakdown

### Phase 1: Research (10-30% progress)
1. **YouTube Search**: Finds top 5 videos on the topic
2. **Caption Extraction**: Extracts transcripts from top 3 videos
3. **Web Research**: Gathers current trends and statistics

### Phase 2: Analysis (30-50% progress)
1. **Competitor Analysis**: Analyzes each transcript for:
   - Key topics and subtopics
   - Engagement techniques
   - Content structure
   - Content gaps
   - Unique angles
   - Call-to-actions

2. **Insight Synthesis**: Combines findings into a comprehensive strategy

### Phase 3: Generation (50-75% progress)
1. **Script Outline**: Creates detailed structure based on analysis
2. **Initial Generation**: Produces complete script with:
   - Compelling hooks
   - Timestamps
   - Visual cues
   - Pattern interrupts
   - CTAs

### Phase 4: Fact-Checking (75-90% progress)
1. **Claim Extraction**: Identifies all verifiable statements
2. **Verification**: Searches for supporting evidence
3. **Confidence Scoring**: Rates each claim's reliability
4. **Source Attribution**: Links to credible sources

### Phase 5: Final Polish (90-100% progress)
1. **Fact Integration**: Updates script with verified information
2. **Flow Optimization**: Ensures natural, conversational tone
3. **Timestamp Verification**: Confirms timing accuracy
4. **Final Touches**: Adds polish for camera-ready delivery

## Configuration Options

### Advanced Research Mode
When enabled (default):
- Analyzes competitor videos
- Extracts captions and insights
- Performs comprehensive fact-checking
- Takes 60-90 seconds

When disabled:
- Direct generation with Gemini only
- Faster generation (20-30 seconds)
- Still includes basic research

### Video Styles
- `educational`: Teaching-focused content
- `entertainment`: Engaging, fun content
- `tutorial`: Step-by-step guides
- `review`: Product/service analysis
- `vlog`: Personal, conversational
- `presentation`: Professional, structured
- `documentary`: In-depth exploration
- `comedy`: Humor-focused
- `interview`: Q&A format

### Duration Options
- `1-3 minutes`: Short-form content
- `3-5 minutes`: Quick explainers
- `5-10 minutes`: Standard videos
- `10-15 minutes`: Detailed content
- `15-30 minutes`: Long-form
- `30+ minutes`: Extended content

## Environment Variables

```bash
# YouTube API (optional - uses mock data if not set)
YOUTUBE_API_KEY=your_youtube_api_key

# Caption extraction (optional)
SUPADATA_API_KEY=your_supadata_api_key

# Required for full functionality
OPENROUTER_API_KEY=your_openrouter_key
GEMINI_API_KEY=your_gemini_key
TAVILY_API_KEY=your_tavily_key
```

## Testing

Run the test script to verify functionality:

```bash
node scripts/test-enhanced-youtube.mjs
```

## Best Practices

1. **Title Selection**
   - Be specific and searchable
   - Include keywords your audience searches for
   - Keep it under 60 characters

2. **Brief Writing**
   - Provide detailed context
   - List key points to cover
   - Mention any specific examples or case studies
   - Include your unique perspective

3. **Audience Targeting**
   - Be specific about expertise level
   - Consider demographics
   - Think about viewer goals

4. **Duration Planning**
   - Match content depth to duration
   - Consider platform preferences
   - Factor in engagement metrics

## Performance Metrics

### With Advanced Research
- **Processing Time**: 60-90 seconds
- **Videos Analyzed**: 3-5
- **Facts Checked**: 5-10
- **Research Sources**: 10-20
- **Accuracy**: High (verified facts)

### Without Advanced Research
- **Processing Time**: 20-30 seconds
- **Videos Analyzed**: 0
- **Facts Checked**: 0
- **Research Sources**: 0
- **Accuracy**: Moderate (unverified)

## Error Handling

The system includes comprehensive error handling:
- **API Key Issues**: Falls back to mock data
- **Caption Extraction Failures**: Continues with available data
- **Fact-Check Failures**: Marks as unverified
- **Progress Updates**: Graceful SSE handling

## Future Enhancements

1. **Video Analytics Integration**
   - Engagement rate analysis
   - Retention curve optimization
   - A/B testing insights

2. **Multi-Language Support**
   - Caption extraction in multiple languages
   - Translation capabilities
   - Cultural adaptation

3. **Visual Script Elements**
   - Shot list generation
   - B-roll suggestions
   - Graphics recommendations

4. **Collaboration Features**
   - Team reviews
   - Version control
   - Comment system 