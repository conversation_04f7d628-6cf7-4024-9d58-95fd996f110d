#!/usr/bin/env node

/**
 * Test script for the new streamlined YouTube workflow
 * Tests the 6-phase process: Web scraping → YouTube captions → Phi-4 analysis → Gemini writing → Gemini fact-checking → Final script
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3000';

async function testStreamlinedWorkflow() {
  console.log('🎬 Testing Streamlined YouTube Workflow');
  console.log('=====================================');
  
  const testTopic = "How to start a successful YouTube channel in 2024";
  const testBrief = "Create a comprehensive guide for beginners who want to start a YouTube channel and grow their audience effectively.";
  
  try {
    console.log(`\n📝 Testing topic: "${testTopic}"`);
    console.log(`📋 Brief: ${testBrief}`);
    
    const requestBody = {
      title: testTopic,
      brief: testBrief,
      duration: "8-10 minutes",
      style: "educational",
      targetAudience: "aspiring YouTubers"
    };

    console.log('\n🚀 Starting YouTube script generation...');
    
    const response = await fetch(`${API_BASE}/api/generate/youtube`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API Error (${response.status}):`, errorText);
      return;
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('\n✅ Streamlined workflow completed successfully!');
      console.log('\n📊 Workflow Metadata:');
      console.log(`   - Videos Analyzed: ${result.metadata.videosAnalyzed}`);
      console.log(`   - Research Sources: ${result.metadata.researchSources}`);
      console.log(`   - Workflow Type: ${result.metadata.workflow}`);
      
      console.log('\n🔍 Workflow Phases:');
      result.metadata.phases.forEach((phase, index) => {
        console.log(`   ${index + 1}. ${phase}`);
      });
      
      console.log('\n🌐 Web Sources:');
      result.insights.webSources.forEach((source, index) => {
        console.log(`   ${index + 1}. ${source.title} (${source.source})`);
      });
      
      console.log('\n📺 YouTube Videos:');
      result.insights.youtubeVideos.forEach((video, index) => {
        console.log(`   ${index + 1}. ${video.title} - ${video.channel} (${video.views} views)`);
      });
      
      console.log('\n📚 Knowledge Base:');
      console.log(`   - Total Entries: ${result.knowledgeBase.totalEntries}`);
      console.log(`   - Entry Types:`, Object.keys(result.knowledgeBase.entriesByType).join(', '));
      
      console.log('\n📝 Generated Script Preview (first 500 chars):');
      console.log('─'.repeat(60));
      console.log(result.content.substring(0, 500) + '...');
      console.log('─'.repeat(60));
      
      console.log('\n🎯 Key Success Metrics:');
      console.log(`   ✓ Web scraping: ${result.insights.webSources.length} sources`);
      console.log(`   ✓ YouTube analysis: ${result.insights.youtubeVideos.length} videos`);
      console.log(`   ✓ Phi-4 structure analysis: Complete`);
      console.log(`   ✓ Gemini script writing: Complete`);
      console.log(`   ✓ Gemini fact-checking: Complete`);
      console.log(`   ✓ Final script generation: Complete`);
      
    } else {
      console.error('❌ Workflow failed:', result.error);
      if (result.details) {
        console.error('📄 Details:', result.details);
      }
    }

  } catch (error) {
    console.error('💥 Test failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n🔧 Make sure the development server is running:');
      console.error('   npm run dev');
    }
  }
}

// Progress monitoring function
async function monitorProgress(progressId) {
  console.log(`\n📊 Monitoring progress for: ${progressId}`);
  
  const maxAttempts = 60; // 5 minutes max
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      const response = await fetch(`${API_BASE}/api/progress/${progressId}`);
      
      if (response.ok) {
        const progress = await response.json();
        
        if (progress.status === 'completed') {
          console.log(`✅ Progress completed: ${progress.message}`);
          break;
        } else if (progress.status === 'error') {
          console.log(`❌ Progress error: ${progress.message}`);
          break;
        } else {
          console.log(`⏳ Progress: ${progress.percentage}% - ${progress.message}`);
        }
      }
      
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
      attempts++;
      
    } catch (error) {
      console.error('Progress monitoring error:', error.message);
      break;
    }
  }
}

// Test quota endpoint
async function testQuotaCheck() {
  console.log('\n🔍 Testing quota check...');
  
  try {
    const response = await fetch(`${API_BASE}/api/quota`);
    
    if (response.ok) {
      const quota = await response.json();
      console.log('📊 Quota status:', quota);
    } else {
      console.log('❌ Quota check failed - may need authentication');
    }
  } catch (error) {
    console.log('⚠️ Quota check error:', error.message);
  }
}

// Main execution
async function main() {
  console.log('🎮 YouTube Streamlined Workflow Test');
  console.log('====================================');
  
  // Test quota first
  await testQuotaCheck();
  
  // Run the main workflow test
  await testStreamlinedWorkflow();
  
  console.log('\n🏁 Test completed!');
}

main().catch(console.error); 