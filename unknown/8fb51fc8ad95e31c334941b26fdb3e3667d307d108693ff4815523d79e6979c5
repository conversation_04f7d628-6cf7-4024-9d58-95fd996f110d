'use client'

import { useRef, useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

// Compact YouTube Script Studio Preview for Dashboard Card
function VideoScriptStudio() {
  const [currentScript, setCurrentScript] = useState('')
  const [showCursor, setShowCursor] = useState(true)
  const [sceneNumber, setSceneNumber] = useState(1)
  const [videoMetrics, setVideoMetrics] = useState({
    views: 125847,
    likes: 8924,
    retention: 87.5,
    engagement: 12.3,
    duration: '12:34'
  })
  const [isRecording, setIsRecording] = useState(false)
  const [scriptProgress, setScriptProgress] = useState(0)
  const [activeSection, setActiveSection] = useState(0) // 0: intro, 1: hook, 2: content, 3: cta
  const [particles, setParticles] = useState<Array<{id: number, x: number, y: number, delay: number}>>([])
  const [glowIntensity, setGlowIntensity] = useState(0.5)
  const typewriterRef = useRef<NodeJS.Timeout | null>(null)
  const cursorRef = useRef<NodeJS.Timeout | null>(null)

  // Generate fewer particles for compact view
  useEffect(() => {
    const generateParticles = () => {
      const newParticles = Array.from({ length: 6 }, (_, i) => ({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        delay: Math.random() * 2
      }))
      setParticles(newParticles)
    }
    generateParticles()
    const interval = setInterval(generateParticles, 20000)
    return () => clearInterval(interval)
  }, [])

  // Dynamic glow effect
  useEffect(() => {
    const interval = setInterval(() => {
      setGlowIntensity(prev => 0.3 + Math.sin(Date.now() * 0.002) * 0.3)
    }, 150)
    return () => clearInterval(interval)
  }, [])

  const scriptSections = [
    {
      title: "INTRO",
      content: "[INTRO MUSIC FADES IN]\n\nHey YouTube! Welcome back to my channel! 👋\n\nIf you're new here, I'm [YOUR NAME] and I help creators...",
      timestamp: "00:00",
      color: "#ff6b6b",
      emoji: "🎬",
      bgGradient: "from-red-500/20 to-orange-500/20"
    },
    {
      title: "HOOK",
      content: "What if I told you there's a 30-second technique that can DOUBLE your video retention?\n\n[PAUSE FOR DRAMATIC EFFECT]...",
      timestamp: "00:15",
      color: "#4ecdc4",
      emoji: "⚡",
      bgGradient: "from-cyan-500/20 to-teal-500/20"
    },
    {
      title: "CONTENT",
      content: "Alright, let's dive into the good stuff! 💪\n\n[TRANSITION TO SCREEN RECORDING]\n\nStep 1: The 3-Second Rule...",
      timestamp: "00:45",
      color: "#45b7d1",
      emoji: "🎓",
      bgGradient: "from-blue-500/20 to-indigo-500/20"
    },
    {
      title: "CTA",
      content: "If this video helped you out, SMASH that like button! 👍\n\nAnd if you want more content like this, hit subscribe!",
      timestamp: "08:30",
      color: "#f093fb",
      emoji: "🎯",
      bgGradient: "from-purple-500/20 to-pink-500/20"
    }
  ]

  useEffect(() => {
    let charIndex = 0
    let isDeleting = false
    let sectionPause = false

    const typeScript = () => {
      const currentSection = scriptSections[activeSection]
      
      if (sectionPause) {
        setTimeout(() => {
          sectionPause = false
          setActiveSection((prev) => (prev + 1) % scriptSections.length)
          charIndex = 0
        }, 2000)
        return
      }
      
      if (!isDeleting) {
        setCurrentScript(currentSection.content.substring(0, charIndex + 1))
        charIndex++
        setScriptProgress((charIndex / currentSection.content.length) * 100)
        
        // Update metrics periodically
        if (charIndex % 15 === 0) {
          setVideoMetrics(prev => ({
            ...prev,
            views: prev.views + Math.floor(Math.random() * 100) + 25,
            likes: prev.likes + Math.floor(Math.random() * 8) + 1,
            retention: Math.min(96, prev.retention + Math.random() * 0.5),
            engagement: Math.min(20, prev.engagement + Math.random() * 0.3)
          }))
        }
        
        // Update scene number
        if (charIndex % 80 === 0) {
          setSceneNumber(prev => Math.min(8, prev + 1))
        }
        
        // Recording indicator
        if (charIndex % 30 === 0 && Math.random() > 0.7) {
            setIsRecording(prev => !prev)
        }
        
        if (charIndex === currentSection.content.length) {
          setTimeout(() => {
            isDeleting = true
          }, 2500)
        }
      } else {
        setCurrentScript(currentSection.content.substring(0, charIndex - 1))
        charIndex--
        setScriptProgress((charIndex / currentSection.content.length) * 100)
        
        if (charIndex === 0) {
          isDeleting = false
          sectionPause = true
        }
      }
    }

    typewriterRef.current = setInterval(typeScript, isDeleting ? 20 : 80)

    return () => {
      if (typewriterRef.current) clearInterval(typewriterRef.current)
    }
  }, [activeSection])

  useEffect(() => {
    cursorRef.current = setInterval(() => {
      setShowCursor(prev => !prev)
    }, 500)

    return () => {
      if (cursorRef.current) clearInterval(cursorRef.current)
    }
  }, [])

  return (
    <div className="relative w-full h-full flex items-center justify-center p-2 overflow-hidden">
      {/* Compact Background with reduced effects */}
      <div className="absolute inset-0 bg-[#0f0f0f] rounded-lg">
        {/* Animated gradient background */}
        <motion.div
          key={activeSection}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className={`absolute inset-0 bg-gradient-to-br ${scriptSections[activeSection].bgGradient} rounded-lg`}
        />
        
        {/* Simplified floating orb */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            x: [0, 30, 0],
            y: [0, -20, 0],
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 left-1/4 w-32 h-32 bg-[#ff0000]/20 rounded-full blur-[40px]"
          style={{ filter: `blur(40px) brightness(${glowIntensity + 0.3})` }}
        />

        {/* Compact Floating Particles */}
        <AnimatePresence>
          {particles.map((particle) => (
            <motion.div
              key={particle.id}
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: [0, 0.4, 0],
                scale: [0, 1, 0],
                x: [particle.x + '%', (particle.x + 15) + '%'],
                y: [particle.y + '%', (particle.y - 20) + '%'],
              }}
              transition={{
                duration: 6 + Math.random() * 2,
                repeat: Infinity,
                delay: particle.delay,
                ease: "easeInOut"
              }}
              className="absolute w-1 h-1 rounded-full"
              style={{ backgroundColor: scriptSections[activeSection].color }}
            />
          ))}
        </AnimatePresence>
      </div>
      
      {/* Compact Main Interface */}
      <div className="relative z-10 w-full h-full flex flex-col">
        
        {/* Compact Header */}
        <motion.div 
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-[#1a1a1a]/90 backdrop-blur-sm border border-[#303030]/50 rounded-t-lg p-2 flex-shrink-0"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <motion.div 
                animate={{ 
                  rotate: 360,
                  boxShadow: [`0 0 0px ${scriptSections[activeSection].color}`, `0 0 10px ${scriptSections[activeSection].color}40`, `0 0 0px ${scriptSections[activeSection].color}`]
                }}
                transition={{ 
                  rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                  boxShadow: { duration: 3, repeat: Infinity }
                }}
                className="w-6 h-6 bg-gradient-to-br from-[#ff0000] to-[#cc0000] rounded-lg flex items-center justify-center"
              >
                <span className="text-white font-bold text-xs">YT</span>
              </motion.div>
              <div>
                <motion.h3 
                  animate={{ 
                    color: [scriptSections[activeSection].color, '#ffffff', scriptSections[activeSection].color]
                  }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="font-bold text-xs"
                >
                  Studio Pro
                </motion.h3>
                <p className="text-[#aaa] text-[10px]">AI Script Generator</p>
              </div>
            </div>
            <div className="flex items-center space-x-1">
              <AnimatePresence>
                {isRecording && (
                  <motion.div 
                    initial={{ scale: 0 }}
                    animate={{ scale: [1, 1.1, 1] }}
                    exit={{ scale: 0 }}
                    transition={{ scale: { duration: 1, repeat: Infinity } }}
                    className="flex items-center space-x-1 bg-red-600/30 border border-red-500/50 rounded-full px-2 py-1"
                  >
                    <div className="w-1 h-1 bg-red-500 rounded-full" />
                    <span className="text-red-400 text-[8px] font-bold">REC</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </motion.div>

        {/* Compact Script Editor */}
        <div className="bg-[#181818]/95 backdrop-blur-sm border-x border-[#303030]/50 p-2 flex-1 flex flex-col min-h-0">
          
          {/* Compact Section Tabs */}
          <div className="flex space-x-1 mb-2 bg-[#0f0f0f]/80 rounded-lg p-1">
            {scriptSections.map((section, index) => (
              <motion.button
                key={index}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setActiveSection(index)}
                className={`flex-1 py-1 px-2 rounded-md text-[8px] font-semibold transition-all duration-300 relative ${
                  activeSection === index 
                    ? 'text-white' 
                    : 'text-[#aaa] hover:text-white'
                }`}
                style={{
                  background: activeSection === index 
                    ? `linear-gradient(135deg, ${section.color}60, ${section.color}30)`
                    : 'transparent'
                }}
              >
                {activeSection === index && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute inset-0 rounded-md"
                    style={{ background: `linear-gradient(135deg, ${section.color}40, ${section.color}20)` }}
                    transition={{ type: "spring", damping: 25, stiffness: 300 }}
                  />
                )}
                <motion.span 
                  animate={{ scale: activeSection === index ? [1, 1.1, 1] : 1 }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="text-[10px] mr-1 relative z-10"
                >
                  {section.emoji}
                </motion.span>
                <span className="relative z-10">{section.title}</span>
              </motion.button>
            ))}
          </div>

          {/* Current Section Info */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <motion.div 
                animate={{ 
                  scale: [1, 1.2, 1],
                  boxShadow: [`0 0 0px ${scriptSections[activeSection].color}`, `0 0 8px ${scriptSections[activeSection].color}40`, `0 0 0px ${scriptSections[activeSection].color}`]
                }}
                transition={{ duration: 3, repeat: Infinity }}
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: scriptSections[activeSection].color }}
              />
              <span className="text-white font-semibold text-[10px]">
                {scriptSections[activeSection].title}
              </span>
              <span className="text-[#aaa] text-[8px]">
                {scriptSections[activeSection].timestamp}
              </span>
            </div>
            <div className="flex items-center space-x-2 text-[8px]">
              <span className="text-[#aaa]">Scene {sceneNumber}</span>
              <div className="w-8 bg-[#303030]/50 rounded-full h-1">
                <motion.div 
                  className="h-1 rounded-full"
                  style={{ 
                    width: `${scriptProgress}%`,
                    backgroundColor: scriptSections[activeSection].color
                  }}
                  transition={{ duration: 0.3 }}
                />
              </div>
              <span className="text-[#aaa]">{Math.round(scriptProgress)}%</span>
            </div>
          </div>

          {/* Compact Script Content */}
          <motion.div 
            key={activeSection}
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            className="bg-[#0f0f0f]/80 border rounded-lg p-2 flex-1 relative overflow-hidden"
            style={{ borderColor: scriptSections[activeSection].color + '40' }}
          >
            <div className="font-mono text-[8px] text-white leading-relaxed whitespace-pre-wrap overflow-hidden">
              {currentScript.substring(0, 120)}...
              <motion.span 
                animate={{ opacity: showCursor ? 1 : 0 }}
                style={{ color: scriptSections[activeSection].color }}
                className="font-bold"
              >
                |
              </motion.span>
            </div>

            {/* Compact AI indicator */}
            <div className="absolute top-1 right-1">
              <motion.div
                animate={{ 
                  scale: [1, 1.05, 1],
                }}
                transition={{ duration: 2, repeat: Infinity }}
                className="border rounded-full px-2 py-1 backdrop-blur-sm"
                style={{ 
                  backgroundColor: scriptSections[activeSection].color + '20',
                  borderColor: scriptSections[activeSection].color + '40'
                }}
              >
                <span 
                  className="text-[8px] font-bold"
                  style={{ color: scriptSections[activeSection].color }}
                >
                  🧠 AI
                </span>
              </motion.div>
            </div>
          </motion.div>
        </div>

        {/* Compact Analytics Footer */}
        <motion.div 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-[#1a1a1a]/90 backdrop-blur-sm border border-[#303030]/50 rounded-b-lg p-2 flex-shrink-0"
        >
          <div className="grid grid-cols-4 gap-2 text-center">
            <motion.div whileHover={{ scale: 1.05 }} className="cursor-pointer">
              <motion.div 
                animate={{ color: ['#ffffff', scriptSections[activeSection].color, '#ffffff'] }}
                transition={{ duration: 4, repeat: Infinity }}
                className="text-xs font-bold"
              >
                {videoMetrics.views > 999999 ? `${(videoMetrics.views/1000000).toFixed(1)}M` : `${Math.floor(videoMetrics.views/1000)}K`}
              </motion.div>
              <div className="text-[#aaa] text-[8px]">👁️ Views</div>
            </motion.div>
            
            <motion.div whileHover={{ scale: 1.05 }} className="cursor-pointer">
              <motion.div 
                animate={{ color: ["#fff", "#ff0000", "#fff"] }}
                transition={{ duration: 3, repeat: Infinity }}
                className="text-xs font-bold"
              >
                {Math.floor(videoMetrics.likes/1000)}K
              </motion.div>
              <div className="text-[#aaa] text-[8px]">👍 Likes</div>
            </motion.div>
            
            <motion.div whileHover={{ scale: 1.05 }} className="cursor-pointer">
              <motion.div 
                animate={{ color: ['#10b981', '#ffffff', '#10b981'] }}
                transition={{ duration: 5, repeat: Infinity }}
                className="text-xs font-bold"
              >
                {videoMetrics.retention.toFixed(1)}%
              </motion.div>
              <div className="text-[#aaa] text-[8px]">📊 Retention</div>
            </motion.div>
            
            <motion.div whileHover={{ scale: 1.05 }} className="cursor-pointer">
              <motion.div 
                animate={{ color: ["#fff", "#ffd700", "#fff"] }}
                transition={{ duration: 3.5, repeat: Infinity }}
                className="text-xs font-bold"
              >
                {videoMetrics.engagement.toFixed(1)}%
              </motion.div>
              <div className="text-[#aaa] text-[8px]">💬 Engagement</div>
            </motion.div>
          </div>
        </motion.div>

        {/* Compact Corner Badge */}
        <motion.div
          animate={{ 
            scale: [1, 1.05, 1]
          }}
          transition={{ duration: 4, repeat: Infinity }}
          className="absolute -top-1 -right-1 bg-gradient-to-r from-[#ff0000] to-[#ff6b6b] text-white px-2 py-1 rounded-full text-[8px] font-bold shadow-lg"
        >
          <motion.span
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
            className="inline-block mr-1"
          >
            🔥
          </motion.span>
          VIRAL
        </motion.div>
      </div>
    </div>
  )
}

export default function VideoScriptPreview() {
  return (
    <div className="w-full h-full bg-[#0f0f0f] rounded-lg overflow-hidden">
      <VideoScriptStudio />
    </div>
  )
} 