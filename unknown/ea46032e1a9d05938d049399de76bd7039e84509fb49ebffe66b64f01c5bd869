#!/usr/bin/env node

/**
 * Test YouTube Caption Extraction Methods
 * This script tests the youtube-transcript library and custom extraction methods
 */

import { YoutubeTranscript } from 'youtube-transcript';
import axios from 'axios';

console.log('🎬 Testing YouTube Caption Extraction Methods');
console.log('==============================================');

// Test video IDs that are known to have captions
const testVideos = [
  { id: 'dQw4w9WgXcQ', title: '<PERSON> - Never Gonna Give You Up' },
  { id: 'jNQXAC9IVRw', title: 'Me at the zoo (First YouTube Video)' },
  { id: 'kJQP7kiw5Fk', title: '<PERSON> ft. Daddy Yankee' }
];

// Test 1: Basic youtube-transcript functionality
async function testYouTubeTranscript() {
  console.log('\n📝 Test 1: Testing youtube-transcript library...');
  
  for (const video of testVideos) {
    console.log(`\n🎥 Testing: ${video.title} (${video.id})`);
    
    try {
      // Try to fetch transcript
      const transcript = await YoutubeTranscript.fetchTranscript(video.id, {
        lang: 'en'
      });
      
      if (transcript && transcript.length > 0) {
        console.log(`✅ Success! Extracted ${transcript.length} caption segments`);
        
        // Show first few segments
        console.log('📋 First 3 segments:');
        transcript.slice(0, 3).forEach((segment, index) => {
          const minutes = Math.floor(segment.offset / 60);
          const seconds = Math.floor(segment.offset % 60);
          const timestamp = `${minutes}:${seconds.toString().padStart(2, '0')}`;
          console.log(`   ${index + 1}. [${timestamp}] ${segment.text}`);
        });
        
        // Create full transcript
        const fullText = transcript.map(t => t.text).join(' ');
        console.log(`📄 Full transcript: ${fullText.length} characters`);
        console.log(`📄 Preview: ${fullText.substring(0, 150)}...`);
        
      } else {
        console.log('⚠️ No captions found');
      }
    } catch (error) {
      console.log(`❌ Failed: ${error.message}`);
    }
  }
}

// Test 2: Custom extraction method
async function testCustomExtraction(videoId) {
  console.log(`\n🔧 Test 2: Testing custom extraction for ${videoId}...`);
  
  try {
    // Fetch the YouTube video page
    const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
    const response = await axios.get(videoUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    const html = response.data;
    
    // Extract ytInitialPlayerResponse
    const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?})\s*;\s*(?:var\s+(?:meta|head)|<\/script|\n)/);
    
    if (!playerResponseMatch) {
      console.log('❌ Could not find ytInitialPlayerResponse');
      return;
    }

    const playerResponse = JSON.parse(playerResponseMatch[1]);
    
    // Check for captions
    if (!playerResponse.captions?.playerCaptionsTracklistRenderer?.captionTracks) {
      console.log('⚠️ No captions available via custom method');
      return;
    }

    const tracks = playerResponse.captions.playerCaptionsTracklistRenderer.captionTracks;
    console.log(`✅ Found ${tracks.length} caption tracks:`);
    
    tracks.forEach((track, index) => {
      console.log(`   ${index + 1}. Language: ${track.languageCode}, Kind: ${track.kind || 'manual'}`);
    });

    // Try to get the English track
    const englishTrack = tracks.find(track => track.languageCode === 'en') || tracks[0];
    
    if (englishTrack?.baseUrl) {
      console.log('\n🌍 Fetching caption data...');
      const captionResponse = await axios.get(englishTrack.baseUrl + '&fmt=json3');
      const captionData = captionResponse.data;

      if (captionData.events) {
        const captions = [];
        
        for (const event of captionData.events) {
          if (event.segs) {
            const text = event.segs
              .map(seg => seg.utf8)
              .join(' ')
              .replace(/[\u200B-\u200D\uFEFF]/g, '')
              .replace(/\s+/g, ' ')
              .trim();

            if (text) {
              captions.push({
                text,
                start: parseFloat(event.tStartMs) / 1000 || 0,
                duration: parseFloat(event.dDurationMs) / 1000 || 0
              });
            }
          }
        }

        console.log(`✅ Custom extraction successful! Got ${captions.length} segments`);
        
        // Show first few
        console.log('📋 First 3 segments:');
        captions.slice(0, 3).forEach((caption, index) => {
          const minutes = Math.floor(caption.start / 60);
          const seconds = Math.floor(caption.start % 60);
          const timestamp = `${minutes}:${seconds.toString().padStart(2, '0')}`;
          console.log(`   ${index + 1}. [${timestamp}] ${caption.text}`);
        });
      }
    }
  } catch (error) {
    console.log(`❌ Custom extraction failed: ${error.message}`);
  }
}

// Test 3: Compare methods
async function compareExtractionMethods(videoId) {
  console.log(`\n⚖️ Test 3: Comparing extraction methods for ${videoId}...`);
  
  console.log('\n📊 Method 1: youtube-transcript library');
  try {
    const libraryResult = await YoutubeTranscript.fetchTranscript(videoId);
    console.log(`✅ Library: ${libraryResult.length} segments`);
    if (libraryResult.length > 0) {
      console.log(`📄 Sample: ${libraryResult[0].text}`);
    }
  } catch (error) {
    console.log(`❌ Library failed: ${error.message}`);
  }

  console.log('\n📊 Method 2: Custom extraction');
  await testCustomExtraction(videoId);
}

// Test 4: Error handling and fallbacks
async function testErrorHandling() {
  console.log('\n🛠️ Test 4: Testing error handling...');
  
  // Test with non-existent video
  console.log('\n🚫 Testing with non-existent video ID...');
  try {
    const result = await YoutubeTranscript.fetchTranscript('invalidVideoId123');
    console.log('⚠️ Unexpected success with invalid ID');
  } catch (error) {
    console.log(`✅ Properly handled error: ${error.message}`);
  }
  
  // Test with private video (this might work or fail depending on the video)
  console.log('\n🔒 Testing with potentially private/unavailable video...');
  try {
    const result = await YoutubeTranscript.fetchTranscript('xxxxxxxxxx');
    console.log('⚠️ Unexpected success with private video');
  } catch (error) {
    console.log(`✅ Properly handled private video: ${error.message}`);
  }
}

// YouTube API test using the provided key
async function testYouTubeAPI() {
  console.log('\n🔑 Test 5: Testing YouTube API with provided key...');
  
  const apiKey = 'AIzaSyCHK5v6aCrIXQAakOtAbJjPA1MEpDWEEMo';
  
  try {
    // Test video search
    const searchUrl = 'https://www.googleapis.com/youtube/v3/search';
    const searchResponse = await axios.get(searchUrl, {
      params: {
        key: apiKey,
        q: 'JavaScript tutorial',
        part: 'snippet',
        type: 'video',
        maxResults: 3
      }
    });

    console.log(`✅ API Search successful! Found ${searchResponse.data.items.length} videos`);
    
    searchResponse.data.items.forEach((item, index) => {
      console.log(`   ${index + 1}. ${item.snippet.title}`);
      console.log(`      Channel: ${item.snippet.channelTitle}`);
      console.log(`      Video ID: ${item.id.videoId}`);
    });

    // Test video details
    if (searchResponse.data.items.length > 0) {
      const videoId = searchResponse.data.items[0].id.videoId;
      const detailsUrl = 'https://www.googleapis.com/youtube/v3/videos';
      const detailsResponse = await axios.get(detailsUrl, {
        params: {
          key: apiKey,
          id: videoId,
          part: 'snippet,statistics,contentDetails'
        }
      });

      if (detailsResponse.data.items.length > 0) {
        const video = detailsResponse.data.items[0];
        console.log(`\n📊 Video Details for: ${video.snippet.title}`);
        console.log(`   Views: ${parseInt(video.statistics.viewCount).toLocaleString()}`);
        console.log(`   Likes: ${video.statistics.likeCount ? parseInt(video.statistics.likeCount).toLocaleString() : 'N/A'}`);
        console.log(`   Duration: ${video.contentDetails.duration}`);
      }
    }

  } catch (error) {
    if (error.response?.status === 403) {
      console.log('❌ API key may be invalid or quota exceeded');
    } else {
      console.log(`❌ API test failed: ${error.message}`);
    }
  }
}

// Run all tests
async function runAllTests() {
  try {
    await testYouTubeTranscript();
    await compareExtractionMethods('dQw4w9WgXcQ');
    await testErrorHandling();
    await testYouTubeAPI();
    
    console.log('\n🎉 All tests completed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ youtube-transcript library functionality verified');
    console.log('   ✅ Custom extraction method tested');
    console.log('   ✅ Method comparison completed');
    console.log('   ✅ Error handling validated');
    console.log('   ✅ YouTube API key tested');
    console.log('\n🚀 Ready to use enhanced caption extraction!');
    
  } catch (error) {
    console.error('💥 Test suite failed:', error);
  }
}

// Start testing
runAllTests(); 