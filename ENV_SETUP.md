# Environment Setup Guide

## Overview
The project now uses a single consolidated environment configuration system.

## Files Structure

### `.env` (Template File)
- Contains all environment variable definitions with placeholder values
- Includes comprehensive documentation for each variable
- Safe to commit to version control
- Use this as a reference for all available configuration options

### `.env.local` (Your Local Configuration)
- Copy the structure from `.env` and add your actual API keys
- **Never commit this file** - it contains your sensitive information
- This file is automatically ignored by git

## Quick Setup

1. **Copy the template:**
   ```bash
   cp .env .env.local
   ```

2. **Add your API keys to `.env.local`:**
   - Replace all `your_*_key_here` placeholders with actual values
   - Only add the keys you actually have/need

3. **Required API Keys:**
   - `OPENROUTER_API_KEY` - For AI model access
   - `NEXT_PUBLIC_OPENROUTER_API_KEY` - Same as above, for client-side access

4. **Optional API Keys:**
   - `TAVILY_API_KEY` - For web research capabilities
   - `YOUTUBE_API_KEY` - For YouTube data access
   - `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET` - For OAuth
   - Others as needed

## Key Changes Made

1. **Consolidated Configuration**: All environment variables are now in a single `.env` file
2. **No Hardcoded Keys**: Removed all hardcoded API keys from source code
3. **Fallback Handling**: Applications gracefully handle missing API keys
4. **Security Improvements**: Clear separation between template and actual secrets

## Security Notes

- Never commit `.env.local` to version control
- Regularly rotate your API keys
- Use different keys for development and production
- Monitor API usage and costs

## Environment Variable Categories

- **Database**: PostgreSQL/SQLite configuration
- **Authentication**: OAuth and session management
- **AI Models**: OpenRouter, OpenAI, Gemini APIs
- **Search & Research**: Tavily, Google Search APIs
- **Video Services**: YouTube Data API
- **Application**: Runtime and performance settings