{"timestamp": "2025-07-21T05:38:31.169Z", "summary": {"totalTested": 9, "validKeys": 6, "quotaExceeded": 3, "invalidKeys": 0, "networkErrors": 0}, "validKeys": [{"key": "...0Qna", "full": "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"}, {"key": "...f9Vq", "full": "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"}, {"key": "...FHXW", "full": "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"}, {"key": "...ncYf", "full": "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"}, {"key": "...sSTM", "full": "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"}, {"key": "...JCgO", "full": "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"}], "quotaExceededKeys": [{"key": "...gSay", "full": "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"}, {"key": "...s3a5", "full": "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"}, {"key": "...hQYR", "full": "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"}], "invalidKeys": [], "networkErrorKeys": [], "detailedResults": [{"key": "Key 1 (...gSay)", "keyValue": "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", "status": 432, "success": false, "responseData": {"detail": {"error": "This request exceeds your plan's set usage limit. Please upgrade your plan <NAME_EMAIL>"}}, "timestamp": "2025-07-21T05:38:28.249Z", "category": "quota_exceeded"}, {"key": "Key 3 (...s3a5)", "keyValue": "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", "status": 432, "success": false, "responseData": {"detail": {"error": "This request exceeds your plan's set usage limit. Please upgrade your plan <NAME_EMAIL>"}}, "timestamp": "2025-07-21T05:38:28.255Z", "category": "quota_exceeded"}, {"key": "Key 5 (...hQYR)", "keyValue": "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", "status": 432, "success": false, "responseData": {"detail": {"error": "This request exceeds your plan's set usage limit. Please upgrade your plan <NAME_EMAIL>"}}, "timestamp": "2025-07-21T05:38:28.305Z", "category": "quota_exceeded"}, {"key": "Key 7 (...0Qna)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 0.86}, "timestamp": "2025-07-21T05:38:28.803Z", "category": "valid"}, {"key": "Key 2 (...f9Vq)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 0.79}, "timestamp": "2025-07-21T05:38:29.165Z", "category": "valid"}, {"key": "Key 4 (...FHXW)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 0.97}, "timestamp": "2025-07-21T05:38:29.297Z", "category": "valid"}, {"key": "Key 8 (...ncYf)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 1.01}, "timestamp": "2025-07-21T05:38:29.311Z", "category": "valid"}, {"key": "Key 9 (...sSTM)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 1.21}, "timestamp": "2025-07-21T05:38:29.565Z", "category": "valid"}, {"key": "Key 6 (...JCgO)", "keyValue": "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", "status": 200, "success": true, "responseData": {"query": "test search query", "follow_up_questions": null, "answer": null, "images": [], "results": [{"url": "https://katalon.com/resources-center/blog/test-cases-for-search-functionality", "title": "100+ Test Cases For Search Functionality You Should Know", "content": "- Important Test Cases For Search Functionality Negative Test Cases For Search Functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Performance Test Cases For Search Functionality ## Important Test Cases For Search Functionality Negative Test Cases For Search Functionality negative test cases for search functionality Query Input Test For Search Feature Test Cases For Search Results Evaluation Here are some test cases to help you evaluate the search result quality: 4. Test the display of product images for all search results. Test the display of related products or suggestions alongside search results. Test the behavior when a user clicks on a search result link. Performance Test Cases For Search Functionality Test the effectiveness of product recommendations integrated with search results.", "score": 0.49406826, "raw_content": null}], "response_time": 1.23}, "timestamp": "2025-07-21T05:38:29.649Z", "category": "valid"}], "recommendations": [{"type": "remove_keys", "priority": "high", "action": "Remove quota exceeded keys from TavilyApiKeyRotator", "keys": ["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", "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", "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"], "reason": "These keys have reached their quota limit and will continue to fail"}]}